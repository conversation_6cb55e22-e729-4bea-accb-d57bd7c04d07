@extends('layouts.port-app')

@section('page-title', 'تقرير استغلال المستودعات')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('warehouses.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى المستودعات
    </a>
    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
        <i class="fas fa-print me-2"></i>
        طباعة
    </button>
</div>
@endsection

@section('content')
<!-- ملخص عام -->
<div class="row mb-4">
    @php
        $totalWarehouses = $warehouses->count();
        $activeWarehouses = $warehouses->where('status', 'active')->count();
        $totalArea = $warehouses->sum('total_area');
        $usedArea = $warehouses->sum(function($w) { return $w->total_area - $w->available_area; });
        $totalCapacity = $warehouses->sum('max_capacity');
        $currentCapacity = $warehouses->sum('current_capacity');
        $overallOccupancy = $totalArea > 0 ? ($usedArea / $totalArea) * 100 : 0;
        $capacityOccupancy = $totalCapacity > 0 ? ($currentCapacity / $totalCapacity) * 100 : 0;
    @endphp

    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ $totalWarehouses }}</h3>
                <small>إجمالي المستودعات</small>
                <div class="mt-2">
                    <small>{{ $activeWarehouses }} نشط</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ number_format($totalArea, 0) }}</h3>
                <small>إجمالي المساحة (م²)</small>
                <div class="mt-2">
                    <small>{{ number_format($usedArea, 0) }} مستغل</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ number_format($overallOccupancy, 1) }}%</h3>
                <small>نسبة استغلال المساحة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ number_format($capacityOccupancy, 1) }}%</h3>
                <small>نسبة استغلال السعة</small>
                @if($totalCapacity > 0)
                <div class="mt-2">
                    <small>{{ number_format($currentCapacity, 0) }} / {{ number_format($totalCapacity, 0) }} طن</small>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- تفاصيل المستودعات -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            تفاصيل استغلال المستودعات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>المستودع</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>المساحة الإجمالية</th>
                        <th>المساحة المستغلة</th>
                        <th>نسبة استغلال المساحة</th>
                        <th>السعة القصوى</th>
                        <th>السعة الحالية</th>
                        <th>نسبة استغلال السعة</th>
                        <th>الحمولات المخزنة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($warehouses as $warehouse)
                    <tr>
                        <td>
                            <div class="fw-bold">{{ $warehouse->name }}</div>
                            <small class="text-muted">{{ $warehouse->code }}</small>
                            @if($warehouse->location)
                                <br><small class="text-muted">{{ $warehouse->location }}</small>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $warehouse->type_label }}</span>
                            @if($warehouse->is_covered)
                                <br><small class="text-success">مغطى</small>
                            @else
                                <br><small class="text-secondary">مكشوف</small>
                            @endif
                        </td>
                        <td>
                            @switch($warehouse->status)
                                @case('active')
                                    <span class="badge bg-success">{{ $warehouse->status_label }}</span>
                                    @break
                                @case('inactive')
                                    <span class="badge bg-secondary">{{ $warehouse->status_label }}</span>
                                    @break
                                @case('maintenance')
                                    <span class="badge bg-warning">{{ $warehouse->status_label }}</span>
                                    @break
                                @case('full')
                                    <span class="badge bg-danger">{{ $warehouse->status_label }}</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $warehouse->status_label }}</span>
                            @endswitch
                        </td>
                        <td>{{ number_format($warehouse->total_area, 0) }} م²</td>
                        <td>{{ number_format($warehouse->used_area, 0) }} م²</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="progress flex-grow-1 me-2" style="height: 20px; width: 80px;">
                                    <div class="progress-bar
                                        @if($warehouse->area_occupancy_percentage < 50) bg-success
                                        @elseif($warehouse->area_occupancy_percentage < 80) bg-warning
                                        @else bg-danger
                                        @endif"
                                         role="progressbar"
                                         style="width: {{ $warehouse->area_occupancy_percentage }}%">
                                    </div>
                                </div>
                                <span class="fw-bold">{{ number_format($warehouse->area_occupancy_percentage, 1) }}%</span>
                            </div>
                        </td>
                        <td>
                            @if($warehouse->max_capacity)
                                {{ number_format($warehouse->max_capacity, 0) }} طن
                            @else
                                <span class="text-muted">غير محدد</span>
                            @endif
                        </td>
                        <td>
                            @if($warehouse->max_capacity)
                                {{ number_format($warehouse->current_capacity, 0) }} طن
                            @else
                                <span class="text-muted">--</span>
                            @endif
                        </td>
                        <td>
                            @if($warehouse->max_capacity)
                                <div class="d-flex align-items-center">
                                    <div class="progress flex-grow-1 me-2" style="height: 20px; width: 80px;">
                                        <div class="progress-bar
                                            @if($warehouse->occupancy_percentage < 50) bg-success
                                            @elseif($warehouse->occupancy_percentage < 80) bg-warning
                                            @else bg-danger
                                            @endif"
                                             role="progressbar"
                                             style="width: {{ $warehouse->occupancy_percentage }}%">
                                        </div>
                                    </div>
                                    <span class="fw-bold">{{ number_format($warehouse->occupancy_percentage, 1) }}%</span>
                                </div>
                            @else
                                <span class="text-muted">--</span>
                            @endif
                        </td>
                        <td>
                            @php
                                $currentCargosCount = $warehouse->cargoStorages->where('storage_end', null)->count();
                            @endphp
                            @if($currentCargosCount > 0)
                                <span class="badge bg-primary">{{ $currentCargosCount }}</span>
                            @else
                                <span class="text-muted">لا توجد</span>
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- إحصائيات حسب النوع -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المستودعات حسب النوع
                </h6>
            </div>
            <div class="card-body">
                @php
                    $typeStats = $warehouses->groupBy('type')->map(function($group) {
                        return [
                            'count' => $group->count(),
                            'total_area' => $group->sum('total_area'),
                            'used_area' => $group->sum(function($w) { return $w->total_area - $w->available_area; })
                        ];
                    });
                @endphp

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>العدد</th>
                                <th>المساحة الإجمالية</th>
                                <th>المساحة المستغلة</th>
                                <th>نسبة الاستغلال</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($typeStats as $type => $stats)
                            <tr>
                                <td>
                                    @switch($type)
                                        @case('warehouse')
                                            مستودع
                                            @break
                                        @case('yard')
                                            ساحة
                                            @break
                                        @case('cold_storage')
                                            تبريد
                                            @break
                                        @case('tank')
                                            خزان
                                            @break
                                        @case('silo')
                                            صومعة
                                            @break
                                        @default
                                            {{ $type }}
                                    @endswitch
                                </td>
                                <td>{{ $stats['count'] }}</td>
                                <td>{{ number_format($stats['total_area'], 0) }} م²</td>
                                <td>{{ number_format($stats['used_area'], 0) }} م²</td>
                                <td>
                                    @php
                                        $occupancy = $stats['total_area'] > 0 ? ($stats['used_area'] / $stats['total_area']) * 100 : 0;
                                    @endphp
                                    {{ number_format($occupancy, 1) }}%
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    توزيع المستودعات حسب الحالة
                </h6>
            </div>
            <div class="card-body">
                @php
                    $totalWarehouses = $warehouses->count();
                    $statusStats = $warehouses->groupBy('status')->map(function($group) use ($totalWarehouses) {
                        return [
                            'count' => $group->count(),
                            'total_area' => $group->sum('total_area'),
                            'percentage' => $totalWarehouses > 0 ? ($group->count() / $totalWarehouses) * 100 : 0
                        ];
                    });
                @endphp

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                                <th>المساحة الإجمالية</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($statusStats as $status => $stats)
                            <tr>
                                <td>
                                    @switch($status)
                                        @case('active')
                                            <span class="badge bg-success">نشط</span>
                                            @break
                                        @case('inactive')
                                            <span class="badge bg-secondary">غير نشط</span>
                                            @break
                                        @case('maintenance')
                                            <span class="badge bg-warning">قيد الصيانة</span>
                                            @break
                                        @case('full')
                                            <span class="badge bg-danger">ممتلئ</span>
                                            @break
                                        @default
                                            {{ $status }}
                                    @endswitch
                                </td>
                                <td>{{ $stats['count'] }}</td>
                                <td>{{ number_format($stats['percentage'], 1) }}%</td>
                                <td>{{ number_format($stats['total_area'], 0) }} م²</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات التقرير -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات التقرير</h6>
                        <p class="mb-1"><strong>تاريخ التقرير:</strong> {{ now()->format('Y/m/d H:i') }}</p>
                        <p class="mb-1"><strong>إجمالي المستودعات:</strong> {{ $warehouses->count() }}</p>
                        <p class="mb-0"><strong>المستودعات النشطة:</strong> {{ $warehouses->where('status', 'active')->count() }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">ملاحظات</h6>
                        <ul class="mb-0">
                            <li>نسبة الاستغلال محسوبة بناءً على المساحة المستغلة من المساحة الإجمالية</li>
                            <li>السعة الحالية تُحدث تلقائياً بناءً على الحمولات المخزنة</li>
                            <li>المستودعات قيد الصيانة لا تحسب في الإحصائيات التشغيلية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
@media print {
    .btn-group, .navbar, .sidebar {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }

    .progress {
        border: 1px solid #dee2e6;
    }

    .badge {
        border: 1px solid #000;
    }
}

.progress {
    background-color: #e9ecef;
}
</style>
@endpush
