@extends('layouts.port-app')

@section('page-title', 'إضافة رصيف جديد')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('berths.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى قائمة الأرصفة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    بيانات الرصيف الجديد
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('berths.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم الرصيف <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="code" class="form-label">كود الرصيف <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                               id="code" name="code" value="{{ old('code') }}" required>
                                        <div class="form-text">مثال: B001, PIER-A, etc.</div>
                                        @error('code')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة الرصيف <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="available" {{ old('status') == 'available' ? 'selected' : '' }}>
                                        متاح
                                    </option>
                                    <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>
                                        قيد الصيانة
                                    </option>
                                    <option value="out_of_service" {{ old('status') == 'out_of_service' ? 'selected' : '' }}>
                                        خارج الخدمة
                                    </option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المواصفات التقنية -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-ruler me-2"></i>
                                المواصفات التقنية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="length" class="form-label">الطول (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('length') is-invalid @enderror" 
                                               id="length" name="length" value="{{ old('length') }}">
                                        @error('length')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="depth" class="form-label">العمق (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('depth') is-invalid @enderror" 
                                               id="depth" name="depth" value="{{ old('depth') }}">
                                        @error('depth')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="max_tonnage" class="form-label">الحمولة القصوى (طن)</label>
                                        <input type="number" class="form-control @error('max_tonnage') is-invalid @enderror" 
                                               id="max_tonnage" name="max_tonnage" value="{{ old('max_tonnage') }}">
                                        @error('max_tonnage')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الوصف -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-file-alt me-2"></i>
                                معلومات إضافية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف الرصيف</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4">{{ old('description') }}</textarea>
                                <div class="form-text">وصف مختصر عن الرصيف ومميزاته والخدمات المتاحة</div>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('berths.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الرصيف
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// تحديث كود الرصيف تلقائياً بناءً على الاسم
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const codeField = document.getElementById('code');
    
    if (name && !codeField.value) {
        // تحويل الاسم إلى كود مقترح
        let suggestedCode = name
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]/g, '')
            .toUpperCase()
            .substring(0, 10);
        
        codeField.value = suggestedCode;
    }
});
</script>
@endpush
