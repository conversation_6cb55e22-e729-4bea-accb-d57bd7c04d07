<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained('invoices')->onDelete('cascade'); // الفاتورة
            $table->date('payment_date'); // تاريخ الدفع
            $table->decimal('amount', 10, 2); // المبلغ المدفوع
            $table->enum('payment_method', ['cash', 'bank_transfer', 'check', 'credit_card']); // طريقة الدفع
            $table->string('reference_number')->nullable(); // رقم المرجع
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
