@extends('layouts.port-app')

@section('page-title', 'تقرير حركة السفن اليومي')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('reports.daily-ship-movement', array_merge(request()->all(), ['format' => 'pdf'])) }}" class="btn btn-danger">
        <i class="fas fa-file-pdf me-2"></i>
        تحميل PDF
    </a>
    <button onclick="window.print()" class="btn btn-secondary">
        <i class="fas fa-print me-2"></i>
        طباعة
    </button>
</div>
@endsection

@section('content')
<div class="report-container">
    <!-- رأس التقرير -->
    <div class="text-center mb-4 border-bottom pb-3">
        <h2 class="text-primary">الجمهورية العربية السورية</h2>
        <h3>وزارة النقل - المؤسسة العامة للموانئ</h3>
        <h4>مرفأ اللاذقية</h4>
        <h5 class="mt-3">تقرير حركة السفن اليومي</h5>
        <p class="text-muted">التاريخ: {{ $report_date->format('Y/m/d') }} - {{ $report_date->locale('ar')->translatedFormat('l') }}</p>
    </div>

    <!-- إعلانات الوصول -->
    <div class="section mb-4">
        <h5 class="bg-primary text-white p-2 rounded">
            <i class="fas fa-bullhorn me-2"></i>
            إعلانات الوصول
        </h5>

        @if($arrival_announcements && $arrival_announcements->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم السفينة</th>
                            <th>الوكيل الملاحي</th>
                            <th>نوع الحمولة</th>
                            <th>الكمية</th>
                            <th>تاريخ الوصول المتوقع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($arrival_announcements as $ship)
                        <tr>
                            <td>{{ $ship->name }}</td>
                            <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->pluck('cargo_type')->join(', ') }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->sum('quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>{{ $ship->expected_arrival_date ? $ship->expected_arrival_date->format('Y/m/d H:i') : 'غير محدد' }}</td>
                            <td>{{ $ship->notes ?? '-' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد إعلانات وصول لهذا التاريخ
            </div>
        @endif
    </div>

    <!-- السفن الموجودة خارج الحوض -->
    <div class="section mb-4">
        <h5 class="bg-warning text-dark p-2 rounded">
            <i class="fas fa-ship me-2"></i>
            السفن الموجودة خارج الحوض
        </h5>

        @if($ships_outside_port && $ships_outside_port->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم السفينة</th>
                            <th>تاريخ الوصول</th>
                            <th>نوع الحمولة</th>
                            <th>الكمية</th>
                            <th>الوكيل الملاحي</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($ships_outside_port as $ship)
                        <tr>
                            <td>{{ $ship->name }}</td>
                            <td>{{ $ship->actual_arrival_date ? $ship->actual_arrival_date->format('Y/m/d') : 'غير محدد' }}</td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->pluck('cargo_type')->join(', ') }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->sum('quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                            <td>{{ $ship->notes ?? '-' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد سفن خارج الحوض
            </div>
        @endif
    </div>

    <!-- السفن الموجودة على الأرصفة والمكسر -->
    <div class="section mb-4">
        <h5 class="bg-success text-white p-2 rounded">
            <i class="fas fa-warehouse me-2"></i>
            السفن الموجودة على الأرصفة والمكسر
        </h5>

        @if($ships_on_berths && $ships_on_berths->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم السفينة</th>
                            <th>تاريخ الرسو</th>
                            <th>الرصيف</th>
                            <th>نوع الحمولة</th>
                            <th>الكمية</th>
                            <th>الوكيل الملاحي</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($ships_on_berths as $ship)
                        <tr>
                            <td>{{ $ship->name }}</td>
                            <td>{{ $ship->actual_arrival_date ? $ship->actual_arrival_date->format('Y/m/d') : 'غير محدد' }}</td>
                            <td>
                                @if($ship->currentBerth && $ship->currentBerth->berth)
                                    {{ $ship->currentBerth->berth->name }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->pluck('cargo_type')->join(', ') }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->sum('quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                            <td>{{ $ship->notes ?? '-' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد سفن مرسية على الأرصفة
            </div>
        @endif
    </div>

    <!-- السفن التي غادرت خلال 24 ساعة الأخيرة -->
    <div class="section mb-4">
        <h5 class="bg-danger text-white p-2 rounded">
            <i class="fas fa-sign-out-alt me-2"></i>
            السفن التي غادرت خلال 24 ساعة الأخيرة
        </h5>

        @if($departed_ships && $departed_ships->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>اسم السفينة</th>
                            <th>تاريخ المغادرة</th>
                            <th>الكمية المصدرة</th>
                            <th>الوكيل الملاحي</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($departed_ships as $ship)
                        <tr>
                            <td>{{ $ship->name }}</td>
                            <td>{{ $ship->departure_date ? $ship->departure_date->format('Y/m/d H:i') : 'غير محدد' }}</td>
                            <td>
                                @if($ship->cargos->count() > 0)
                                    {{ $ship->cargos->where('operation_type', 'loading')->sum('completed_quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                            <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                            <td>{{ $ship->notes ?? '-' }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد سفن غادرت خلال 24 ساعة الأخيرة
            </div>
        @endif
    </div>

    <!-- ملخص التقرير -->
    <div class="section">
        <h5 class="bg-secondary text-white p-2 rounded">
            <i class="fas fa-chart-bar me-2"></i>
            ملخص التقرير
        </h5>

        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">{{ $arrival_announcements ? $arrival_announcements->count() : 0 }}</h4>
                        <p class="card-text">إعلانات الوصول</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning">{{ $ships_outside_port ? $ships_outside_port->count() : 0 }}</h4>
                        <p class="card-text">سفن خارج الحوض</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success">{{ $ships_on_berths ? $ships_on_berths->count() : 0 }}</h4>
                        <p class="card-text">سفن مرسية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-danger">{{ $departed_ships ? $departed_ships->count() : 0 }}</h4>
                        <p class="card-text">سفن مغادرة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تذييل التقرير -->
    <div class="text-center mt-4 pt-3 border-top">
        <p class="text-muted">
            تم إنشاء هذا التقرير بتاريخ {{ now()->format('Y/m/d H:i') }}
            <br>
            نظام إدارة المرفأ البحري - الإصدار 1.0
        </p>
    </div>
</div>

<style>
@media print {
    .btn, .navbar, .sidebar {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
    }

    .report-container {
        font-size: 12px;
    }

    .table {
        font-size: 11px;
    }

    .section {
        page-break-inside: avoid;
    }
}
</style>
@endsection
