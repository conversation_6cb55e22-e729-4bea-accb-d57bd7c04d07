@extends('layouts.port-app')

@section('page-title', 'إدارة السفن')

@section('page-actions')
<div class="btn-group">
    @can('ships.create')
    <a href="{{ route('ships.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة سفينة جديدة
    </a>
    @endcan
</div>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-md-12">
        <!-- فلاتر البحث -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر البحث
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('ships.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث في اسم السفينة</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="اسم السفينة...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="announced" {{ request('status') == 'announced' ? 'selected' : '' }}>
                                    إعلان وصول
                                </option>
                                <option value="outside_port" {{ request('status') == 'outside_port' ? 'selected' : '' }}>
                                    خارج الحوض
                                </option>
                                <option value="berthed" {{ request('status') == 'berthed' ? 'selected' : '' }}>
                                    مرسية
                                </option>
                                <option value="departed" {{ request('status') == 'departed' ? 'selected' : '' }}>
                                    غادرت
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="agent_id" class="form-label">الوكيل الملاحي</label>
                            <select class="form-select" id="agent_id" name="agent_id">
                                <option value="">جميع الوكلاء</option>
                                @foreach($agents as $agent)
                                    <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                        {{ $agent->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="{{ route('ships.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ship me-2"></i>
                    قائمة السفن ({{ $ships->total() }} سفينة)
                </h5>
            </div>
            <div class="card-body">
                @if($ships->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم السفينة</th>
                                    <th>رقم IMO</th>
                                    <th>الجنسية</th>
                                    <th>نوع السفينة</th>
                                    <th>الوكيل الملاحي</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الوصول المتوقع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($ships as $ship)
                                <tr>
                                    <td>
                                        <strong>{{ $ship->name }}</strong>
                                        @if($ship->owner_company)
                                            <br><small class="text-muted">{{ $ship->owner_company }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $ship->imo_number ?? 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $ship->flag }}</span>
                                    </td>
                                    <td>{{ $ship->ship_type }}</td>
                                    <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                                    <td>
                                        @switch($ship->status)
                                            @case('announced')
                                                <span class="badge bg-primary">إعلان وصول</span>
                                                @break
                                            @case('outside_port')
                                                <span class="badge bg-warning">خارج الحوض</span>
                                                @break
                                            @case('berthed')
                                                <span class="badge bg-success">مرسية</span>
                                                @break
                                            @case('departed')
                                                <span class="badge bg-secondary">غادرت</span>
                                                @break
                                            @default
                                                <span class="badge bg-light text-dark">غير محدد</span>
                                        @endswitch
                                    </td>
                                    <td>
                                        @if($ship->expected_arrival_date)
                                            {{ $ship->expected_arrival_date->format('Y/m/d H:i') }}
                                        @else
                                            غير محدد
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @can('ships.view')
                                            <a href="{{ route('ships.show', $ship) }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('ships.edit')
                                            <a href="{{ route('ships.edit', $ship) }}" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('ships.delete')
                                            <button type="button" class="btn btn-outline-danger" title="حذف"
                                                    onclick="confirmDelete('{{ $ship->id }}', '{{ $ship->name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $ships->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-ship fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد سفن</h5>
                        <p class="text-muted">لم يتم العثور على أي سفن تطابق معايير البحث</p>
                        @can('ships.create')
                        <a href="{{ route('ships.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة سفينة جديدة
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف السفينة <strong id="shipName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم حذف جميع البيانات المرتبطة بهذه السفينة نهائياً
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(shipId, shipName) {
    document.getElementById('shipName').textContent = shipName;
    document.getElementById('deleteForm').action = '/ships/' + shipId;
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
