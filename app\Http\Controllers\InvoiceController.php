<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\Ship;
use App\Models\Agent;
use App\Models\Payment;
use App\Models\ShipBerthHistory;
use App\Models\CargoStorage;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Exports\InvoicesExport;
use Maatwebsite\Excel\Facades\Excel;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Gate::authorize('invoices.view');

        $query = Invoice::with(['ship', 'agent']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الوكيل
        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        // بحث في رقم الفاتورة أو اسم السفينة
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('invoice_number', 'like', '%' . $request->search . '%')
                  ->orWhereHas('ship', function($shipQuery) use ($request) {
                      $shipQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $invoices = $query->orderBy('invoice_date', 'desc')->paginate(15);

        // حساب الإحصائيات المحسنة
        $stats = [
            'total' => Invoice::count(),
            'draft' => Invoice::where('status', 'draft')->count(),
            'sent' => Invoice::where('status', 'sent')->count(),
            'paid' => Invoice::where('status', 'paid')->count(),
            'overdue' => Invoice::where('status', 'overdue')->count(),
            'cancelled' => Invoice::where('status', 'cancelled')->count(),
            'partial' => Invoice::where('status', 'partial')->count(),
            'total_amount' => Invoice::sum('total_amount') ?? 0,
            'paid_amount' => Invoice::sum('paid_amount') ?? 0,
            'remaining_amount' => (Invoice::sum('total_amount') ?? 0) - (Invoice::sum('paid_amount') ?? 0),
            'this_month_amount' => Invoice::whereMonth('invoice_date', now()->month)
                ->whereYear('invoice_date', now()->year)
                ->sum('total_amount') ?? 0,
            'this_month_paid' => Invoice::whereMonth('invoice_date', now()->month)
                ->whereYear('invoice_date', now()->year)
                ->sum('paid_amount') ?? 0,
            'average_invoice' => Invoice::count() > 0 ? (Invoice::sum('total_amount') ?? 0) / Invoice::count() : 0
        ];

        $agents = Agent::where('is_active', true)->get();

        return view('invoices.index', compact('invoices', 'stats', 'agents'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        Gate::authorize('invoices.create');

        $ships = Ship::with('agent')->get();
        $agents = Agent::where('is_active', true)->get();

        // إذا تم تحديد سفينة معينة
        $selectedShip = null;
        if ($request->filled('ship_id')) {
            $selectedShip = Ship::with(['agent', 'berthHistory', 'cargos.storages'])
                ->find($request->ship_id);
        }

        return view('invoices.create', compact('ships', 'agents', 'selectedShip'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Gate::authorize('invoices.create');

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id',
            'agent_id' => 'required|exists:agents,id',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after:invoice_date',
            'berthing_fees' => 'nullable|numeric|min:0',
            'cargo_handling_fees' => 'nullable|numeric|min:0',
            'storage_fees' => 'nullable|numeric|min:0',
            'pilotage_fees' => 'nullable|numeric|min:0',
            'other_fees' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        // توليد رقم الفاتورة
        $validated['invoice_number'] = Invoice::generateInvoiceNumber();

        // حساب الإجمالي
        $validated['total_amount'] =
            ($validated['berthing_fees'] ?? 0) +
            ($validated['cargo_handling_fees'] ?? 0) +
            ($validated['storage_fees'] ?? 0) +
            ($validated['pilotage_fees'] ?? 0) +
            ($validated['other_fees'] ?? 0);

        $invoice = Invoice::create($validated);

        return redirect()->route('invoices.show', $invoice)
            ->with('success', 'تم إنشاء الفاتورة بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Invoice $invoice)
    {
        Gate::authorize('invoices.view');

        $invoice->load(['ship.agent', 'agent', 'payments']);

        return view('invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        // لا يمكن تعديل الفواتير المدفوعة أو الملغاة
        if (in_array($invoice->status, ['paid', 'cancelled'])) {
            return back()->with('error', 'لا يمكن تعديل هذه الفاتورة');
        }

        $ships = Ship::with('agent')->get();
        $agents = Agent::where('is_active', true)->get();

        return view('invoices.edit', compact('invoice', 'ships', 'agents'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        // لا يمكن تعديل الفواتير المدفوعة أو الملغاة
        if (in_array($invoice->status, ['paid', 'cancelled'])) {
            return back()->with('error', 'لا يمكن تعديل هذه الفاتورة');
        }

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id',
            'agent_id' => 'required|exists:agents,id',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after:invoice_date',
            'berthing_fees' => 'nullable|numeric|min:0',
            'cargo_handling_fees' => 'nullable|numeric|min:0',
            'storage_fees' => 'nullable|numeric|min:0',
            'pilotage_fees' => 'nullable|numeric|min:0',
            'other_fees' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        // حساب الإجمالي
        $validated['total_amount'] =
            ($validated['berthing_fees'] ?? 0) +
            ($validated['cargo_handling_fees'] ?? 0) +
            ($validated['storage_fees'] ?? 0) +
            ($validated['pilotage_fees'] ?? 0) +
            ($validated['other_fees'] ?? 0);

        $invoice->update($validated);

        return redirect()->route('invoices.show', $invoice)
            ->with('success', 'تم تحديث الفاتورة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Invoice $invoice)
    {
        Gate::authorize('invoices.delete');

        // لا يمكن حذف الفواتير المدفوعة
        if ($invoice->status === 'paid') {
            return back()->with('error', 'لا يمكن حذف الفواتير المدفوعة');
        }

        $invoice->delete();

        return redirect()->route('invoices.index')
            ->with('success', 'تم حذف الفاتورة بنجاح');
    }

    /**
     * حساب الرسوم تلقائياً بناءً على بيانات السفينة
     */
    public function calculateFees(Request $request)
    {
        $ship = Ship::with(['berthHistory', 'cargos.storages'])->find($request->ship_id);

        if (!$ship) {
            return response()->json(['error' => 'السفينة غير موجودة'], 404);
        }

        $fees = [
            'berthing_fees' => 0,
            'cargo_handling_fees' => 0,
            'storage_fees' => 0,
            'pilotage_fees' => 100, // رسوم إرشاد ثابتة
            'other_fees' => 0
        ];

        // حساب رسوم الرسو
        $activeBerthing = $ship->berthHistory()->where('status', 'completed')->get();
        foreach ($activeBerthing as $berth) {
            $fees['berthing_fees'] += $berth->total_berthing_cost ?? 0;
        }

        // حساب رسوم مناولة البضائع
        $totalCargo = $ship->cargos()->sum('quantity');
        $fees['cargo_handling_fees'] = $totalCargo * 5; // 5 ليرة لكل طن

        // حساب رسوم التخزين
        foreach ($ship->cargos as $cargo) {
            foreach ($cargo->storages as $storage) {
                if ($storage->storage_end) {
                    $days = Carbon::parse($storage->storage_start)
                        ->diffInDays(Carbon::parse($storage->storage_end));
                    $fees['storage_fees'] += $days * $storage->daily_rate;
                }
            }
        }

        return response()->json($fees);
    }

    /**
     * تغيير حالة الفاتورة
     */
    public function updateStatus(Request $request, Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        $validated = $request->validate([
            'status' => 'required|in:draft,sent,paid,overdue,cancelled'
        ]);

        $invoice->update($validated);

        return back()->with('success', 'تم تحديث حالة الفاتورة بنجاح');
    }

    /**
     * إضافة دفعة
     */
    public function addPayment(Request $request, Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        $validated = $request->validate([
            'payment_date' => 'required|date',
            'amount' => 'required|numeric|min:0.01|max:' . $invoice->remaining_amount,
            'payment_method' => 'required|in:cash,bank_transfer,check,credit_card',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string'
        ]);

        $payment = $invoice->payments()->create($validated);

        // تحديث المبلغ المدفوع
        $invoice->increment('paid_amount', $validated['amount']);

        // تحديث حالة الفاتورة
        if ($invoice->is_fully_paid) {
            $invoice->update(['status' => 'paid']);
        }

        return back()->with('success', 'تم إضافة الدفعة بنجاح');
    }

    /**
     * طباعة الفاتورة
     */
    public function print(Invoice $invoice)
    {
        Gate::authorize('invoices.view');

        $invoice->load(['ship.agent', 'agent']);

        $pdf = Pdf::loadView('invoices.print', compact('invoice'));

        return $pdf->download('invoice-' . $invoice->invoice_number . '.pdf');
    }

    /**
     * تقرير الفواتير
     */
    public function report(Request $request)
    {
        Gate::authorize('invoices.view');

        // تحديد الفترة الزمنية (افتراضياً آخر 30 يوم)
        $dateFrom = $request->date_from ? Carbon::parse($request->date_from) : Carbon::now()->subDays(30);
        $dateTo = $request->date_to ? Carbon::parse($request->date_to)->endOfDay() : Carbon::now()->endOfDay();

        // بناء استعلام الفواتير
        $query = Invoice::with(['ship', 'agent'])
            ->whereBetween('invoice_date', [$dateFrom, $dateTo]);

        // تطبيق الفلاتر
        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->agent_id) {
            $query->where('agent_id', $request->agent_id);
        }

        $invoices = $query->orderBy('invoice_date', 'desc')->get();

        // حساب الإحصائيات
        $summary = [
            'total_invoices' => $invoices->count(),
            'total_amount' => $invoices->sum('total_amount'),
            'paid_amount' => $invoices->sum('paid_amount'),
            'remaining_amount' => $invoices->sum('total_amount') - $invoices->sum('paid_amount'),
            'by_status' => [
                'draft' => $invoices->where('status', 'draft')->count(),
                'sent' => $invoices->where('status', 'sent')->count(),
                'paid' => $invoices->where('status', 'paid')->count(),
                'overdue' => $invoices->where('status', 'overdue')->count(),
                'cancelled' => $invoices->where('status', 'cancelled')->count(),
            ]
        ];

        $agents = Agent::where('is_active', true)->get();

        // تصدير حسب التنسيق المطلوب
        if ($request->get('format') === 'pdf') {
            $pdf = Pdf::loadView('invoices.report-pdf', compact('invoices', 'summary', 'dateFrom', 'dateTo'));
            return $pdf->download('تقرير-الفواتير-' . $dateFrom->format('Y-m-d') . '-إلى-' . $dateTo->format('Y-m-d') . '.pdf');
        }

        if ($request->get('format') === 'excel') {
            return $this->exportInvoicesExcel($invoices, $summary, $dateFrom, $dateTo);
        }

        return view('invoices.report', compact('invoices', 'summary', 'agents', 'dateFrom', 'dateTo'));
    }

    /**
     * إرسال تذكير بالفواتير المتأخرة
     */
    public function sendOverdueReminders(Request $request)
    {
        Gate::authorize('invoices.edit');

        $overdueInvoices = Invoice::with(['agent', 'ship'])
            ->where('status', '!=', 'paid')
            ->where('status', '!=', 'cancelled')
            ->where('due_date', '<', Carbon::now())
            ->get();

        $sentCount = 0;
        foreach ($overdueInvoices as $invoice) {
            // هنا يمكن إضافة منطق إرسال البريد الإلكتروني أو الرسائل النصية
            // Mail::to($invoice->agent->email)->send(new OverdueInvoiceReminder($invoice));
            $sentCount++;
        }

        return back()->with('success', "تم إرسال {$sentCount} تذكير للفواتير المتأخرة");
    }

    /**
     * تطبيق خصم على فاتورة
     */
    public function applyDiscount(Request $request, Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        $validated = $request->validate([
            'discount_amount' => 'required|numeric|min:0|max:' . $invoice->total_amount,
            'discount_reason' => 'required|string|max:255'
        ]);

        $newTotal = $invoice->total_amount - $validated['discount_amount'];
        
        $invoice->update([
            'total_amount' => $newTotal,
            'notes' => ($invoice->notes ? $invoice->notes . "\n" : '') . 
                      "خصم مطبق: {$validated['discount_amount']} ل.س - السبب: {$validated['discount_reason']}"
        ]);

        return back()->with('success', 'تم تطبيق الخصم بنجاح');
    }

    /**
     * دمج فواتير متعددة
     */
    public function mergeInvoices(Request $request)
    {
        Gate::authorize('invoices.create');

        $validated = $request->validate([
            'invoice_ids' => 'required|array|min:2',
            'invoice_ids.*' => 'exists:invoices,id'
        ]);

        $invoices = Invoice::whereIn('id', $validated['invoice_ids'])
            ->where('status', 'draft')
            ->get();

        if ($invoices->count() < 2) {
            return back()->with('error', 'يجب اختيار فاتورتين على الأقل في حالة مسودة');
        }

        // التحقق من أن جميع الفواتير لنفس الوكيل
        $agentIds = $invoices->pluck('agent_id')->unique();
        if ($agentIds->count() > 1) {
            return back()->with('error', 'يجب أن تكون جميع الفواتير لنفس الوكيل الملاحي');
        }

        DB::beginTransaction();
        try {
            // إنشاء فاتورة جديدة مدمجة
            $mergedInvoice = Invoice::create([
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'ship_id' => $invoices->first()->ship_id,
                'agent_id' => $invoices->first()->agent_id,
                'invoice_date' => Carbon::now(),
                'due_date' => $invoices->max('due_date'),
                'berthing_fees' => $invoices->sum('berthing_fees'),
                'cargo_handling_fees' => $invoices->sum('cargo_handling_fees'),
                'storage_fees' => $invoices->sum('storage_fees'),
                'pilotage_fees' => $invoices->sum('pilotage_fees'),
                'other_fees' => $invoices->sum('other_fees'),
                'total_amount' => $invoices->sum('total_amount'),
                'paid_amount' => 0,
                'status' => 'draft',
                'notes' => 'فاتورة مدمجة من الفواتير: ' . $invoices->pluck('invoice_number')->implode(', ')
            ]);

            // حذف الفواتير الأصلية
            Invoice::whereIn('id', $validated['invoice_ids'])->delete();

            DB::commit();

            return redirect()->route('invoices.show', $mergedInvoice)
                ->with('success', 'تم دمج الفواتير بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'حدث خطأ أثناء دمج الفواتير');
        }
    }

    /**
     * تصدير الفواتير إلى Excel
     */
    private function exportInvoicesExcel($invoices, $summary, $dateFrom, $dateTo)
    {
        return new InvoicesExport($invoices, $summary, $dateFrom, $dateTo);
    }

    /**
     * إحصائيات سريعة للفواتير
     */
    public function quickStats()
    {
        Gate::authorize('invoices.view');

        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        $stats = [
            'today' => [
                'invoices' => Invoice::whereDate('invoice_date', $today)->count(),
                'amount' => Invoice::whereDate('invoice_date', $today)->sum('total_amount'),
            ],
            'this_month' => [
                'invoices' => Invoice::where('invoice_date', '>=', $thisMonth)->count(),
                'amount' => Invoice::where('invoice_date', '>=', $thisMonth)->sum('total_amount'),
                'paid' => Invoice::where('invoice_date', '>=', $thisMonth)->sum('paid_amount'),
            ],
            'overdue' => [
                'count' => Invoice::where('due_date', '<', Carbon::now())
                    ->where('status', '!=', 'paid')
                    ->where('status', '!=', 'cancelled')
                    ->count(),
                'amount' => Invoice::where('due_date', '<', Carbon::now())
                    ->where('status', '!=', 'paid')
                    ->where('status', '!=', 'cancelled')
                    ->sum('total_amount'),
            ]
        ];

        return response()->json($stats);
    }

    /**
     * إرسال فاتورة
     */
    public function send(Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        if ($invoice->status !== 'draft') {
            return response()->json(['success' => false, 'message' => 'يمكن إرسال المسودات فقط']);
        }

        $invoice->update(['status' => 'sent', 'sent_at' => now()]);

        return response()->json(['success' => true, 'message' => 'تم إرسال الفاتورة بنجاح']);
    }

    /**
     * إلغاء فاتورة
     */
    public function cancel(Invoice $invoice)
    {
        Gate::authorize('invoices.edit');

        if (in_array($invoice->status, ['paid', 'cancelled'])) {
            return response()->json(['success' => false, 'message' => 'لا يمكن إلغاء هذه الفاتورة']);
        }

        $invoice->update(['status' => 'cancelled', 'cancelled_at' => now()]);

        return response()->json(['success' => true, 'message' => 'تم إلغاء الفاتورة بنجاح']);
    }

    /**
     * Export invoices to Excel
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('invoices.view');

        $query = Invoice::with(['ship', 'agent']);

        // تطبيق نفس الفلاتر المستخدمة في الفهرس
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('invoice_number', 'like', '%' . $request->search . '%')
                  ->orWhereHas('ship', function($shipQuery) use ($request) {
                      $shipQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $invoices = $query->orderBy('invoice_date', 'desc')->get();

        return Excel::download(
            new InvoicesExport($invoices), 
            'invoices-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Bulk actions for invoices
     */
    public function bulkActions(Request $request)
    {
        Gate::authorize('invoices.edit');

        $validated = $request->validate([
            'action' => 'required|in:send,cancel,delete,update_status',
            'invoice_ids' => 'required|array',
            'invoice_ids.*' => 'exists:invoices,id',
            'status' => 'required_if:action,update_status|in:draft,sent,paid,overdue,cancelled'
        ]);

        $invoices = Invoice::whereIn('id', $validated['invoice_ids']);

        switch ($validated['action']) {
            case 'send':
                $invoices->where('status', 'draft')->update([
                    'status' => 'sent',
                    'sent_at' => now()
                ]);
                return redirect()->back()
                    ->with('success', 'تم إرسال الفواتير المحددة بنجاح');
                break;

            case 'cancel':
                $invoices->whereNotIn('status', ['paid', 'cancelled'])->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now()
                ]);
                return redirect()->back()
                    ->with('success', 'تم إلغاء الفواتير المحددة بنجاح');
                break;

            case 'delete':
                if (!Gate::allows('invoices.delete')) {
                    abort(403);
                }
                $invoices->where('status', 'draft')->delete();
                return redirect()->back()
                    ->with('success', 'تم حذف الفواتير المحددة بنجاح');
                break;

            case 'update_status':
                $invoices->update(['status' => $validated['status']]);
                return redirect()->back()
                    ->with('success', 'تم تحديث حالة الفواتير المحددة بنجاح');
                break;
        }

        return redirect()->back();
    }

    /**
     * Advanced financial report
     */
    public function advancedReport(Request $request)
    {
        Gate::authorize('invoices.view');

        $dateFrom = $request->input('date_from', now()->startOfMonth());
        $dateTo = $request->input('date_to', now()->endOfMonth());

        // إحصائيات شاملة
        $stats = [
            'total_invoices' => Invoice::whereBetween('invoice_date', [$dateFrom, $dateTo])->count(),
            'total_amount' => Invoice::whereBetween('invoice_date', [$dateFrom, $dateTo])->sum('total_amount'),
            'paid_amount' => Invoice::whereBetween('invoice_date', [$dateFrom, $dateTo])->sum('paid_amount'),
            'pending_amount' => Invoice::whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->where('status', '!=', 'paid')
                ->where('status', '!=', 'cancelled')
                ->sum('total_amount'),
            'overdue_amount' => Invoice::where('due_date', '<', now())
                ->where('status', '!=', 'paid')
                ->where('status', '!=', 'cancelled')
                ->sum('total_amount'),
        ];

        // إحصائيات حسب الوكلاء
        $agentStats = Invoice::with('agent')
            ->whereBetween('invoice_date', [$dateFrom, $dateTo])
            ->selectRaw('agent_id, COUNT(*) as invoice_count, SUM(total_amount) as total_amount, SUM(paid_amount) as paid_amount')
            ->groupBy('agent_id')
            ->orderByDesc('total_amount')
            ->get();

        // إحصائيات شهرية
        $monthlyStats = Invoice::selectRaw('
                YEAR(invoice_date) as year,
                MONTH(invoice_date) as month,
                COUNT(*) as invoice_count,
                SUM(total_amount) as total_amount,
                SUM(paid_amount) as paid_amount
            ')
            ->whereBetween('invoice_date', [$dateFrom, $dateTo])
            ->groupByRaw('YEAR(invoice_date), MONTH(invoice_date)')
            ->orderByRaw('YEAR(invoice_date) DESC, MONTH(invoice_date) DESC')
            ->get();

        return view('invoices.advanced-report', compact(
            'stats', 'agentStats', 'monthlyStats', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * Payment tracking report
     */
    public function paymentTrackingReport(Request $request)
    {
        Gate::authorize('invoices.view');

        $query = Invoice::with(['ship', 'agent', 'payments']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        $invoices = $query->orderBy('invoice_date', 'desc')->paginate(20);

        return view('invoices.payment-tracking', compact('invoices'));
    }
}
