@extends('layouts.port-app')

@section('page-title', 'إدارة المستخدمين')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-users me-2"></i>
                    إدارة المستخدمين
                </h2>
                <div class="btn-group">
                    @can('users.create')
                    <a href="{{ route('users.create') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم جديد
                    </a>
                    @endcan
                    
                    <div class="btn-group">
                        <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-tools me-2"></i>
                            أدوات متقدمة
                        </button>
                        <ul class="dropdown-menu">
                            @can('users.view')
                            <li>
                                <a class="dropdown-item" href="{{ route('users.export') }}">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير قائمة المستخدمين
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('users.activity-log', ['user' => $users->first()?->id ?? 1]) }}">
                                    <i class="fas fa-history me-2"></i>
                                    سجل النشاطات
                                </a>
                            </li>
                            @endcan
                            @can('users.edit')
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#bulkActionsModal">
                                    <i class="fas fa-tasks me-2"></i>
                                    إجراءات مجمعة
                                </button>
                            </li>
                            @endcan
                        </ul>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $stats['total_users'] ?? 0 }}</h3>
                                    <p class="mb-0">إجمالي المستخدمين</p>
                                </div>
                                <div class="fs-1">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $stats['active_users'] ?? 0 }}</h3>
                                    <p class="mb-0">مستخدمين نشطين</p>
                                </div>
                                <div class="fs-1">
                                    <i class="fas fa-user-check"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $stats['inactive_users'] ?? 0 }}</h3>
                                    <p class="mb-0">مستخدمين غير نشطين</p>
                                </div>
                                <div class="fs-1">
                                    <i class="fas fa-user-times"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0">{{ $stats['online_users'] ?? 0 }}</h3>
                                    <p class="mb-0">متصلين الآن</p>
                                </div>
                                <div class="fs-1">
                                    <i class="fas fa-circle text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث والتصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('users.index') }}" id="filterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" name="search" id="search" class="form-control" 
                                           value="{{ request('search') }}" placeholder="اسم المستخدم، البريد الإلكتروني...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="role" class="form-label">الدور</label>
                                    <select name="role" id="role" class="form-select">
                                        <option value="">جميع الأدوار</option>
                                        @foreach($roles ?? [] as $role)
                                        <option value="{{ $role->name }}" {{ request('role') === $role->name ? 'selected' : '' }}>
                                            {{ $role->display_name ?? $role->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>نشط</option>
                                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                        <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>معلق</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="created_from" class="form-label">تاريخ الإنشاء من</label>
                                    <input type="date" name="created_from" id="created_from" 
                                           class="form-control" value="{{ request('created_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="created_to" class="form-label">إلى</label>
                                    <input type="date" name="created_to" id="created_to" 
                                           class="form-control" value="{{ request('created_to') }}">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول المستخدمين -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المستخدمين ({{ $users->total() ?? 0 }})
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                            <i class="fas fa-check-square me-1"></i>
                            تحديد الكل
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="deselectAll()">
                            <i class="fas fa-square me-1"></i>
                            إلغاء التحديد
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="usersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>آخر تسجيل دخول</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($users ?? [] as $user)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_users[]" value="{{ $user->id }}" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                @if($user->avatar)
                                                <img src="{{ asset('storage/' . $user->avatar) }}" 
                                                     class="avatar-img rounded-circle" alt="{{ $user->name }}">
                                                @else
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    {{ substr($user->name, 0, 1) }}
                                                </div>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $user->name }}</h6>
                                                <small class="text-muted">{{ $user->username ?? 'غير محدد' }}</small>
                                                @if($user->is_online ?? false)
                                                <span class="badge bg-success ms-1">متصل</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {{ $user->email }}
                                            @if($user->email_verified_at)
                                            <i class="fas fa-check-circle text-success ms-1" title="بريد إلكتروني مؤكد"></i>
                                            @else
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="بريد إلكتروني غير مؤكد"></i>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @foreach($user->roles ?? [] as $role)
                                        <span class="badge bg-info me-1">{{ $role->display_name ?? $role->name }}</span>
                                        @endforeach
                                    </td>
                                    <td>
                                        @if($user->is_active ?? true)
                                            <span class="badge bg-success">نشط</span>
                                        @elseif($user->is_suspended ?? false)
                                            <span class="badge bg-danger">معلق</span>
                                        @else
                                            <span class="badge bg-secondary">غير نشط</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->last_login_at)
                                        <div>
                                            {{ $user->last_login_at->format('Y/m/d H:i') }}
                                            <br>
                                            <small class="text-muted">{{ $user->last_login_at->diffForHumans() }}</small>
                                        </div>
                                        @else
                                        <span class="text-muted">لم يسجل دخول</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            {{ $user->created_at->format('Y/m/d') }}
                                            <br>
                                            <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @can('users.view')
                                            <a href="{{ route('users.show', $user) }}" class="btn btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('users.edit')
                                            <a href="{{ route('users.edit', $user) }}" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                            
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown" title="المزيد">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    @can('users.edit')
                                                    @if($user->is_active ?? true)
                                                    <li>
                                                        <button type="button" class="dropdown-item text-warning" 
                                                                onclick="suspendUser({{ $user->id }})">
                                                            <i class="fas fa-ban me-2"></i>
                                                            تعليق المستخدم
                                                        </button>
                                                    </li>
                                                    @else
                                                    <li>
                                                        <button type="button" class="dropdown-item text-success" 
                                                                onclick="activateUser({{ $user->id }})">
                                                            <i class="fas fa-check me-2"></i>
                                                            تفعيل المستخدم
                                                        </button>
                                                    </li>
                                                    @endif
                                                    
                                                    <li>
                                                        <button type="button" class="dropdown-item" 
                                                                onclick="resetPassword({{ $user->id }})">
                                                            <i class="fas fa-key me-2"></i>
                                                            إعادة تعيين كلمة المرور
                                                        </button>
                                                    </li>
                                                    @endcan
                                                    
                                                    @can('users.delete')
                                                    @if($user->id !== auth()->id())
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button type="button" class="dropdown-item text-danger" 
                                                                onclick="deleteUser({{ $user->id }})">
                                                            <i class="fas fa-trash me-2"></i>
                                                            حذف المستخدم
                                                        </button>
                                                    </li>
                                                    @endif
                                                    @endcan
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد مستخدمين</p>
                                        @can('users.create')
                                        <a href="{{ route('users.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة أول مستخدم
                                        </a>
                                        @endcan
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if(isset($users) && $users->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $users->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal الإجراءات المجمعة -->
@can('users.edit')
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الإجراءات المجمعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkActionsForm">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bulk_action" class="form-label">اختر الإجراء</label>
                        <select name="action" id="bulk_action" class="form-select" required>
                            <option value="">اختر الإجراء</option>
                            <option value="activate">تفعيل المستخدمين</option>
                            <option value="suspend">تعليق المستخدمين</option>
                            <option value="send_welcome">إرسال بريد ترحيبي</option>
                            <option value="reset_password">إعادة تعيين كلمة المرور</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تطبيق الإجراء على المستخدمين المحددين فقط.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check me-1"></i>
                        تطبيق الإجراء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
<script>
// تحديد/إلغاء تحديد جميع المستخدمين
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    
    userCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function selectAll() {
    document.getElementById('selectAllCheckbox').checked = true;
    toggleSelectAll();
}

function deselectAll() {
    document.getElementById('selectAllCheckbox').checked = false;
    toggleSelectAll();
}

// تعليق مستخدم
function suspendUser(userId) {
    if (confirm('هل أنت متأكد من تعليق هذا المستخدم؟')) {
        fetch(`/users/${userId}/suspend`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تعليق المستخدم');
            }
        });
    }
}

// تفعيل مستخدم
function activateUser(userId) {
    if (confirm('هل أنت متأكد من تفعيل هذا المستخدم؟')) {
        fetch(`/users/${userId}/activate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تفعيل المستخدم');
            }
        });
    }
}

// إعادة تعيين كلمة المرور
function resetPassword(userId) {
    if (confirm('هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟')) {
        fetch(`/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال رابط إعادة تعيين كلمة المرور بنجاح');
            } else {
                alert('حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور');
            }
        });
    }
}

// حذف مستخدم
function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف المستخدم');
            }
        });
    }
}

// الإجراءات المجمعة
document.getElementById('bulkActionsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    
    if (selectedUsers.length === 0) {
        alert('يرجى تحديد مستخدم واحد على الأقل');
        return;
    }
    
    const action = document.getElementById('bulk_action').value;
    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }
    
    if (confirm(`هل أنت متأكد من تطبيق هذا الإجراء على ${selectedUsers.length} مستخدم؟`)) {
        const formData = new FormData(this);
        formData.append('user_ids', JSON.stringify(selectedUsers));
        
        fetch('/users/bulk-actions', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تطبيق الإجراء');
            }
        });
    }
});

// تفعيل DataTable
$(document).ready(function() {
    $('#usersTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        order: [[6, 'desc']], // ترتيب حسب تاريخ الإنشاء
        pageLength: 25,
        responsive: true,
        columnDefs: [
            { orderable: false, targets: [0, 7] } // عدم ترتيب عمود التحديد والإجراءات
        ]
    });
});
</script>
@endpush