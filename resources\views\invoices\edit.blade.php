@extends('layouts.port-app')

@section('title', 'تعديل الفاتورة - ' . $invoice->invoice_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الفاتورة {{ $invoice->invoice_number }}
                </h2>
                <div>
                    <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للفاتورة
                    </a>
                    <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>
                        قائمة الفواتير
                    </a>
                </div>
            </div>

            <form method="POST" action="{{ route('invoices.update', $invoice) }}" id="invoiceForm">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <!-- معلومات أساسية -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    المعلومات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ship_id" class="form-label">السفينة <span class="text-danger">*</span></label>
                                            <select name="ship_id" id="ship_id" class="form-select @error('ship_id') is-invalid @enderror" required>
                                                <option value="">اختر السفينة</option>
                                                @foreach($ships as $ship)
                                                <option value="{{ $ship->id }}" 
                                                        data-agent-id="{{ $ship->agent_id }}"
                                                        {{ old('ship_id', $invoice->ship_id) == $ship->id ? 'selected' : '' }}>
                                                    {{ $ship->name }} - {{ $ship->ship_type }}
                                                </option>
                                                @endforeach
                                            </select>
                                            @error('ship_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="agent_id" class="form-label">الوكيل الملاحي <span class="text-danger">*</span></label>
                                            <select name="agent_id" id="agent_id" class="form-select @error('agent_id') is-invalid @enderror" required>
                                                <option value="">اختر الوكيل الملاحي</option>
                                                @foreach($agents as $agent)
                                                <option value="{{ $agent->id }}" {{ old('agent_id', $invoice->agent_id) == $agent->id ? 'selected' : '' }}>
                                                    {{ $agent->name }} - {{ $agent->company_name }}
                                                </option>
                                                @endforeach
                                            </select>
                                            @error('agent_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_date" class="form-label">تاريخ الفاتورة <span class="text-danger">*</span></label>
                                            <input type="date" name="invoice_date" id="invoice_date" 
                                                   class="form-control @error('invoice_date') is-invalid @enderror" 
                                                   value="{{ old('invoice_date', $invoice->invoice_date->format('Y-m-d')) }}" required>
                                            @error('invoice_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                            <input type="date" name="due_date" id="due_date" 
                                                   class="form-control @error('due_date') is-invalid @enderror" 
                                                   value="{{ old('due_date', $invoice->due_date?->format('Y-m-d')) }}">
                                            @error('due_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الرسوم -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-calculator me-2"></i>
                                    تفاصيل الرسوم
                                </h5>
                                <button type="button" id="calculateFeesBtn" class="btn btn-sm btn-info">
                                    <i class="fas fa-magic me-1"></i>
                                    إعادة حساب
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="berthing_fees" class="form-label">رسوم الرسو (ل.س)</label>
                                            <input type="number" name="berthing_fees" id="berthing_fees" 
                                                   class="form-control @error('berthing_fees') is-invalid @enderror" 
                                                   value="{{ old('berthing_fees', $invoice->berthing_fees) }}" step="0.01" min="0">
                                            @error('berthing_fees')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="cargo_handling_fees" class="form-label">رسوم مناولة البضائع (ل.س)</label>
                                            <input type="number" name="cargo_handling_fees" id="cargo_handling_fees" 
                                                   class="form-control @error('cargo_handling_fees') is-invalid @enderror" 
                                                   value="{{ old('cargo_handling_fees', $invoice->cargo_handling_fees) }}" step="0.01" min="0">
                                            @error('cargo_handling_fees')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="storage_fees" class="form-label">رسوم التخزين (ل.س)</label>
                                            <input type="number" name="storage_fees" id="storage_fees" 
                                                   class="form-control @error('storage_fees') is-invalid @enderror" 
                                                   value="{{ old('storage_fees', $invoice->storage_fees) }}" step="0.01" min="0">
                                            @error('storage_fees')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="pilotage_fees" class="form-label">رسوم الإرشاد (ل.س)</label>
                                            <input type="number" name="pilotage_fees" id="pilotage_fees" 
                                                   class="form-control @error('pilotage_fees') is-invalid @enderror" 
                                                   value="{{ old('pilotage_fees', $invoice->pilotage_fees) }}" step="0.01" min="0">
                                            @error('pilotage_fees')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="other_fees" class="form-label">رسوم أخرى (ل.س)</label>
                                            <input type="number" name="other_fees" id="other_fees" 
                                                   class="form-control @error('other_fees') is-invalid @enderror" 
                                                   value="{{ old('other_fees', $invoice->other_fees) }}" step="0.01" min="0">
                                            @error('other_fees')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المبلغ الإجمالي (ل.س)</label>
                                            <div class="form-control bg-light" id="total_amount_display">{{ number_format($invoice->total_amount, 2) }}</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea name="notes" id="notes" rows="3" 
                                              class="form-control @error('notes') is-invalid @enderror" 
                                              placeholder="أي ملاحظات إضافية...">{{ old('notes', $invoice->notes) }}</textarea>
                                    @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الفاتورة الحالية -->
                    <div class="col-md-4">
                        <!-- معلومات الفاتورة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>
                                    معلومات الفاتورة
                                </h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td><strong>رقم الفاتورة:</strong></td>
                                        <td>{{ $invoice->invoice_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ $invoice->status_color }}">
                                                {{ $invoice->status_label }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>المبلغ المدفوع:</strong></td>
                                        <td><strong class="text-success">{{ number_format($invoice->paid_amount, 2) }} ل.س</strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>المبلغ المتبقي:</strong></td>
                                        <td>
                                            @if($invoice->remaining_amount > 0)
                                                <strong class="text-danger">{{ number_format($invoice->remaining_amount, 2) }} ل.س</strong>
                                            @else
                                                <strong class="text-success">0.00 ل.س</strong>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ الإنشاء:</strong></td>
                                        <td>{{ $invoice->created_at->format('Y/m/d') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- تحذيرات -->
                        @if($invoice->paid_amount > 0)
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تنبيه
                            </h6>
                            <p class="mb-0">تم دفع مبلغ {{ number_format($invoice->paid_amount, 2) }} ل.س من هذه الفاتورة. تأكد من صحة التعديلات قبل الحفظ.</p>
                        </div>
                        @endif

                        <!-- نصائح -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    نصائح
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        يمكن إعادة حساب الرسوم تلقائياً
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        تأكد من صحة المبالغ قبل الحفظ
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        لا يمكن تعديل الفواتير المدفوعة
                                    </li>
                                    <li class="mb-0">
                                        <i class="fas fa-check text-success me-2"></i>
                                        سيتم إعادة حساب المجموع تلقائياً
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-secondary btn-lg me-3">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-list me-2"></i>
                                    قائمة الفواتير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الوكيل الملاحي عند اختيار السفينة
    const shipSelect = document.getElementById('ship_id');
    const agentSelect = document.getElementById('agent_id');
    
    shipSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const agentId = selectedOption.getAttribute('data-agent-id');
        
        if (agentId) {
            agentSelect.value = agentId;
        }
    });
    
    // حساب المجموع تلقائياً
    const feeInputs = ['berthing_fees', 'cargo_handling_fees', 'storage_fees', 'pilotage_fees', 'other_fees'];
    const totalDisplay = document.getElementById('total_amount_display');
    
    function calculateTotal() {
        let total = 0;
        feeInputs.forEach(function(inputId) {
            const input = document.getElementById(inputId);
            const value = parseFloat(input.value) || 0;
            total += value;
        });
        totalDisplay.textContent = total.toFixed(2);
    }
    
    feeInputs.forEach(function(inputId) {
        document.getElementById(inputId).addEventListener('input', calculateTotal);
    });
    
    // حساب الرسوم تلقائياً
    document.getElementById('calculateFeesBtn').addEventListener('click', function() {
        const shipId = shipSelect.value;
        
        if (!shipId) {
            alert('يرجى اختيار السفينة أولاً');
            return;
        }
        
        if (!confirm('هل تريد إعادة حساب الرسوم؟ سيتم استبدال القيم الحالية.')) {
            return;
        }
        
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحساب...';
        
        fetch('{{ route("invoices.calculate-fees") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                ship_id: shipId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
            } else {
                document.getElementById('berthing_fees').value = data.berthing_fees;
                document.getElementById('cargo_handling_fees').value = data.cargo_handling_fees;
                document.getElementById('storage_fees').value = data.storage_fees;
                document.getElementById('pilotage_fees').value = data.pilotage_fees;
                document.getElementById('other_fees').value = data.other_fees;
                calculateTotal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حساب الرسوم');
        })
        .finally(() => {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-magic me-1"></i> إعادة حساب';
        });
    });
    
    // حساب المجموع عند تحميل الصفحة
    calculateTotal();
});
</script>
@endsection
