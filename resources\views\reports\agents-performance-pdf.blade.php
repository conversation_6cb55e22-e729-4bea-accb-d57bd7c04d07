<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير أداء الوكلاء الملاحيين</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body {
            font-family: 'Cairo', 'DejaVu Sans', sans-serif;
            font-size: 12px;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .amount {
            font-weight: bold;
        }
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <h2>تقرير أداء الوكلاء الملاحيين</h2>
    <p>الفترة: {{ $date_from->format('Y/m/d') }} - {{ $date_to->format('Y/m/d') }}</p>
    <table>
        <thead>
            <tr>
                <th>الترتيب</th>
                <th>اسم الوكيل</th>
                <th>اسم الشركة</th>
                <th>عدد السفن</th>
                <th>عدد الفواتير</th>
                <th>إجمالي المبلغ (ل.س)</th>
                <th>المبلغ المدفوع (ل.س)</th>
                <th>المبلغ المعلق (ل.س)</th>
            </tr>
        </thead>
        <tbody>
            @php $i = 1; @endphp
            @foreach($agent_stats as $agent)
            <tr>
                <td>{{ $i++ }}</td>
                <td>{{ $agent['agent']->name }}</td>
                <td>{{ $agent['agent']->company_name }}</td>
                <td>{{ $agent['ships_count'] }}</td>
                <td>{{ $agent['invoices_count'] }}</td>
                <td class="amount">{{ number_format($agent['total_amount'], 0) }}</td>
                <td class="amount">{{ number_format($agent['paid_amount'], 0) }}</td>
                <td class="amount">{{ number_format($agent['pending_amount'], 0) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المرفأ البحري</p>
        <p>{{ config('app.name') }} - تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>
</body>
</html> 