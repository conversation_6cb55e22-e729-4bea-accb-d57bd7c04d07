@extends('layouts.port-app')

@section('title', 'تقرير استخدام الأرصفة')

@section('content')
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="text-primary">
                    <i class="fas fa-warehouse me-2"></i>
                    تقرير استخدام الأرصفة
                </h2>
                <div class="btn-group">
                    <a href="{{ route('reports.berths-utilization', array_merge(request()->all(), ['format' => 'pdf'])) }}" 
                       class="btn btn-danger">
                        <i class="fas fa-file-pdf me-2"></i>
                        تحميل PDF
                    </a>
                    <a href="{{ route('reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
            <p class="text-muted">
                الفترة: {{ $date_from->format('Y/m/d') }} - {{ $date_to->format('Y/m/d') }}
                ({{ $date_from->diffInDays($date_to) + 1 }} يوم)
            </p>
        </div>
    </div>

    <!-- فلتر التاريخ -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar me-2"></i>
                اختيار الفترة الزمنية
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('reports.berths-utilization') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" 
                               value="{{ request('date_from', $date_from->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" 
                               value="{{ request('date_to', $date_to->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search me-2"></i>
                            عرض التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_berths'] }}</h3>
                    <p class="mb-0">إجمالي الأرصفة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['average_utilization'], 1) }}%</h3>
                    <p class="mb-0">متوسط الاستخدام</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_revenue'], 0) }}</h3>
                    <p class="mb-0">إجمالي الإيرادات (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_ships'] }}</h3>
                    <p class="mb-0">إجمالي السفن</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الأرصفة -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل استخدام الأرصفة
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الرصيف</th>
                            <th>النوع</th>
                            <th>الطول (م)</th>
                            <th>العمق (م)</th>
                            <th>الأيام المشغولة</th>
                            <th>معدل الاستخدام</th>
                            <th>عدد السفن</th>
                            <th>إجمالي الإيرادات</th>
                            <th>متوسط الإيراد/يوم</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($berth_stats as $stat)
                        <tr>
                            <td>
                                <strong>{{ $stat['berth']->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $stat['berth']->code }}</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $stat['berth']->berth_type }}</span>
                            </td>
                            <td>{{ $stat['berth']->length }}</td>
                            <td>{{ $stat['berth']->depth }}</td>
                            <td>
                                <span class="badge bg-primary">
                                    {{ $stat['occupied_days'] }} / {{ $stat['total_days'] }}
                                </span>
                            </td>
                            <td>
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar 
                                        @if($stat['utilization_rate'] >= 80) bg-success
                                        @elseif($stat['utilization_rate'] >= 60) bg-warning
                                        @elseif($stat['utilization_rate'] >= 40) bg-info
                                        @else bg-danger
                                        @endif" 
                                        role="progressbar" 
                                        style="width: {{ $stat['utilization_rate'] }}%">
                                        {{ $stat['utilization_rate'] }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($stat['ships_count'] > 0)
                                    <span class="badge bg-success">{{ $stat['ships_count'] }}</span>
                                @else
                                    <span class="text-muted">0</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($stat['total_revenue'], 0) }} ل.س</strong>
                            </td>
                            <td>
                                {{ number_format($stat['average_revenue_per_day'], 0) }} ل.س
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="4">الإجمالي</th>
                            <th>{{ $berth_stats->sum('occupied_days') }}</th>
                            <th>{{ number_format($berth_stats->avg('utilization_rate'), 1) }}%</th>
                            <th>{{ $berth_stats->sum('ships_count') }}</th>
                            <th>{{ number_format($berth_stats->sum('total_revenue'), 0) }} ل.س</th>
                            <th>{{ number_format($berth_stats->avg('average_revenue_per_day'), 0) }} ل.س</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- تحليل الأداء -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تصنيف الأرصفة حسب الاستخدام
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $high_utilization = $berth_stats->where('utilization_rate', '>=', 80)->count();
                        $medium_utilization = $berth_stats->where('utilization_rate', '>=', 60)->where('utilization_rate', '<', 80)->count();
                        $low_utilization = $berth_stats->where('utilization_rate', '<', 60)->count();
                    @endphp
                    
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-success text-white">
                                <h3>{{ $high_utilization }}</h3>
                                <p class="mb-0">استخدام عالي (≥80%)</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-warning text-dark">
                                <h3>{{ $medium_utilization }}</h3>
                                <p class="mb-0">استخدام متوسط (60-79%)</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 bg-danger text-white">
                                <h3>{{ $low_utilization }}</h3>
                                <p class="mb-0">استخدام منخفض (<60%)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        أفضل 3 أرصفة من حيث الإيرادات
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($berth_stats->sortByDesc('total_revenue')->take(3) as $index => $stat)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge bg-{{ $index == 0 ? 'warning' : ($index == 1 ? 'secondary' : 'dark') }} me-2">
                                {{ $index + 1 }}
                            </span>
                            <strong>{{ $stat['berth']->name }}</strong>
                            <br>
                            <small class="text-muted">{{ $stat['ships_count'] }} سفينة</small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success">{{ number_format($stat['total_revenue'], 0) }} ل.س</strong>
                            <br>
                            <small class="text-muted">{{ $stat['utilization_rate'] }}% استخدام</small>
                        </div>
                    </div>
                    @if(!$loop->last)
                        <hr>
                    @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// يمكن إضافة مخططات بيانية هنا
</script>
@endpush
@endsection