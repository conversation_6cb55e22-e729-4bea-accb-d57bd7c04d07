<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cargos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained('ships')->onDelete('cascade'); // السفينة
            $table->string('cargo_type'); // نوع الحمولة
            $table->text('description'); // وصف الحمولة
            $table->decimal('quantity', 12, 2); // الكمية
            $table->string('unit'); // الوحدة
            $table->decimal('weight', 12, 2)->nullable(); // الوزن
            $table->decimal('volume', 12, 2)->nullable(); // الحجم
            $table->decimal('value', 15, 2)->nullable(); // القيمة
            $table->string('currency', 3)->nullable(); // العملة
            $table->string('origin_country'); // بلد المنشأ
            $table->string('destination_country'); // بلد الوجهة
            $table->enum('status', ['loading', 'loaded', 'in_transit', 'unloading', 'unloaded', 'stored', 'delivered'])->default('loading'); // الحالة
            $table->datetime('loading_date')->nullable(); // تاريخ التحميل
            $table->datetime('unloading_date')->nullable(); // تاريخ التفريغ
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cargos');
    }
};
