@extends('layouts.port-app')

@section('page-title', 'تفاصيل المستخدم - ' . $user->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user me-2"></i>
                    تفاصيل المستخدم
                </h2>
                <div class="btn-group">
                    <a href="{{ route('users.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                    @can('users.edit')
                    <a href="{{ route('users.edit', $user) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    @endcan
                    
                    <div class="btn-group">
                        <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>
                            إجراءات
                        </button>
                        <ul class="dropdown-menu">
                            @can('users.edit')
                            @if($user->is_active)
                            <li>
                                <button type="button" class="dropdown-item text-warning" onclick="suspendUser()">
                                    <i class="fas fa-ban me-2"></i>
                                    تعليق المستخدم
                                </button>
                            </li>
                            @else
                            <li>
                                <button type="button" class="dropdown-item text-success" onclick="activateUser()">
                                    <i class="fas fa-check me-2"></i>
                                    تفعيل المستخدم
                                </button>
                            </li>
                            @endif
                            
                            <li>
                                <button type="button" class="dropdown-item" onclick="resetPassword()">
                                    <i class="fas fa-key me-2"></i>
                                    إعادة تعيين كلمة المرور
                                </button>
                            </li>
                            
                            <li>
                                <button type="button" class="dropdown-item" onclick="sendWelcomeEmail()">
                                    <i class="fas fa-envelope me-2"></i>
                                    إرسال بريد ترحيبي
                                </button>
                            </li>
                            
                            <li><hr class="dropdown-divider"></li>
                            
                            <li>
                                <a class="dropdown-item" href="{{ route('users.activity-log', $user) }}">
                                    <i class="fas fa-history me-2"></i>
                                    سجل النشاطات
                                </a>
                            </li>
                            
                            <li>
                                <a class="dropdown-item" href="{{ route('users.login-history', $user) }}">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    سجل تسجيل الدخول
                                </a>
                            </li>
                            @endcan
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- المعلومات الأساسية -->
                <div class="col-md-8">
                    <!-- بطاقة المستخدم -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <div class="avatar-xl mb-3">
                                        @if($user->avatar)
                                        <img src="{{ asset('storage/' . $user->avatar) }}" 
                                             class="avatar-img rounded-circle" alt="{{ $user->name }}">
                                        @else
                                        <div class="avatar-title bg-primary rounded-circle fs-1">
                                            {{ substr($user->name, 0, 1) }}
                                        </div>
                                        @endif
                                    </div>
                                    @if($user->is_online)
                                    <span class="badge bg-success mb-2">
                                        <i class="fas fa-circle me-1"></i>
                                        متصل الآن
                                    </span>
                                    @else
                                    <span class="badge bg-secondary mb-2">
                                        <i class="fas fa-circle me-1"></i>
                                        غير متصل
                                    </span>
                                    @endif
                                </div>
                                <div class="col-md-9">
                                    <h3 class="mb-1">{{ $user->name }}</h3>
                                    <p class="text-muted mb-2">{{ $user->username ?? 'لا يوجد اسم مستخدم' }}</p>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>البريد الإلكتروني:</strong><br>
                                            <span class="text-muted">{{ $user->email }}</span>
                                            @if($user->email_verified_at)
                                            <i class="fas fa-check-circle text-success ms-1" title="مؤكد"></i>
                                            @else
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="غير مؤكد"></i>
                                            @endif
                                        </div>
                                        <div class="col-md-6">
                                            <strong>رقم الهاتف:</strong><br>
                                            <span class="text-muted">{{ $user->phone ?? 'غير محدد' }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <strong>القسم:</strong><br>
                                            <span class="text-muted">{{ $user->department ?? 'غير محدد' }}</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>المنصب:</strong><br>
                                            <span class="text-muted">{{ $user->position ?? 'غير محدد' }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>الأدوار:</strong><br>
                                        @forelse($user->roles as $role)
                                        <span class="badge bg-info me-1">{{ $role->display_name ?? $role->name }}</span>
                                        @empty
                                        <span class="text-muted">لا توجد أدوار محددة</span>
                                        @endforelse
                                    </div>
                                    
                                    @if($user->bio)
                                    <div class="mb-3">
                                        <strong>نبذة شخصية:</strong><br>
                                        <p class="text-muted mb-0">{{ $user->bio }}</p>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات النشاط
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-primary">{{ $userStats['total_logins'] ?? 0 }}</h4>
                                        <p class="mb-0 small">مرات تسجيل الدخول</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-success">{{ $userStats['invoices_created'] ?? 0 }}</h4>
                                        <p class="mb-0 small">فواتير أنشأها</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-info">{{ $userStats['ships_managed'] ?? 0 }}</h4>
                                        <p class="mb-0 small">سفن تعامل معها</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-warning">{{ $userStats['days_since_join'] ?? 0 }}</h4>
                                        <p class="mb-0 small">يوم منذ الانضمام</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آخر النشاطات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2"></i>
                                آخر النشاطات
                            </h5>
                        </div>
                        <div class="card-body">
                            @if(isset($recentActivities) && $recentActivities->count() > 0)
                            <div class="timeline">
                                @foreach($recentActivities as $activity)
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <div class="avatar-sm">
                                                <div class="avatar-title bg-{{ $activity->type_color ?? 'primary' }} rounded-circle">
                                                    <i class="fas fa-{{ $activity->type_icon ?? 'circle' }}"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h6 class="mb-1">{{ $activity->description }}</h6>
                                                    <p class="text-muted mb-0 small">{{ $activity->details ?? '' }}</p>
                                                </div>
                                                <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            <div class="text-center">
                                <a href="{{ route('users.activity-log', $user) }}" class="btn btn-sm btn-outline-primary">
                                    عرض جميع النشاطات
                                </a>
                            </div>
                            @else
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد نشاطات حديثة</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-md-4">
                    <!-- معلومات الحساب -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الحساب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>الحالة:</strong><br>
                                @if($user->is_active)
                                    <span class="badge bg-success">نشط</span>
                                @elseif($user->is_suspended)
                                    <span class="badge bg-danger">معلق</span>
                                @else
                                    <span class="badge bg-secondary">غير نشط</span>
                                @endif
                            </div>
                            
                            <div class="mb-3">
                                <strong>تاريخ الإنشاء:</strong><br>
                                <span class="text-muted">{{ $user->created_at->format('Y/m/d H:i') }}</span>
                                <br>
                                <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                            </div>
                            
                            <div class="mb-3">
                                <strong>آخر تحديث:</strong><br>
                                <span class="text-muted">{{ $user->updated_at->format('Y/m/d H:i') }}</span>
                                <br>
                                <small class="text-muted">{{ $user->updated_at->diffForHumans() }}</small>
                            </div>
                            
                            @if($user->last_login_at)
                            <div class="mb-3">
                                <strong>آخر تسجيل دخول:</strong><br>
                                <span class="text-muted">{{ $user->last_login_at->format('Y/m/d H:i') }}</span>
                                <br>
                                <small class="text-muted">{{ $user->last_login_at->diffForHumans() }}</small>
                            </div>
                            @endif
                            
                            @if($user->hire_date)
                            <div class="mb-3">
                                <strong>تاريخ التوظيف:</strong><br>
                                <span class="text-muted">{{ $user->hire_date->format('Y/m/d') }}</span>
                                <br>
                                <small class="text-muted">{{ $user->hire_date->diffForHumans() }}</small>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- الصلاحيات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                الصلاحيات
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($user->getAllPermissions()->count() > 0)
                            <div class="row">
                                @foreach($user->getAllPermissions()->groupBy('group') as $group => $permissions)
                                <div class="col-12 mb-3">
                                    <h6 class="text-primary">{{ $group ?? 'عام' }}</h6>
                                    @foreach($permissions as $permission)
                                    <span class="badge bg-light text-dark me-1 mb-1">
                                        {{ $permission->display_name ?? $permission->name }}
                                    </span>
                                    @endforeach
                                </div>
                                @endforeach
                            </div>
                            @else
                            <p class="text-muted text-center">لا توجد صلاحيات محددة</p>
                            @endif
                        </div>
                    </div>

                    <!-- إجراءات سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            @can('users.edit')
                            <div class="d-grid gap-2">
                                @if($user->is_active)
                                <button type="button" class="btn btn-warning btn-sm" onclick="suspendUser()">
                                    <i class="fas fa-ban me-1"></i>
                                    تعليق المستخدم
                                </button>
                                @else
                                <button type="button" class="btn btn-success btn-sm" onclick="activateUser()">
                                    <i class="fas fa-check me-1"></i>
                                    تفعيل المستخدم
                                </button>
                                @endif
                                
                                <button type="button" class="btn btn-info btn-sm" onclick="resetPassword()">
                                    <i class="fas fa-key me-1"></i>
                                    إعادة تعيين كلمة المرور
                                </button>
                                
                                <button type="button" class="btn btn-secondary btn-sm" onclick="sendWelcomeEmail()">
                                    <i class="fas fa-envelope me-1"></i>
                                    إرسال بريد ترحيبي
                                </button>
                                
                                @if($user->id !== auth()->id())
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="loginAsUser()">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    تسجيل دخول كهذا المستخدم
                                </button>
                                @endif
                            </div>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// تعليق المستخدم
function suspendUser() {
    if (confirm('هل أنت متأكد من تعليق هذا المستخدم؟')) {
        fetch(`/users/{{ $user->id }}/suspend`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تعليق المستخدم');
            }
        });
    }
}

// تفعيل المستخدم
function activateUser() {
    if (confirm('هل أنت متأكد من تفعيل هذا المستخدم؟')) {
        fetch(`/users/{{ $user->id }}/activate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تفعيل المستخدم');
            }
        });
    }
}

// إعادة تعيين كلمة المرور
function resetPassword() {
    if (confirm('هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟')) {
        fetch(`/users/{{ $user->id }}/reset-password`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال رابط إعادة تعيين كلمة المرور بنجاح');
            } else {
                alert('حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور');
            }
        });
    }
}

// إرسال بريد ترحيبي
function sendWelcomeEmail() {
    if (confirm('هل تريد إرسال بريد ترحيبي لهذا المستخدم؟')) {
        fetch(`/users/{{ $user->id }}/send-welcome`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال البريد الترحيبي بنجاح');
            } else {
                alert('حدث خطأ أثناء إرسال البريد الترحيبي');
            }
        });
    }
}

// تسجيل دخول كمستخدم آخر
function loginAsUser() {
    if (confirm('هل أنت متأكد من تسجيل الدخول كهذا المستخدم؟ سيتم تسجيل خروجك من حسابك الحالي.')) {
        fetch(`/users/{{ $user->id }}/login-as`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect_url || '/dashboard';
            } else {
                alert('حدث خطأ أثناء تسجيل الدخول');
            }
        });
    }
}
</script>
@endpush