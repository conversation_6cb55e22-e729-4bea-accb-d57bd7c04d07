@extends('layouts.port-app')

@section('page-title', 'تفاصيل الرصيف: ' . $berth->name)

@section('page-actions')
<div class="btn-group">
    @can('berths.edit')
    <a href="{{ route('berths.edit', $berth) }}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i>
        تعديل
    </a>
    @endcan
    
    @if($berth->status === 'available')
        @can('berths.edit')
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#assignShipModal">
            <i class="fas fa-ship me-2"></i>
            ربط سفينة
        </button>
        @endcan
    @elseif($berth->status === 'occupied')
        @can('berths.edit')
        <button type="button" class="btn btn-danger" onclick="confirmReleaseShip()">
            <i class="fas fa-sign-out-alt me-2"></i>
            تحرير الرصيف
        </button>
        @endcan
    @endif
    
    <a href="{{ route('berths.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <!-- المعلومات الأساسية -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-anchor me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">اسم الرصيف:</td>
                                <td>{{ $berth->name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">كود الرصيف:</td>
                                <td><span class="badge bg-primary">{{ $berth->code }}</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الحالة:</td>
                                <td>
                                    @switch($berth->status)
                                        @case('available')
                                            <span class="badge bg-success">متاح</span>
                                            @break
                                        @case('occupied')
                                            <span class="badge bg-warning">مشغول</span>
                                            @break
                                        @case('maintenance')
                                            <span class="badge bg-info">قيد الصيانة</span>
                                            @break
                                        @case('out_of_service')
                                            <span class="badge bg-danger">خارج الخدمة</span>
                                            @break
                                    @endswitch
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">الطول:</td>
                                <td>{{ $berth->length ? $berth->length . ' متر' : 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">العمق:</td>
                                <td>{{ $berth->depth ? $berth->depth . ' متر' : 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الحمولة القصوى:</td>
                                <td>{{ $berth->max_tonnage ? number_format($berth->max_tonnage) . ' طن' : 'غير محدد' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($berth->description)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="text-muted">الوصف:</h6>
                        <p class="bg-light p-3 rounded">{{ $berth->description }}</p>
                    </div>
                </div>
                @endif
                
                @if($berth->status === 'occupied' && $berth->currentShip)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="fas fa-ship me-2"></i>
                                السفينة المرسية حالياً
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>اسم السفينة:</strong> {{ $berth->currentShip->name }}<br>
                                    <strong>نوع السفينة:</strong> {{ $berth->currentShip->ship_type }}<br>
                                    <strong>الجنسية:</strong> {{ $berth->currentShip->flag }}
                                </div>
                                <div class="col-md-6">
                                    <strong>تاريخ الرسو:</strong> {{ $berth->occupied_since ? $berth->occupied_since->format('Y/m/d H:i') : 'غير محدد' }}<br>
                                    <strong>مدة الرسو:</strong> {{ $berth->occupied_since ? $berth->occupied_since->diffForHumans() : 'غير محدد' }}<br>
                                    <strong>الوكيل الملاحي:</strong> {{ $berth->currentShip->agent->name ?? 'غير محدد' }}
                                </div>
                            </div>
                            <div class="mt-2">
                                <a href="{{ route('ships.show', $berth->currentShip) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض تفاصيل السفينة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
        
        <!-- تاريخ الرصيف -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ الرصيف (آخر 10 عمليات)
                </h5>
            </div>
            <div class="card-body">
                @if($berth->history->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم السفينة</th>
                                    <th>تاريخ الرسو</th>
                                    <th>تاريخ المغادرة</th>
                                    <th>مدة الإقامة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($berth->history as $record)
                                <tr>
                                    <td>
                                        <strong>{{ $record->ship->name ?? 'غير محدد' }}</strong>
                                        @if($record->ship)
                                            <br><small class="text-muted">{{ $record->ship->ship_type }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $record->berthed_at ? $record->berthed_at->format('Y/m/d H:i') : 'غير محدد' }}</td>
                                    <td>
                                        @if($record->departed_at)
                                            {{ $record->departed_at->format('Y/m/d H:i') }}
                                        @else
                                            <span class="text-muted">لا تزال مرسية</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($record->departed_at && $record->berthed_at)
                                            {{ $record->berthed_at->diffInHours($record->departed_at) }} ساعة
                                        @elseif($record->berthed_at)
                                            {{ $record->berthed_at->diffForHumans() }}
                                        @else
                                            غير محدد
                                        @endif
                                    </td>
                                    <td>
                                        @if($record->status === 'active')
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-secondary">مكتمل</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($record->ship)
                                            <a href="{{ route('ships.show', $record->ship) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                        <p class="text-muted">لا يوجد تاريخ لهذا الرصيف</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-md-4">
        <!-- إحصائيات سريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الرصيف
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ $stats['total_ships'] }}</h4>
                            <small class="text-muted">إجمالي السفن</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning">{{ $stats['current_occupancy'] }}</h4>
                            <small class="text-muted">مشغول حالياً</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-info">{{ $stats['monthly_usage'] }}</h4>
                            <small class="text-muted">استخدام شهري</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-success">{{ $stats['avg_stay_duration'] ? round($stats['avg_stay_duration']) : 0 }}</h4>
                            <small class="text-muted">متوسط الإقامة (ساعة)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات تقنية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    المعلومات التقنية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>الأبعاد:</strong><br>
                    <small class="text-muted">
                        الطول: {{ $berth->length ? $berth->length . ' متر' : 'غير محدد' }}<br>
                        العمق: {{ $berth->depth ? $berth->depth . ' متر' : 'غير محدد' }}
                    </small>
                </div>
                <div class="mb-3">
                    <strong>الحمولة القصوى:</strong><br>
                    <small class="text-muted">{{ $berth->max_tonnage ? number_format($berth->max_tonnage) . ' طن' : 'غير محدد' }}</small>
                </div>
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong><br>
                    <small class="text-muted">{{ $berth->created_at->format('Y/m/d') }}</small>
                </div>
                <div>
                    <strong>آخر تحديث:</strong><br>
                    <small class="text-muted">{{ $berth->updated_at->diffForHumans() }}</small>
                </div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @can('berths.edit')
                    <a href="{{ route('berths.edit', $berth) }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الرصيف
                    </a>
                    @endcan
                    
                    @can('reports.view')
                    <button class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقرير الرصيف
                    </button>
                    @endcan
                    
                    @can('berths.edit')
                    @if($berth->status === 'maintenance')
                    <button class="btn btn-outline-success btn-sm" onclick="updateBerthStatus('available')">
                        <i class="fas fa-check me-2"></i>
                        إنهاء الصيانة
                    </button>
                    @elseif($berth->status === 'available')
                    <button class="btn btn-outline-warning btn-sm" onclick="updateBerthStatus('maintenance')">
                        <i class="fas fa-tools me-2"></i>
                        بدء الصيانة
                    </button>
                    @endif
                    @endcan
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal ربط سفينة -->
@if($berth->status === 'available')
<div class="modal fade" id="assignShipModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ربط سفينة بالرصيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('berths.assign-ship', $berth) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="ship_id" class="form-label">اختر السفينة</label>
                        <select class="form-select" id="ship_id" name="ship_id" required>
                            <option value="">اختر السفينة...</option>
                            @foreach(\App\Models\Ship::where('status', 'outside_port')->get() as $ship)
                                <option value="{{ $ship->id }}">
                                    {{ $ship->name }} - {{ $ship->ship_type }} ({{ $ship->flag }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تحديث حالة الرصيف إلى "مشغول" وحالة السفينة إلى "مرسية"
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-ship me-2"></i>
                        ربط السفينة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
function confirmReleaseShip() {
    if (confirm('هل أنت متأكد من تحرير الرصيف؟ سيتم تحديث حالة السفينة إلى "خارج الحوض"')) {
        // إنشاء form وإرساله
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("berths.release-ship", $berth) }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function updateBerthStatus(status) {
    const statusText = status === 'maintenance' ? 'صيانة' : 'متاح';
    if (confirm(`هل أنت متأكد من تغيير حالة الرصيف إلى "${statusText}"؟`)) {
        // يمكن إضافة AJAX request هنا لتحديث الحالة
        window.location.href = '{{ route("berths.edit", $berth) }}';
    }
}
</script>
@endpush
