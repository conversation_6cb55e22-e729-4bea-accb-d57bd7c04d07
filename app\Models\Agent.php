<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Agent extends Model
{
    protected $fillable = [
        'name',
        'company_name',
        'phone',
        'email',
        'address',
        'license_number',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Get all ships for this agent
     */
    public function ships(): HasMany
    {
        return $this->hasMany(Ship::class);
    }

    /**
     * Get all invoices for this agent
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }
}
