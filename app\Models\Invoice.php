<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Invoice extends Model
{
    protected $fillable = [
        'invoice_number',
        'ship_id',
        'agent_id',
        'invoice_date',
        'due_date',
        'berthing_fees',
        'cargo_handling_fees',
        'storage_fees',
        'pilotage_fees',
        'other_fees',
        'total_amount',
        'paid_amount',
        'status',
        'notes'
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'berthing_fees' => 'decimal:2',
        'cargo_handling_fees' => 'decimal:2',
        'storage_fees' => 'decimal:2',
        'pilotage_fees' => 'decimal:2',
        'other_fees' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2'
    ];

    /**
     * العلاقة مع السفينة
     */
    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    /**
     * العلاقة مع الوكيل الملاحي
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * حساب المبلغ المتبقي
     */
    public function getRemainingAmountAttribute(): float
    {
        return $this->total_amount - $this->paid_amount;
    }

    /**
     * التحقق من كون الفاتورة مدفوعة بالكامل
     */
    public function getIsFullyPaidAttribute(): bool
    {
        return $this->paid_amount >= $this->total_amount;
    }

    /**
     * التحقق من كون الفاتورة متأخرة
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date &&
               $this->due_date->isPast() &&
               !$this->is_fully_paid;
    }

    /**
     * الحصول على حالة الفاتورة بالعربية
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'draft' => 'مسودة',
            'sent' => 'مرسلة',
            'paid' => 'مدفوعة',
            'overdue' => 'متأخرة',
            'cancelled' => 'ملغية',
            default => $this->status
        };
    }

    /**
     * الحصول على لون حالة الفاتورة
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'secondary',
            'sent' => 'warning',
            'paid' => 'success',
            'overdue' => 'danger',
            'cancelled' => 'dark',
            default => 'secondary'
        };
    }

    /**
     * توليد رقم فاتورة تلقائي
     */
    public static function generateInvoiceNumber(): string
    {
        $year = Carbon::now()->year;
        $month = Carbon::now()->format('m');

        $lastInvoice = static::whereYear('created_at', $year)
            ->whereMonth('created_at', Carbon::now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastInvoice ?
            (int) substr($lastInvoice->invoice_number, -4) + 1 : 1;

        return sprintf('INV-%s%s-%04d', $year, $month, $sequence);
    }

    /**
     * حساب إجمالي الرسوم تلقائياً
     */
    public function calculateTotal(): void
    {
        $this->total_amount =
            $this->berthing_fees +
            $this->cargo_handling_fees +
            $this->storage_fees +
            $this->pilotage_fees +
            $this->other_fees;
    }

    /**
     * Scopes للاستعلامات
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
            ->orWhere(function($q) {
                $q->where('due_date', '<', Carbon::now())
                  ->where('status', '!=', 'paid')
                  ->where('status', '!=', 'cancelled');
            });
    }

    public function scopeUnpaid($query)
    {
        return $query->where('paid_amount', '<', 'total_amount')
            ->where('status', '!=', 'cancelled');
    }
}
