@extends('layouts.port-app')

@section('page-title', 'تقرير التفتيشات')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('inspections.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى التفتيشات
    </a>
    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
        <i class="fas fa-print me-2"></i>
        طباعة
    </button>
    <a href="{{ route('inspections.export.excel', ['date_from' => request('date_from'), 'date_to' => request('date_to')]) }}" class="btn btn-outline-success">
        <i class="fas fa-file-excel me-2"></i>
        تصدير Excel
    </a>
</div>
@endsection

@section('content')
<!-- فلاتر التقرير -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            فلاتر التقرير
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('inspections.report') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="{{ request('date_from', $dateFrom->format('Y-m-d')) }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="{{ request('date_to', $dateTo->format('Y-m-d')) }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="inspection_type" class="form-label">نوع التفتيش</label>
                        <select class="form-select" id="inspection_type" name="inspection_type">
                            <option value="">جميع الأنواع</option>
                            <option value="customs" {{ request('inspection_type') == 'customs' ? 'selected' : '' }}>جمركي</option>
                            <option value="health" {{ request('inspection_type') == 'health' ? 'selected' : '' }}>صحي</option>
                            <option value="security" {{ request('inspection_type') == 'security' ? 'selected' : '' }}>أمني</option>
                            <option value="technical" {{ request('inspection_type') == 'technical' ? 'selected' : '' }}>فني</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="result" class="form-label">النتيجة</label>
                        <select class="form-select" id="result" name="result">
                            <option value="">جميع النتائج</option>
                            <option value="approved" {{ request('result') == 'approved' ? 'selected' : '' }}>موافق</option>
                            <option value="rejected" {{ request('result') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                            <option value="conditional" {{ request('result') == 'conditional' ? 'selected' : '' }}>موافق مشروط</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                تطبيق الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- إحصائيات التقرير -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-search fa-2x text-primary mb-2"></i>
                <h4 class="mb-1">{{ number_format($reportStats['total_inspections']) }}</h4>
                <small class="text-muted">إجمالي التفتيشات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="mb-1">{{ number_format($reportStats['approved']) }}</h4>
                <small class="text-muted">معتمد</small>
                @if($reportStats['total_inspections'] > 0)
                <div class="mt-1">
                    <small class="text-success">
                        {{ number_format(($reportStats['approved'] / $reportStats['total_inspections']) * 100, 1) }}%
                    </small>
                </div>
                @endif
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                <h4 class="mb-1">{{ number_format($reportStats['rejected']) }}</h4>
                <small class="text-muted">مرفوض</small>
                @if($reportStats['total_inspections'] > 0)
                <div class="mt-1">
                    <small class="text-danger">
                        {{ number_format(($reportStats['rejected'] / $reportStats['total_inspections']) * 100, 1) }}%
                    </small>
                </div>
                @endif
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <h4 class="mb-1">{{ number_format($reportStats['conditional']) }}</h4>
                <small class="text-muted">مشروط</small>
                @if($reportStats['total_inspections'] > 0)
                <div class="mt-1">
                    <small class="text-warning">
                        {{ number_format(($reportStats['conditional'] / $reportStats['total_inspections']) * 100, 1) }}%
                    </small>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- معدل الموافقة -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">معدل الموافقة العام</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ $reportStats['approval_rate'] }}%">
                        {{ number_format($reportStats['approval_rate'], 1) }}%
                    </div>
                </div>
                <small class="text-muted mt-2 d-block">
                    معدل الموافقة يشمل التفتيشات المعتمدة والمشروطة
                </small>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات حسب النوع -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع التفتيشات حسب النوع
                </h6>
            </div>
            <div class="card-body">
                @if($reportStats['by_type']->count() > 0)
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>نوع التفتيش</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reportStats['by_type'] as $type => $count)
                            <tr>
                                <td>
                                    @switch($type)
                                        @case('customs')
                                            <i class="fas fa-file-invoice me-1"></i> جمركي
                                            @break
                                        @case('health')
                                            <i class="fas fa-heartbeat me-1"></i> صحي
                                            @break
                                        @case('security')
                                            <i class="fas fa-shield-alt me-1"></i> أمني
                                            @break
                                        @case('technical')
                                            <i class="fas fa-tools me-1"></i> فني
                                            @break
                                        @default
                                            {{ $type }}
                                    @endswitch
                                </td>
                                <td>{{ number_format($count) }}</td>
                                <td>
                                    @if($reportStats['total_inspections'] > 0)
                                        {{ number_format(($count / $reportStats['total_inspections']) * 100, 1) }}%
                                    @else
                                        0%
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <p class="text-muted text-center">لا توجد بيانات</p>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    توزيع التفتيشات حسب الجهة
                </h6>
            </div>
            <div class="card-body">
                @if($reportStats['by_authority']->count() > 0)
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الجهة المفتشة</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reportStats['by_authority'] as $authority => $count)
                            <tr>
                                <td>{{ $authority }}</td>
                                <td>{{ number_format($count) }}</td>
                                <td>
                                    @if($reportStats['total_inspections'] > 0)
                                        {{ number_format(($count / $reportStats['total_inspections']) * 100, 1) }}%
                                    @else
                                        0%
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <p class="text-muted text-center">لا توجد بيانات</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- جدول التفتيشات التفصيلي -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            تفاصيل التفتيشات ({{ $inspections->count() }})
        </h6>
    </div>
    <div class="card-body p-0">
        @if($inspections->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>السفينة</th>
                        <th>نوع التفتيش</th>
                        <th>المفتش</th>
                        <th>الجهة المفتشة</th>
                        <th>تاريخ التفتيش</th>
                        <th>النتيجة</th>
                        <th>صالح حتى</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($inspections as $inspection)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            <div>
                                <strong>{{ $inspection->ship->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $inspection->ship->flag }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                <i class="{{ $inspection->inspection_icon }} me-1"></i>
                                {{ $inspection->inspection_type_label }}
                            </span>
                        </td>
                        <td>{{ $inspection->inspector_name }}</td>
                        <td>
                            <small>{{ $inspection->inspector_authority }}</small>
                        </td>
                        <td>
                            {{ $inspection->inspection_date->format('Y/m/d') }}
                            <br>
                            <small class="text-muted">{{ $inspection->inspection_date->format('H:i') }}</small>
                        </td>
                        <td>
                            <span class="badge bg-{{ $inspection->result_color }}">
                                {{ $inspection->result_label }}
                            </span>
                        </td>
                        <td>
                            @if($inspection->valid_until)
                                {{ $inspection->valid_until->format('Y/m/d') }}
                                @if(!$inspection->is_valid)
                                    <br><small class="text-danger">منتهي الصلاحية</small>
                                @endif
                            @else
                                <span class="text-muted">غير محدد</span>
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تفتيشات</h5>
            <p class="text-muted">لم يتم العثور على أي تفتيشات في الفترة المحددة</p>
        </div>
        @endif
    </div>
</div>

<!-- معلومات التقرير -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات التقرير</h6>
                        <p class="mb-1"><strong>تاريخ التقرير:</strong> {{ now()->format('Y/m/d H:i') }}</p>
                        <p class="mb-1"><strong>الفترة:</strong> من {{ $dateFrom->format('Y/m/d') }} إلى {{ $dateTo->format('Y/m/d') }}</p>
                        <p class="mb-0"><strong>إجمالي التفتيشات:</strong> {{ $inspections->count() }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">ملاحظات</h6>
                        <ul class="mb-0">
                            <li>معدل الموافقة يشمل التفتيشات المعتمدة والمشروطة</li>
                            <li>التفتيشات المنتهية الصلاحية تظهر بتنبيه أحمر</li>
                            <li>يمكن تصدير التقرير إلى Excel للمعالجة الإضافية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // يمكن تطوير هذه الدالة لاحقاً لتصدير البيانات إلى Excel
    alert('ميزة التصدير إلى Excel ستكون متاحة قريباً');
}

// طباعة التقرير
@media print {
    .btn-group, .card-header .btn, .no-print {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}
</script>
@endsection
