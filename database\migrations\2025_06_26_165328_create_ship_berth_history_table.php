<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_berth_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained('ships')->onDelete('cascade'); // السفينة
            $table->foreignId('berth_id')->constrained('berths'); // الرصيف
            $table->datetime('berthing_start'); // بداية الرسو
            $table->datetime('berthing_end')->nullable(); // نهاية الرسو
            $table->integer('berthing_hours')->default(0); // عدد ساعات الرسو
            $table->decimal('hourly_rate', 8, 2); // التعريفة الساعية
            $table->decimal('total_berthing_cost', 10, 2)->default(0); // إجمالي تكلفة الرسو
            $table->enum('status', ['active', 'completed'])->default('active'); // حالة الرسو
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_berth_history');
    }
};
