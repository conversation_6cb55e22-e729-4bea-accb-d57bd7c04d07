@extends('layouts.port-app')

@section('page-title', 'تفاصيل التفتيش')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('inspections.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
    @can('inspections.edit')
    <a href="{{ route('inspections.edit', $inspection) }}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i>
        تعديل
    </a>
    @endcan
    @can('inspections.delete')
    <form action="{{ route('inspections.destroy', $inspection) }}" method="POST" class="d-inline"
          onsubmit="return confirm('هل أنت متأكد من حذف هذا التفتيش؟')">
        @csrf
        @method('DELETE')
        <button type="submit" class="btn btn-danger">
            <i class="fas fa-trash me-2"></i>
            حذف
        </button>
    </form>
    @endcan
</div>
@endsection

@section('content')
<div class="row">
    <!-- معلومات التفتيش الأساسية -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="{{ $inspection->inspection_icon }} me-2"></i>
                    تفتيش {{ $inspection->inspection_type_label }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">معلومات السفينة</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">اسم السفينة:</td>
                                <td>
                                    <a href="{{ route('ships.show', $inspection->ship) }}" class="text-decoration-none">
                                        {{ $inspection->ship->name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">رقم IMO:</td>
                                <td>{{ $inspection->ship->imo_number ?? 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الجنسية:</td>
                                <td>{{ $inspection->ship->flag }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">نوع السفينة:</td>
                                <td>{{ $inspection->ship->ship_type }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الوكيل الملاحي:</td>
                                <td>{{ $inspection->ship->agent->name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">معلومات التفتيش</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">نوع التفتيش:</td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <i class="{{ $inspection->inspection_icon }} me-1"></i>
                                        {{ $inspection->inspection_type_label }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">تاريخ التفتيش:</td>
                                <td>{{ $inspection->inspection_date->format('Y/m/d H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">المفتش:</td>
                                <td>{{ $inspection->inspector_name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الجهة المفتشة:</td>
                                <td>{{ $inspection->inspector_authority }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">النتيجة:</td>
                                <td>
                                    <span class="badge bg-{{ $inspection->result_color }}">
                                        {{ $inspection->result_label }}
                                    </span>
                                </td>
                            </tr>
                            @if($inspection->valid_until)
                            <tr>
                                <td class="fw-bold">صالح حتى:</td>
                                <td>
                                    {{ $inspection->valid_until->format('Y/m/d') }}
                                    @if(!$inspection->is_valid)
                                        <span class="badge bg-danger ms-2">منتهي الصلاحية</span>
                                    @else
                                        <span class="badge bg-success ms-2">صالح</span>
                                    @endif
                                </td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>

                @if($inspection->notes)
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="text-primary mb-3">ملاحظات</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ $inspection->notes }}
                        </div>
                    </div>
                </div>
                @endif

                @if($inspection->violations)
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="text-danger mb-3">المخالفات</h6>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ $inspection->violations }}
                        </div>
                    </div>
                </div>
                @endif

                @if($inspection->conditions)
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="text-warning mb-3">الشروط المطلوبة</h6>
                        <div class="alert alert-warning">
                            <i class="fas fa-list-ul me-2"></i>
                            {{ $inspection->conditions }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- حمولات السفينة -->
        @if($inspection->ship->cargos->count() > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    حمولات السفينة ({{ $inspection->ship->cargos->count() }})
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>نوع الحمولة</th>
                                <th>الوصف</th>
                                <th>الكمية</th>
                                <th>الوزن</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($inspection->ship->cargos as $cargo)
                            <tr>
                                <td>
                                    <span class="badge bg-light text-dark">{{ $cargo->cargo_type }}</span>
                                </td>
                                <td>
                                    <a href="{{ route('cargos.show', $cargo) }}" class="text-decoration-none">
                                        {{ Str::limit($cargo->description, 30) }}
                                    </a>
                                </td>
                                <td>{{ number_format($cargo->quantity, 2) }} {{ $cargo->unit }}</td>
                                <td>{{ $cargo->weight ? number_format($cargo->weight, 2) . ' طن' : 'غير محدد' }}</td>
                                <td>
                                    @if($cargo->value)
                                        {{ number_format($cargo->value, 2) }} {{ $cargo->currency }}
                                    @else
                                        غير محدد
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ $cargo->status_color }}">
                                        {{ $cargo->status_label }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-md-4">
        <!-- تحديث سريع للنتيجة -->
        @can('inspections.edit')
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تحديث سريع
                </h6>
            </div>
            <div class="card-body">
                <form action="{{ route('inspections.update-result', $inspection) }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="result" class="form-label">النتيجة</label>
                        <select class="form-select" id="result" name="result" required>
                            <option value="approved" {{ $inspection->result == 'approved' ? 'selected' : '' }}>موافق</option>
                            <option value="rejected" {{ $inspection->result == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                            <option value="conditional" {{ $inspection->result == 'conditional' ? 'selected' : '' }}>موافق مشروط</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="valid_until" class="form-label">صالح حتى</label>
                        <input type="date" class="form-control" id="valid_until" name="valid_until" 
                               value="{{ $inspection->valid_until ? $inspection->valid_until->format('Y-m-d') : '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ $inspection->notes }}</textarea>
                    </div>
                    <button type="submit" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-save me-2"></i>
                        تحديث
                    </button>
                </form>
            </div>
        </div>
        @endcan

        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="fw-bold">تاريخ الإنشاء:</td>
                        <td>{{ $inspection->created_at->format('Y/m/d H:i') }}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">آخر تحديث:</td>
                        <td>{{ $inspection->updated_at->format('Y/m/d H:i') }}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">مدة التفتيش:</td>
                        <td>{{ $inspection->inspection_date->diffForHumans() }}</td>
                    </tr>
                    @if($inspection->valid_until)
                    <tr>
                        <td class="fw-bold">انتهاء الصلاحية:</td>
                        <td>{{ $inspection->valid_until->diffForHumans() }}</td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>

        <!-- تفتيشات أخرى لنفس السفينة -->
        @php
            $otherInspections = $inspection->ship->inspections()
                ->where('id', '!=', $inspection->id)
                ->orderBy('inspection_date', 'desc')
                ->limit(5)
                ->get();
        @endphp
        
        @if($otherInspections->count() > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    تفتيشات أخرى لنفس السفينة
                </h6>
            </div>
            <div class="card-body">
                @foreach($otherInspections as $otherInspection)
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <span class="badge bg-light text-dark">
                            <i class="{{ $otherInspection->inspection_icon }} me-1"></i>
                            {{ $otherInspection->inspection_type_label }}
                        </span>
                        <br>
                        <small class="text-muted">{{ $otherInspection->inspection_date->format('Y/m/d') }}</small>
                    </div>
                    <div>
                        <span class="badge bg-{{ $otherInspection->result_color }}">
                            {{ $otherInspection->result_label }}
                        </span>
                        <br>
                        <a href="{{ route('inspections.show', $otherInspection) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                @if(!$loop->last)
                <hr>
                @endif
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
