<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ship;
use App\Models\Agent;
use App\Models\Berth;
use App\Models\Cargo;
use App\Models\Invoice;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth();

        // إحصائيات عامة
        $stats = [
            'total_ships' => Ship::count(),
            'ships_in_port' => Ship::whereIn('status', ['outside_port', 'berthed'])->count(),
            'ships_berthed' => Ship::where('status', 'berthed')->count(),
            'available_berths' => Berth::where('status', 'available')->count(),
            'total_agents' => Agent::where('is_active', true)->count(),
            'pending_invoices' => Invoice::where('status', 'sent')->count(),
            'monthly_revenue' => Invoice::where('status', 'paid')
                ->whereMonth('created_at', Carbon::now()->month)
                ->sum('total_amount')
        ];

        // إحصائيات الفواتير
        $invoiceStats = [
            'today_invoices' => Invoice::whereDate('invoice_date', $today)->count(),
            'today_amount' => Invoice::whereDate('invoice_date', $today)->sum('total_amount'),
            'month_invoices' => Invoice::where('invoice_date', '>=', $thisMonth)->count(),
            'month_amount' => Invoice::where('invoice_date', '>=', $thisMonth)->sum('total_amount'),
            'month_paid' => Invoice::where('invoice_date', '>=', $thisMonth)->sum('paid_amount'),
            'overdue_count' => Invoice::where('due_date', '<', Carbon::now())
                ->where('status', '!=', 'paid')
                ->where('status', '!=', 'cancelled')
                ->count(),
            'overdue_amount' => Invoice::where('due_date', '<', Carbon::now())
                ->where('status', '!=', 'paid')
                ->where('status', '!=', 'cancelled')
                ->sum('total_amount'),
        ];

        // إحصائيات الأرصفة
        $berthStats = [
            'total_berths' => Berth::count(),
            'occupied_berths' => Berth::where('status', 'occupied')->count(),
            'available_berths' => Berth::where('status', 'available')->count(),
            'maintenance_berths' => Berth::where('status', 'maintenance')->count(),
        ];

        // السفن المتوقع وصولها اليوم
        $expected_arrivals = Ship::where('status', 'announced')
            ->whereDate('expected_arrival_date', $today)
            ->with('agent')
            ->get();

        // السفن الموجودة في الميناء
        $ships_in_port = Ship::whereIn('status', ['outside_port', 'berthed'])
            ->with(['agent', 'currentBerth.berth'])
            ->orderBy('actual_arrival_date', 'desc')
            ->take(10)
            ->get();

        // الفواتير المتأخرة الأحدث
        $overdue_invoices = Invoice::where('due_date', '<', Carbon::now())
            ->where('status', '!=', 'paid')
            ->where('status', '!=', 'cancelled')
            ->with(['ship', 'agent'])
            ->orderBy('due_date', 'asc')
            ->take(5)
            ->get();

        // أحدث الأنشطة
        $recent_activities = [
            'recent_ships' => Ship::with('agent')->orderBy('created_at', 'desc')->take(5)->get(),
            'recent_invoices' => Invoice::with(['ship', 'agent'])->orderBy('created_at', 'desc')->take(5)->get(),
        ];

        // مقارنة مع الشهر الماضي
        $lastMonthStats = [
            'invoices' => Invoice::whereBetween('invoice_date', [$lastMonth->startOfMonth(), $lastMonth->endOfMonth()])->count(),
            'amount' => Invoice::whereBetween('invoice_date', [$lastMonth->startOfMonth(), $lastMonth->endOfMonth()])->sum('total_amount'),
            'ships' => Ship::whereBetween('created_at', [$lastMonth->startOfMonth(), $lastMonth->endOfMonth()])->count(),
        ];

        return view('dashboard.index', compact(
            'stats', 
            'invoiceStats', 
            'berthStats', 
            'expected_arrivals', 
            'ships_in_port', 
            'overdue_invoices',
            'recent_activities',
            'lastMonthStats'
        ));
    }
}
