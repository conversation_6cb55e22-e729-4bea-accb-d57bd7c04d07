@extends('layouts.port-app')

@section('page-title', 'إدارة الحمولات')

@section('page-actions')
<div class="btn-group">
    @can('cargos.create')
    <a href="{{ route('cargos.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة حمولة جديدة
    </a>
    @endcan
    <a href="{{ route('cargos.export.excel') }}" class="btn btn-outline-success">
        <i class="fas fa-file-excel me-2"></i>
        تصدير Excel
    </a>
    <a href="{{ route('cargos.advanced-report') }}" class="btn btn-outline-info">
        <i class="fas fa-chart-line me-2"></i>
        تقرير متقدم
    </a>
    <a href="{{ route('cargos.tracking-report') }}" class="btn btn-outline-secondary">
        <i class="fas fa-route me-2"></i>
        تتبع الحمولات
    </a>
</div>
@endsection

@section('content')
<!-- إحصائيات شاملة محسنة -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">إجمالي الحمولات</h6>
                        <h3 class="mb-0">{{ number_format($stats['total_cargos'] ?? 0) }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">الوزن الإجمالي</h6>
                        <h3 class="mb-0">{{ number_format($stats['total_weight'] ?? 0, 1) }}</h3>
                        <small>طن</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-weight fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">الحجم الإجمالي</h6>
                        <h3 class="mb-0">{{ number_format($stats['total_volume'] ?? 0, 1) }}</h3>
                        <small>م³</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cube fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">القيمة الإجمالية</h6>
                        <h3 class="mb-0">{{ number_format($stats['total_value'] ?? 0, 0) }}</h3>
                        <small>ل.س</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">قيد التحميل</h6>
                        <h3 class="mb-0">{{ $stats['loading'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-upload fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مخزنة</h6>
                        <h3 class="mb-0">{{ $stats['stored'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-warehouse fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات حسب النوع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">حمولة عامة</h6>
                        <h3 class="mb-0">{{ $stats['general_cargo'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">حاويات</h6>
                        <h3 class="mb-0">{{ $stats['container_cargo'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shipping-fast fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">حمولة سائبة</h6>
                        <h3 class="mb-0">{{ $stats['bulk_cargo'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-mountain fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">حمولة خطرة</h6>
                        <h3 class="mb-0">{{ $stats['dangerous_cargo'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- شريط البحث والفلترة -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('cargos.index') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في الوصف أو بلد المنشأ..." 
                               value="{{ request('search') }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="cargo_type" onchange="this.form.submit()">
                        <option value="">جميع الأنواع</option>
                        <option value="container" {{ request('cargo_type') == 'container' ? 'selected' : '' }}>حاويات</option>
                        <option value="bulk" {{ request('cargo_type') == 'bulk' ? 'selected' : '' }}>سائبة</option>
                        <option value="liquid" {{ request('cargo_type') == 'liquid' ? 'selected' : '' }}>سوائل</option>
                        <option value="gas" {{ request('cargo_type') == 'gas' ? 'selected' : '' }}>غازات</option>
                        <option value="vehicles" {{ request('cargo_type') == 'vehicles' ? 'selected' : '' }}>مركبات</option>
                        <option value="general" {{ request('cargo_type') == 'general' ? 'selected' : '' }}>عامة</option>
                        <option value="dangerous" {{ request('cargo_type') == 'dangerous' ? 'selected' : '' }}>خطرة</option>
                        <option value="refrigerated" {{ request('cargo_type') == 'refrigerated' ? 'selected' : '' }}>مبردة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status" onchange="this.form.submit()">
                        <option value="">جميع الحالات</option>
                        <option value="loading" {{ request('status') == 'loading' ? 'selected' : '' }}>قيد التحميل</option>
                        <option value="loaded" {{ request('status') == 'loaded' ? 'selected' : '' }}>محملة</option>
                        <option value="in_transit" {{ request('status') == 'in_transit' ? 'selected' : '' }}>في الطريق</option>
                        <option value="unloading" {{ request('status') == 'unloading' ? 'selected' : '' }}>قيد التفريغ</option>
                        <option value="unloaded" {{ request('status') == 'unloaded' ? 'selected' : '' }}>مفرغة</option>
                        <option value="stored" {{ request('status') == 'stored' ? 'selected' : '' }}>مخزنة</option>
                        <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>مسلمة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="ship_id" onchange="this.form.submit()">
                        <option value="">جميع السفن</option>
                        @foreach($ships as $ship)
                        <option value="{{ $ship->id }}" {{ request('ship_id') == $ship->id ? 'selected' : '' }}>
                            {{ $ship->name }}
                        </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <a href="{{ route('cargos.index') }}" class="btn btn-outline-secondary w-100" title="إعادة تعيين">
                        <i class="fas fa-redo me-2"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول الحمولات -->
<div class="card">
    @if($cargos->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>السفينة</th>
                        <th>نوع الحمولة</th>
                        <th>الوصف</th>
                        <th>الوزن/الحجم</th>
                        <th>المنشأ/الوجهة</th>
                        <th>الحالة</th>
                        <th>القيمة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($cargos as $cargo)
                    <tr>
                        <td>
                            <input type="checkbox" name="selected_cargos[]" value="{{ $cargo->id }}" class="cargo-checkbox">
                        </td>
                        <td>
                            <a href="{{ route('ships.show', $cargo->ship) }}" class="text-decoration-none">
                                <strong>{{ $cargo->ship->name }}</strong>
                            </a>
                            <br>
                            <small class="text-muted">{{ $cargo->ship->agent->name ?? 'بدون وكيل' }}</small>
                        </td>
                        <td>
                            @switch($cargo->cargo_type)
                                @case('container')
                                    <span class="badge bg-primary">حاويات</span>
                                    @break
                                @case('bulk')
                                    <span class="badge bg-warning">سائبة</span>
                                    @break
                                @case('liquid')
                                    <span class="badge bg-info">سوائل</span>
                                    @break
                                @case('dangerous')
                                    <span class="badge bg-danger">خطرة</span>
                                    @break
                                @case('general')
                                    <span class="badge bg-secondary">عامة</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $cargo->cargo_type }}</span>
                            @endswitch
                        </td>
                        <td>
                            {{ Str::limit($cargo->description, 50) }}
                            @if($cargo->notes)
                                <br><small class="text-muted">{{ Str::limit($cargo->notes, 30) }}</small>
                            @endif
                        </td>
                        <td>
                            <strong>{{ number_format($cargo->weight, 1) }} طن</strong>
                            <br>
                            <small class="text-muted">{{ number_format($cargo->volume, 1) }} م³</small>
                        </td>
                        <td>
                            <strong>{{ $cargo->origin_country }}</strong>
                            <br>
                            <small class="text-muted">إلى: {{ $cargo->destination_country }}</small>
                        </td>
                        <td>
                            @switch($cargo->status)
                                @case('loading')
                                    <span class="badge bg-warning">قيد التحميل</span>
                                    @break
                                @case('loaded')
                                    <span class="badge bg-success">محملة</span>
                                    @break
                                @case('unloading')
                                    <span class="badge bg-info">قيد التفريغ</span>
                                    @break
                                @case('stored')
                                    <span class="badge bg-secondary">مخزنة</span>
                                    @break
                                @case('delivered')
                                    <span class="badge bg-primary">مسلمة</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $cargo->status }}</span>
                            @endswitch
                        </td>
                        <td>
                            @if($cargo->value)
                                <strong class="text-success">{{ number_format($cargo->value, 0) }} ل.س</strong>
                            @else
                                <span class="text-muted">غير محدد</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                @can('cargos.view')
                                <a href="{{ route('cargos.show', $cargo) }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endcan
                                @can('cargos.edit')
                                <a href="{{ route('cargos.edit', $cargo) }}" class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endcan
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown" title="المزيد">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        @can('cargos.edit')
                                        <li>
                                            <button type="button" class="dropdown-item" 
                                                    onclick="updateStatus({{ $cargo->id }})">
                                                <i class="fas fa-sync me-2"></i>
                                                تحديث الحالة
                                            </button>
                                        </li>
                                        @endcan
                                        @can('cargos.delete')
                                        <li>
                                            <button type="button" class="dropdown-item text-danger" 
                                                    onclick="confirmDelete({{ $cargo->id }}, '{{ $cargo->description }}')">
                                                <i class="fas fa-trash me-2"></i>
                                                حذف
                                            </button>
                                        </li>
                                        @endcan
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer">
            {{ $cargos->appends(request()->query())->links() }}
        </div>
    @else
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد حمولات</h5>
            <p class="text-muted">لم يتم العثور على أي حمولات مطابقة للبحث</p>
            @can('cargos.create')
            <a href="{{ route('cargos.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة حمولة جديدة
            </a>
            @endcan
        </div>
    @endif
</div>

<!-- مودال تحديث الحالة -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الحمولة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="updateStatusForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة الجديدة</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="loading">قيد التحميل</option>
                            <option value="loaded">محملة</option>
                            <option value="in_transit">في الطريق</option>
                            <option value="unloading">قيد التفريغ</option>
                            <option value="unloaded">مفرغة</option>
                            <option value="stored">مخزنة</option>
                            <option value="delivered">مسلمة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="status_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="status_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الحمولة: <strong id="cargoDescription"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateStatus(cargoId) {
    document.getElementById('updateStatusForm').action = `/cargos/${cargoId}/update-status`;
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

function confirmDelete(cargoId, cargoDescription) {
    document.getElementById('cargoDescription').textContent = cargoDescription;
    document.getElementById('deleteForm').action = `/cargos/${cargoId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.cargo-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}
</script>
@endpush
