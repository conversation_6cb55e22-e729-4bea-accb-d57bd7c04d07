@extends('layouts.port-app')

@section('page-title', 'تفاصيل المستودع')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('warehouses.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
    @can('warehouses.edit')
    <a href="{{ route('warehouses.edit', $warehouse) }}" class="btn btn-primary">
        <i class="fas fa-edit me-2"></i>
        تعديل
    </a>
    @endcan
    @can('warehouses.delete')
    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
        <i class="fas fa-trash me-2"></i>
        حذف
    </button>
    @endcan
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8">
        <!-- معلومات المستودع الأساسية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المستودع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">اسم المستودع:</td>
                                <td>{{ $warehouse->name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الرمز:</td>
                                <td><span class="badge bg-primary">{{ $warehouse->code }}</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">النوع:</td>
                                <td>
                                    <span class="badge bg-info fs-6">{{ $warehouse->type_label }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الموقع:</td>
                                <td>{{ $warehouse->location ?? 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">التغطية:</td>
                                <td>
                                    @if($warehouse->is_covered)
                                        <span class="badge bg-success">مغطى</span>
                                    @else
                                        <span class="badge bg-secondary">مكشوف</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">المساحة الإجمالية:</td>
                                <td>{{ number_format($warehouse->total_area, 0) }} م²</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">المساحة المتاحة:</td>
                                <td class="text-success">{{ number_format($warehouse->available_area, 0) }} م²</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">المساحة المستغلة:</td>
                                <td class="text-warning">{{ number_format($warehouse->used_area, 0) }} م²</td>
                            </tr>
                            @if($warehouse->max_capacity)
                            <tr>
                                <td class="fw-bold text-muted">السعة القصوى:</td>
                                <td>{{ number_format($warehouse->max_capacity, 0) }} طن</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">السعة الحالية:</td>
                                <td>{{ number_format($warehouse->current_capacity, 0) }} طن</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>

                @if($warehouse->description)
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-primary">الوصف:</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $warehouse->description }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- معلومات المدير -->
        @if($warehouse->manager_name)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-tie me-2"></i>
                    معلومات المدير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">اسم المدير:</td>
                                <td>{{ $warehouse->manager_name }}</td>
                            </tr>
                            @if($warehouse->manager_phone)
                            <tr>
                                <td class="fw-bold text-muted">رقم الهاتف:</td>
                                <td>
                                    <a href="tel:{{ $warehouse->manager_phone }}" class="text-decoration-none">
                                        {{ $warehouse->manager_phone }}
                                    </a>
                                </td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- الحمولات المخزنة حالياً -->
        @if($currentCargos->count() > 0)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    الحمولات المخزنة حالياً
                    <span class="badge bg-primary ms-2">{{ $currentCargos->count() }}</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الحمولة</th>
                                <th>السفينة</th>
                                <th>الكمية</th>
                                <th>تاريخ الدخول</th>
                                <th>المدة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($currentCargos as $storage)
                            <tr>
                                <td>
                                    <a href="{{ route('cargos.show', $storage->cargo) }}" class="text-decoration-none">
                                        {{ Str::limit($storage->cargo->description, 30) }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ $storage->cargo->cargo_type_label }}</small>
                                </td>
                                <td>
                                    <a href="{{ route('ships.show', $storage->cargo->ship) }}" class="text-decoration-none">
                                        {{ $storage->cargo->ship->name }}
                                    </a>
                                    @if($storage->cargo->ship->agent)
                                        <br>
                                        <small class="text-muted">{{ $storage->cargo->ship->agent->name }}</small>
                                    @endif
                                </td>
                                <td>
                                    <strong>{{ number_format($storage->quantity, 2) }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $storage->cargo->unit_label }}</small>
                                </td>
                                <td>
                                    @if($storage->entry_date)
                                        {{ $storage->entry_date->format('Y/m/d') }}
                                        <br>
                                        <small class="text-muted">{{ $storage->entry_date->format('H:i') }}</small>
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                                <td>
                                    @if($storage->entry_date)
                                        {{ $storage->entry_date->diffForHumans() }}
                                    @else
                                        <span class="text-muted">--</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('cargos.show', $storage->cargo) }}" class="btn btn-outline-info" title="عرض الحمولة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- النشاط الأخير -->
        @if($recentActivity->count() > 0)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاط الأخير (آخر 30 يوم)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>الحمولة</th>
                                <th>السفينة</th>
                                <th>العملية</th>
                                <th>الكمية</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentActivity as $activity)
                            <tr>
                                <td>
                                    {{ $activity->created_at->format('m/d') }}
                                    <br>
                                    <small class="text-muted">{{ $activity->created_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    <a href="{{ route('cargos.show', $activity->cargo) }}" class="text-decoration-none">
                                        {{ Str::limit($activity->cargo->description, 25) }}
                                    </a>
                                </td>
                                <td>{{ $activity->cargo->ship->name }}</td>
                                <td>
                                    @if($activity->storage_end)
                                        <span class="badge bg-danger">خروج</span>
                                    @else
                                        <span class="badge bg-success">دخول</span>
                                    @endif
                                </td>
                                <td>{{ number_format($activity->stored_quantity, 0) }} {{ $activity->cargo->unit_label }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-md-4">
        <!-- حالة المستودع -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    حالة المستودع
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    @switch($warehouse->status)
                        @case('active')
                            <span class="badge bg-success fs-5">{{ $warehouse->status_label }}</span>
                            @break
                        @case('inactive')
                            <span class="badge bg-secondary fs-5">{{ $warehouse->status_label }}</span>
                            @break
                        @case('maintenance')
                            <span class="badge bg-warning fs-5">{{ $warehouse->status_label }}</span>
                            @break
                        @case('full')
                            <span class="badge bg-danger fs-5">{{ $warehouse->status_label }}</span>
                            @break
                        @default
                            <span class="badge bg-light text-dark fs-5">{{ $warehouse->status_label }}</span>
                    @endswitch
                </div>

                @can('warehouses.edit')
                <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#statusModal">
                    <i class="fas fa-edit me-1"></i>
                    تحديث الحالة
                </button>
                @endcan
            </div>
        </div>

        <!-- إحصائيات الاستغلال -->
        @if($warehouse->max_capacity)
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    استغلال السعة
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar" role="progressbar"
                             style="width: {{ $warehouse->occupancy_percentage }}%"
                             aria-valuenow="{{ $warehouse->occupancy_percentage }}"
                             aria-valuemin="0" aria-valuemax="100">
                            {{ number_format($warehouse->occupancy_percentage, 1) }}%
                        </div>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary mb-1">{{ number_format($warehouse->current_capacity, 0) }}</h6>
                            <small class="text-muted">السعة الحالية</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success mb-1">{{ number_format($warehouse->max_capacity, 0) }}</h6>
                        <small class="text-muted">السعة القصوى</small>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- إحصائيات المساحة -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-expand-arrows-alt me-2"></i>
                    استغلال المساحة
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar bg-info" role="progressbar"
                             style="width: {{ $warehouse->area_occupancy_percentage }}%"
                             aria-valuenow="{{ $warehouse->area_occupancy_percentage }}"
                             aria-valuemin="0" aria-valuemax="100">
                            {{ number_format($warehouse->area_occupancy_percentage, 1) }}%
                        </div>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-4">
                        <h6 class="text-info mb-1">{{ number_format($warehouse->total_area, 0) }}</h6>
                        <small class="text-muted">إجمالي</small>
                    </div>
                    <div class="col-4">
                        <h6 class="text-warning mb-1">{{ number_format($warehouse->used_area, 0) }}</h6>
                        <small class="text-muted">مستغل</small>
                    </div>
                    <div class="col-4">
                        <h6 class="text-success mb-1">{{ number_format($warehouse->available_area, 0) }}</h6>
                        <small class="text-muted">متاح</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- التعريفة -->
        @if($warehouse->daily_rate > 0)
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-dollar-sign me-2"></i>
                    التعريفة
                </h6>
            </div>
            <div class="card-body text-center">
                <h4 class="text-primary mb-1">${{ number_format($warehouse->daily_rate, 2) }}</h4>
                <small class="text-muted">في اليوم</small>
            </div>
        </div>
        @endif

        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted d-block mb-2">
                    <i class="fas fa-plus me-1"></i>
                    تم الإنشاء: {{ $warehouse->created_at->format('Y/m/d H:i') }}
                </small>
                <small class="text-muted d-block">
                    <i class="fas fa-edit me-1"></i>
                    آخر تحديث: {{ $warehouse->updated_at->format('Y/m/d H:i') }}
                </small>

                @if($currentCargos->count() > 0)
                <hr>
                <small class="text-info d-block">
                    <i class="fas fa-boxes me-1"></i>
                    {{ $currentCargos->count() }} حمولة مخزنة حالياً
                </small>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- نموذج تحديث الحالة -->
@can('warehouses.edit')
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة المستودع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('warehouses.update-status', $warehouse) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة الجديدة</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" {{ $warehouse->status == 'active' ? 'selected' : '' }}>نشط</option>
                            <option value="inactive" {{ $warehouse->status == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                            <option value="maintenance" {{ $warehouse->status == 'maintenance' ? 'selected' : '' }}>قيد الصيانة</option>
                            <option value="full" {{ $warehouse->status == 'full' ? 'selected' : '' }}>ممتلئ</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan

<!-- نموذج تأكيد الحذف -->
@can('warehouses.delete')
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المستودع؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ route('warehouses.destroy', $warehouse) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
