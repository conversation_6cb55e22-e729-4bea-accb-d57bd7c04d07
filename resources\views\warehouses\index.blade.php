@extends('layouts.port-app')

@section('page-title', 'إدارة المستودعات')

@section('page-actions')
<div class="btn-group">
    @can('warehouses.create')
    <a href="{{ route('warehouses.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة مستودع جديد
    </a>
    @endcan
    <a href="{{ route('warehouses.occupancy-report') }}" class="btn btn-outline-info">
        <i class="fas fa-chart-pie me-2"></i>
        تقرير الاستغلال
    </a>
    <a href="{{ route('warehouses.export.excel') }}" class="btn btn-outline-success">
        <i class="fas fa-file-excel me-2"></i>
        تصدير Excel
    </a>
    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filterModal">
        <i class="fas fa-filter me-2"></i>
        تصفية
    </button>
</div>
@endsection

@section('content')
<!-- إحصائيات سريعة محسنة -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">إجمالي المستودعات</h6>
                        <h3 class="mb-0">{{ $stats['total_warehouses'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-warehouse fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">المستودعات النشطة</h6>
                        <h3 class="mb-0">{{ $stats['active'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">المساحة الإجمالية</h6>
                        <h3 class="mb-0">{{ number_format($stats['total_area'], 0) }}</h3>
                        <small>م²</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-expand-arrows-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">المساحة المتاحة</h6>
                        <h3 class="mb-0">{{ number_format($stats['available_area'], 0) }}</h3>
                        <small>م²</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-square fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">نسبة الاستغلال</h6>
                        <h3 class="mb-0">{{ number_format($stats['occupancy_rate'], 1) }}%</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-pie fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-dark text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مستودعات مغطاة</h6>
                        <h3 class="mb-0">{{ $stats['covered'] ?? 0 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
</div>

<!-- شريط البحث والفلاتر -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('warehouses.index') }}">
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في الاسم أو الرمز أو الموقع..." 
                               value="{{ request('search') }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="type" onchange="this.form.submit()">
                        <option value="">جميع الأنواع</option>
                        <option value="warehouse" {{ request('type') == 'warehouse' ? 'selected' : '' }}>مستودع</option>
                        <option value="yard" {{ request('type') == 'yard' ? 'selected' : '' }}>ساحة</option>
                        <option value="cold_storage" {{ request('type') == 'cold_storage' ? 'selected' : '' }}>تبريد</option>
                        <option value="tank" {{ request('type') == 'tank' ? 'selected' : '' }}>خزان</option>
                        <option value="silo" {{ request('type') == 'silo' ? 'selected' : '' }}>صومعة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status" onchange="this.form.submit()">
                        <option value="">جميع الحالات</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                        <option value="maintenance" {{ request('status') == 'maintenance' ? 'selected' : '' }}>قيد الصيانة</option>
                        <option value="full" {{ request('status') == 'full' ? 'selected' : '' }}>ممتلئ</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="is_covered" onchange="this.form.submit()">
                        <option value="">جميع الأنواع</option>
                        <option value="1" {{ request('is_covered') == '1' ? 'selected' : '' }}>مغطى</option>
                        <option value="0" {{ request('is_covered') == '0' ? 'selected' : '' }}>مكشوف</option>
                    </select>
                </div>
                <div class="col-md-2">
                    @if(request()->hasAny(['search', 'type', 'status', 'is_covered']))
                        <a href="{{ route('warehouses.index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i>
                            مسح
                        </a>
                    @endif
                </div>
            </div>
        </form>
    </div>
</div>

<!-- بطاقات المستودعات -->
@if($warehouses->count() > 0)
<div class="row">
    @foreach($warehouses as $warehouse)
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100 warehouse-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-{{ $warehouse->type == 'warehouse' ? 'warehouse' : ($warehouse->type == 'yard' ? 'square' : 'building') }} me-2"></i>
                    {{ $warehouse->name }}
                </h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        @can('warehouses.view')
                        <li><a class="dropdown-item" href="{{ route('warehouses.show', $warehouse) }}">
                            <i class="fas fa-eye me-2"></i>عرض التفاصيل
                        </a></li>
                        @endcan
                        @can('warehouses.edit')
                        <li><a class="dropdown-item" href="{{ route('warehouses.edit', $warehouse) }}">
                            <i class="fas fa-edit me-2"></i>تعديل
                        </a></li>
                        @endcan
                        @can('warehouses.delete')
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ $warehouse->id }}', '{{ $warehouse->name }}')">
                            <i class="fas fa-trash me-2"></i>حذف
                        </a></li>
                        @endcan
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">الرمز:</small>
                        <div class="fw-bold">{{ $warehouse->code }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">النوع:</small>
                        <div>
                            <span class="badge bg-info">{{ $warehouse->type_label }}</span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">المساحة الإجمالية:</small>
                        <div class="fw-bold">{{ number_format($warehouse->total_area, 0) }} م²</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">المساحة المتاحة:</small>
                        <div class="fw-bold text-success">{{ number_format($warehouse->available_area, 0) }} م²</div>
                    </div>
                </div>

                @if($warehouse->max_capacity)
                <div class="mb-3">
                    <small class="text-muted">السعة:</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {{ $warehouse->occupancy_percentage }}%"
                             aria-valuenow="{{ $warehouse->occupancy_percentage }}" 
                             aria-valuemin="0" aria-valuemax="100">
                            {{ number_format($warehouse->occupancy_percentage, 1) }}%
                        </div>
                    </div>
                    <small class="text-muted">
                        {{ number_format($warehouse->current_capacity, 0) }} / {{ number_format($warehouse->max_capacity, 0) }}
                    </small>
                </div>
                @endif

                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">الحالة:</small>
                        <div>
                            @switch($warehouse->status)
                                @case('active')
                                    <span class="badge bg-success">{{ $warehouse->status_label }}</span>
                                    @break
                                @case('inactive')
                                    <span class="badge bg-secondary">{{ $warehouse->status_label }}</span>
                                    @break
                                @case('maintenance')
                                    <span class="badge bg-warning">{{ $warehouse->status_label }}</span>
                                    @break
                                @case('full')
                                    <span class="badge bg-danger">{{ $warehouse->status_label }}</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $warehouse->status_label }}</span>
                            @endswitch
                        </div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">التغطية:</small>
                        <div>
                            @if($warehouse->is_covered)
                                <span class="badge bg-primary">مغطى</span>
                            @else
                                <span class="badge bg-secondary">مكشوف</span>
                            @endif
                        </div>
                    </div>
                </div>

                @if($warehouse->location)
                <div class="mb-3">
                    <small class="text-muted">الموقع:</small>
                    <div class="text-truncate">{{ $warehouse->location }}</div>
                </div>
                @endif

                @if($warehouse->daily_rate > 0)
                <div class="mb-3">
                    <small class="text-muted">التعريفة اليومية:</small>
                    <div class="fw-bold text-primary">${{ number_format($warehouse->daily_rate, 2) }}</div>
                </div>
                @endif

                @if($warehouse->manager_name)
                <div class="mb-3">
                    <small class="text-muted">المدير:</small>
                    <div>{{ $warehouse->manager_name }}</div>
                    @if($warehouse->manager_phone)
                        <small class="text-muted">{{ $warehouse->manager_phone }}</small>
                    @endif
                </div>
                @endif

                <!-- الحمولات المخزنة حالياً -->
                @php
                    $currentCargosCount = $warehouse->currentCargos()->count();
                @endphp
                @if($currentCargosCount > 0)
                <div class="alert alert-info py-2 mb-0">
                    <small>
                        <i class="fas fa-boxes me-1"></i>
                        {{ $currentCargosCount }} حمولة مخزنة حالياً
                    </small>
                </div>
                @endif
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    @can('warehouses.view')
                    <a href="{{ route('warehouses.show', $warehouse) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        عرض التفاصيل
                    </a>
                    @endcan
                    @can('warehouses.edit')
                    <a href="{{ route('warehouses.edit', $warehouse) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>

<!-- Pagination -->
<div class="d-flex justify-content-center">
    {{ $warehouses->appends(request()->query())->links() }}
</div>
@else
<div class="text-center py-5">
    <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">لا توجد مستودعات</h5>
    <p class="text-muted">لم يتم العثور على أي مستودعات مطابقة للبحث</p>
    @can('warehouses.create')
    <a href="{{ route('warehouses.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة مستودع جديد
    </a>
    @endcan
</div>
@endif

<!-- نموذج تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستودع: <strong id="warehouseName"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.warehouse-card {
    transition: transform 0.2s;
}

.warehouse-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(45deg, #007bff, #0056b3);
}
</style>
@endpush

@push('scripts')
<script>
function confirmDelete(warehouseId, warehouseName) {
    document.getElementById('warehouseName').textContent = warehouseName;
    document.getElementById('deleteForm').action = `/warehouses/${warehouseId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
