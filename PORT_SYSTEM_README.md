# نظام إدارة المرفأ البحري

نظام إلكتروني متكامل لإدارة عمل المرفأ البحري من لحظة وصول السفينة حتى مغادرتها، مع متابعة كل العمليات التشغيلية، المالية، اللوجستية، وتوليد تقارير رسمية مشابهة للتقارير اليومية المستخدمة في المرافئ.

## 🚀 المميزات الرئيسية

### 📋 الوحدات المتاحة

1. **وحدة السفن (Ships)**
   - إدخال بيانات السفينة (الاسم، الوكيل، نوع الحمولة، الشركة المالكة، الجنسية...)
   - تسجيل إشعار الوصول
   - تسجيل وقت الوصول الحقيقي ووقت المغادرة
   - تتبع الحالة (وصلت – في الرصيف – غادرت...)

2. **وحدة الأرصفة (Berths)**
   - تخصيص رصيف لكل سفينة تلقائياً أو يدوياً
   - تسجيل وقت بداية ونهاية الرسو
   - معرفة حالة الرصيف (مشغول – فارغ – صيانة)

3. **وحدة الحمولات (Cargo)**
   - ربط الحمولة بكل سفينة
   - نوع البضاعة – الكمية – مصدرها – وجهتها
   - متابعة حالة التفريغ والتحميل

4. **وحدة التخزين (Warehouses)**
   - تحديد مواقع التخزين في المستودعات أو الساحات
   - حساب مدة التخزين لكل شحنة
   - رسوم التخزين

5. **وحدة الجمارك والتفتيش**
   - تسجيل نتائج التفتيش (جمركي – صحي – أمني)
   - قبول أو رفض البضائع
   - تسجيل ملاحظات أو مخالفات

6. **الوحدة المالية (Finance)**
   - حساب الإيرادات من السفينة (رسوم رسو، تحميل، تفريغ، تخزين...)
   - إصدار فواتير أو إيصالات
   - تقارير مالية شهرية وسنوية

7. **وحدة الصلاحيات والمستخدمين**
   - نظام أدوار متقدم مع صلاحيات مفصلة
   - أدوار مختلفة: مدير عام، موظف سفن، موظف مالي، مفتش، إلخ

8. **وحدة التقارير**
   - تقرير حركة السفن اليومي (مشابه لتقرير مرفأ اللاذقية)
   - تقارير مالية شهرية وسنوية
   - تصدير PDF و Excel

## 🛠️ التقنيات المستخدمة

- **Framework**: Laravel 11
- **Database**: MySQL
- **Frontend**: Bootstrap 5 RTL + Blade Templates
- **Authentication**: Laravel Breeze (سيتم إضافته)
- **Permissions**: Spatie Laravel Permission
- **PDF Generation**: DomPDF
- **Excel Export**: Maatwebsite Excel
- **UI Components**: Livewire

## 📦 المتطلبات

- PHP 8.2 أو أحدث
- MySQL 8.0 أو أحدث
- Composer
- Node.js و NPM (للأصول الأمامية)

## 🚀 التثبيت

### 1. إعداد قاعدة البيانات
قم بإنشاء قاعدة بيانات MySQL جديدة:

```sql
CREATE DATABASE port_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. تحديث إعدادات قاعدة البيانات
قم بتحديث ملف `.env` بإعدادات قاعدة البيانات:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=port_management
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
```

### 3. تشغيل الجداول والبيانات الأولية
```bash
php artisan migrate:fresh --seed
```

### 4. تشغيل الخادم
```bash
php artisan serve
```

## 👤 المستخدم الافتراضي

بعد تشغيل الـ Seeder، سيتم إنشاء مستخدم مدير افتراضي:

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password123
- **الدور**: مدير عام (جميع الصلاحيات)

## 🔐 نظام الصلاحيات

### الأدوار المتاحة:

1. **مدير عام**: جميع الصلاحيات
2. **موظف سفن**: إدارة السفن والوكلاء والأرصفة والحمولات
3. **موظف مالي**: عرض السفن والوكلاء وإدارة الفواتير والتقارير المالية
4. **موظف مستودعات**: عرض السفن وإدارة الحمولات والمستودعات
5. **مفتش**: عرض السفن وإدارة التفتيش
6. **موظف تقارير**: عرض جميع البيانات وتصدير التقارير

### الصلاحيات المتاحة:
- `ships.*` - إدارة السفن
- `agents.*` - إدارة الوكلاء
- `berths.*` - إدارة الأرصفة
- `cargos.*` - إدارة الحمولات
- `warehouses.*` - إدارة المستودعات
- `inspections.*` - إدارة التفتيش
- `invoices.*` - إدارة الفواتير
- `reports.*` - التقارير
- `users.*` - إدارة المستخدمين
- `roles.*` - إدارة الأدوار

## 📊 التقارير

### تقرير حركة السفن اليومي
تقرير شامل مشابه لتقرير مرفأ اللاذقية يتضمن:
- إعلانات الوصول
- السفن الموجودة خارج الحوض
- السفن الموجودة على الأرصفة والمكسر
- السفن التي غادرت خلال 24 ساعة الأخيرة

### التقارير المالية
- تقارير الإيرادات الشهرية والسنوية
- تقارير الفواتير المعلقة
- تحليل الربحية لكل رحلة

## 🌐 اللغة والتوطين

- النظام مصمم باللغة العربية بالكامل
- دعم RTL (من اليمين إلى اليسار)
- إمكانية إضافة دعم اللغة الإنجليزية لاحقاً
- التواريخ والأوقات بالتوقيت المحلي (دمشق)

## 📁 هيكل قاعدة البيانات

### الجداول الرئيسية:

1. **agents** - الوكلاء الملاحيون
2. **ships** - السفن
3. **berths** - الأرصفة
4. **cargos** - الحمولات
5. **warehouses** - المستودعات
6. **inspections** - التفتيش
7. **invoices** - الفواتير
8. **cargo_storage** - تخزين البضائع
9. **ship_berth_history** - تاريخ رسو السفن

### جداول الصلاحيات:
- **users** - المستخدمون
- **roles** - الأدوار
- **permissions** - الصلاحيات
- **model_has_roles** - ربط المستخدمين بالأدوار
- **role_has_permissions** - ربط الأدوار بالصلاحيات

## 🔧 الميزات المتقدمة

### 1. نظام التقارير المتقدم
- تقارير PDF بتنسيق احترافي
- دعم اللغة العربية في PDF
- تقارير Excel قابلة للتخصيص
- تقارير تفاعلية مع إمكانية الفلترة

### 2. نظام الصلاحيات المرن
- صلاحيات مفصلة لكل وحدة
- إمكانية إنشاء أدوار مخصصة
- تحكم دقيق في الوصول للبيانات

### 3. واجهة مستخدم متجاوبة
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم RTL كامل
- واجهة سهلة الاستخدام

### 4. تتبع شامل للعمليات
- تتبع حالة السفن في الوقت الفعلي
- تاريخ كامل لجميع العمليات
- إشعارات تلقائية للأحداث المهمة

## 🚧 الميزات القادمة

- [ ] نظام الإشعارات
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الجمارك
- [ ] تقارير تحليلية متقدمة
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] API للتكامل مع أنظمة خارجية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل MySQL
   - تحقق من إعدادات `.env`

2. **مشاكل الصلاحيات**
   - تأكد من تشغيل `php artisan db:seed`
   - تحقق من تعيين الأدوار للمستخدمين

3. **مشاكل PDF**
   - تأكد من تثبيت DomPDF بشكل صحيح
   - تحقق من إعدادات الخطوط للغة العربية

## 📝 الحالة الحالية للمشروع

### ✅ تم إنجازه:
- [x] إعداد Laravel مع الحزم المطلوبة
- [x] إنشاء قاعدة البيانات والجداول
- [x] نظام الصلاحيات والأدوار
- [x] النماذج والعلاقات الأساسية
- [x] Controllers أساسية
- [x] واجهة المستخدم الأساسية (Layout)
- [x] لوحة التحكم
- [x] صفحة التقارير
- [x] تقرير حركة السفن اليومي
- [x] تصدير PDF للتقارير

### 🚧 قيد التطوير:
- [ ] إكمال Controllers للوحدات
- [ ] صفحات إدارة السفن والوكلاء
- [ ] نظام المصادقة (Laravel Breeze)
- [ ] باقي التقارير
- [ ] نظام الفواتير
- [ ] واجهات إدارة المستودعات والأرصفة

### 📋 المطلوب للإكمال:
1. تثبيت وإعداد Laravel Breeze للمصادقة
2. إكمال Controllers للوحدات المختلفة
3. إنشاء صفحات CRUD للسفن والوكلاء
4. إضافة المزيد من التقارير
5. تحسين واجهة المستخدم
6. إضافة التحقق من صحة البيانات
7. اختبار النظام وإصلاح الأخطاء

## 📞 الدعم

للدعم والاستفسارات، يرجى مراجعة الوثائق أو التواصل مع فريق التطوير.

---

**نظام إدارة المرفأ البحري** - حل متكامل لإدارة الموانئ البحرية
