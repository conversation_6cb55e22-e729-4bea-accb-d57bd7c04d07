<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير المالي الشهري - {{ $report_month->format('F Y') }}</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body, * {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        body {
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 11px;
            line-height: 1.4;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
            font-weight: 400;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1, .header h2 {
            font-family: 'Noto Sans Arabic', '<PERSON><PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif !important;
            font-weight: 600;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 20px;
        }
        
        .header h2 {
            color: #34495e;
            margin: 5px 0;
            font-size: 16px;
        }
        
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .stats-row {
            display: table-row;
        }
        
        .stat-box {
            display: table-cell;
            width: 25%;
            padding: 15px;
            margin: 5px;
            border: 1px solid #dee2e6;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .stat-box h3, .stat-box p {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .stat-box h3 {
            margin: 0;
            font-size: 18px;
            color: #495057;
            font-weight: 600;
        }
        
        .stat-box p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 10px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h3 {
            background-color: #343a40;
            color: white;
            padding: 10px;
            margin: 0 0 15px 0;
            font-size: 14px;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table th,
        table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        table th {
            background-color: #f8f9fa;
            font-weight: 600;
            font-size: 10px;
        }
        
        table td {
            font-size: 10px;
        }
        
        .amount {
            text-align: left;
            font-weight: 600;
        }
        
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 9px;
        }
        
        .footer p {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <h1>{{ config('app.name') }}</h1>
        <h2>التقرير المالي الشهري</h2>
        <p>{{ $report_month->format('F Y') }}</p>
        <p>تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="section">
        <h3>الإحصائيات العامة</h3>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stat-box">
                    <h3>{{ $invoice_stats['total_invoices'] }}</h3>
                    <p>إجمالي الفواتير</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format($invoice_stats['total_amount'], 0) }}</h3>
                    <p>إجمالي المبلغ (ل.س)</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format($invoice_stats['paid_amount'], 0) }}</h3>
                    <p>المبلغ المحصل (ل.س)</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format($invoice_stats['pending_amount'], 0) }}</h3>
                    <p>المبلغ المعلق (ل.س)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تفصيل الإيرادات -->
    <div class="section">
        <h3>تفصيل الإيرادات حسب النوع</h3>
        <table>
            <thead>
                <tr>
                    <th>نوع الرسوم</th>
                    <th>المبلغ (ل.س)</th>
                    <th>النسبة</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $total_revenue = array_sum($revenue_breakdown);
                @endphp
                <tr>
                    <td>رسوم الرسو</td>
                    <td class="amount">{{ number_format($revenue_breakdown['berthing_fees'], 2) }}</td>
                    <td>{{ $total_revenue > 0 ? number_format(($revenue_breakdown['berthing_fees'] / $total_revenue) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>رسوم مناولة البضائع</td>
                    <td class="amount">{{ number_format($revenue_breakdown['cargo_handling_fees'], 2) }}</td>
                    <td>{{ $total_revenue > 0 ? number_format(($revenue_breakdown['cargo_handling_fees'] / $total_revenue) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>رسوم التخزين</td>
                    <td class="amount">{{ number_format($revenue_breakdown['storage_fees'], 2) }}</td>
                    <td>{{ $total_revenue > 0 ? number_format(($revenue_breakdown['storage_fees'] / $total_revenue) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>رسوم الإرشاد</td>
                    <td class="amount">{{ number_format($revenue_breakdown['pilotage_fees'], 2) }}</td>
                    <td>{{ $total_revenue > 0 ? number_format(($revenue_breakdown['pilotage_fees'] / $total_revenue) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>رسوم أخرى</td>
                    <td class="amount">{{ number_format($revenue_breakdown['other_fees'], 2) }}</td>
                    <td>{{ $total_revenue > 0 ? number_format(($revenue_breakdown['other_fees'] / $total_revenue) * 100, 1) : 0 }}%</td>
                </tr>
                <tr style="background-color: #f8f9fa; font-weight: bold;">
                    <td>الإجمالي</td>
                    <td class="amount">{{ number_format($total_revenue, 2) }}</td>
                    <td>100%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- إحصائيات الوكلاء -->
    <div class="section">
        <h3>أفضل 10 وكلاء ملاحيين</h3>
        <table>
            <thead>
                <tr>
                    <th>الترتيب</th>
                    <th>اسم الوكيل</th>
                    <th>اسم الشركة</th>
                    <th>عدد الفواتير</th>
                    <th>إجمالي المبلغ (ل.س)</th>
                    <th>المبلغ المدفوع (ل.س)</th>
                </tr>
            </thead>
            <tbody>
                @foreach($agent_stats->take(10) as $index => $agent)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $agent['name'] }}</td>
                    <td>{{ $agent['company_name'] }}</td>
                    <td>{{ $agent['invoices_count'] }}</td>
                    <td class="amount">{{ number_format($agent['total_amount'], 0) }}</td>
                    <td class="amount">{{ number_format($agent['paid_amount'], 0) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- الإحصائيات اليومية -->
    <div class="section page-break">
        <h3>الإحصائيات اليومية للشهر</h3>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>اليوم</th>
                    <th>عدد الفواتير</th>
                    <th>إجمالي المبلغ (ل.س)</th>
                    <th>المبلغ المحصل (ل.س)</th>
                    <th>معدل التحصيل</th>
                </tr>
            </thead>
            <tbody>
                @foreach($daily_stats as $day)
                <tr>
                    <td>{{ \Carbon\Carbon::parse($day['date'])->format('Y/m/d') }}</td>
                    <td>{{ \Carbon\Carbon::parse($day['date'])->locale('ar')->dayName }}</td>
                    <td>{{ $day['invoices_count'] }}</td>
                    <td class="amount">{{ number_format($day['total_amount'], 0) }}</td>
                    <td class="amount">{{ number_format($day['paid_amount'], 0) }}</td>
                    <td>
                        @if($day['total_amount'] > 0)
                            {{ number_format(($day['paid_amount'] / $day['total_amount']) * 100, 1) }}%
                        @else
                            -
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- ملخص التقرير -->
    <div class="section">
        <h3>ملخص التقرير</h3>
        <table>
            <tbody>
                <tr>
                    <td><strong>إجمالي الفواتير المصدرة:</strong></td>
                    <td class="amount">{{ $invoice_stats['total_invoices'] }} فاتورة</td>
                </tr>
                <tr>
                    <td><strong>إجمالي الإيرادات المستحقة:</strong></td>
                    <td class="amount">{{ number_format($invoice_stats['total_amount'], 2) }} ل.س</td>
                </tr>
                <tr>
                    <td><strong>إجمالي الإيرادات المحصلة:</strong></td>
                    <td class="amount">{{ number_format($invoice_stats['paid_amount'], 2) }} ل.س</td>
                </tr>
                <tr>
                    <td><strong>معدل التحصيل الإجمالي:</strong></td>
                    <td class="amount">
                        {{ $invoice_stats['total_amount'] > 0 ? number_format(($invoice_stats['paid_amount'] / $invoice_stats['total_amount']) * 100, 1) : 0 }}%
                    </td>
                </tr>
                <tr>
                    <td><strong>المبالغ المعلقة:</strong></td>
                    <td class="amount">{{ number_format($invoice_stats['pending_amount'], 2) }} ل.س</td>
                </tr>
                <tr>
                    <td><strong>عدد الوكلاء النشطين:</strong></td>
                    <td class="amount">{{ $agent_stats->where('invoices_count', '>', 0)->count() }} وكيل</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المرفأ البحري</p>
        <p>{{ config('app.name') }} - تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>
</body>
</html>