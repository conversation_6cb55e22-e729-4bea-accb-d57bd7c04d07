<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ShipController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\BerthController;
use App\Http\Controllers\CargoController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\InspectionController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ReportController;
use Illuminate\Support\Facades\Route;

// الصفحة الرئيسية - توجيه إلى لوحة التحكم
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// صفحة اختبار
Route::get('/test', function () {
    return view('test');
})->middleware('auth');

// صفحة اختبار الصلاحيات
Route::get('/test-permissions', function () {
    return view('test-permissions');
})->middleware('auth')->name('test-permissions');

// لوحة التحكم الرئيسية
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// مجموعة المسارات المحمية بالمصادقة
Route::middleware(['auth', 'verified'])->group(function () {

    // إدارة الملف الشخصي
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // إدارة السفن
    Route::resource('ships', ShipController::class);

    // إدارة الوكلاء الملاحيين
    Route::resource('agents', AgentController::class);

    // إدارة الأرصفة
    Route::resource('berths', BerthController::class);
    Route::post('berths/{berth}/assign-ship', [BerthController::class, 'assignShip'])->name('berths.assign-ship');
    Route::post('berths/{berth}/release-ship', [BerthController::class, 'releaseShip'])->name('berths.release-ship');

    // إدارة الحمولات
    Route::resource('cargos', CargoController::class);
    Route::post('cargos/{cargo}/update-status', [CargoController::class, 'updateStatus'])->name('cargos.update-status');
    Route::get('cargos/export/excel', [CargoController::class, 'exportExcel'])->name('cargos.export.excel');
    Route::post('cargos/bulk-actions', [CargoController::class, 'bulkActions'])->name('cargos.bulk-actions');
    Route::get('cargos/advanced-report', [CargoController::class, 'advancedReport'])->name('cargos.advanced-report');
    Route::get('cargos/tracking-report', [CargoController::class, 'trackingReport'])->name('cargos.tracking-report');
    Route::get('cargos-quick-stats', [CargoController::class, 'quickStats'])->name('cargos.quick-stats');

    // إدارة المستودعات
    Route::resource('warehouses', WarehouseController::class);
    Route::post('warehouses/{warehouse}/update-status', [WarehouseController::class, 'updateStatus'])->name('warehouses.update-status');
    Route::get('warehouses-occupancy-report', [WarehouseController::class, 'occupancyReport'])->name('warehouses.occupancy-report');
    Route::get('warehouses/export/excel', [WarehouseController::class, 'exportExcel'])->name('warehouses.export.excel');
    Route::get('warehouses/{warehouse}/capacity-details', [WarehouseController::class, 'getCapacityDetails'])->name('warehouses.capacity-details');
    Route::post('warehouses/{warehouse}/update-capacity', [WarehouseController::class, 'updateCapacity'])->name('warehouses.update-capacity');
    Route::post('warehouses/bulk-actions', [WarehouseController::class, 'bulkActions'])->name('warehouses.bulk-actions');

    // إدارة التفتيش
    Route::resource('inspections', InspectionController::class);
    Route::post('inspections/{inspection}/update-result', [InspectionController::class, 'updateResult'])->name('inspections.update-result');
    Route::get('inspections-report', [InspectionController::class, 'report'])->name('inspections.report');
    Route::get('/inspections/export/excel', [\App\Http\Controllers\InspectionController::class, 'exportExcel'])->name('inspections.export.excel');

    // إدارة الفواتير
    Route::resource('invoices', InvoiceController::class);
    Route::post('invoices/calculate-fees', [InvoiceController::class, 'calculateFees'])->name('invoices.calculate-fees');
    Route::post('invoices/{invoice}/update-status', [InvoiceController::class, 'updateStatus'])->name('invoices.update-status');
    Route::post('invoices/{invoice}/add-payment', [InvoiceController::class, 'addPayment'])->name('invoices.add-payment');
    Route::post('invoices/{invoice}/apply-discount', [InvoiceController::class, 'applyDiscount'])->name('invoices.apply-discount');
    Route::get('invoices/{invoice}/print', [InvoiceController::class, 'print'])->name('invoices.print');
    Route::get('invoices-report', [InvoiceController::class, 'report'])->name('invoices.report');
    Route::post('invoices/send-overdue-reminders', [InvoiceController::class, 'sendOverdueReminders'])->name('invoices.send-overdue-reminders');
    Route::post('invoices/merge', [InvoiceController::class, 'mergeInvoices'])->name('invoices.merge');
    Route::get('invoices-quick-stats', [InvoiceController::class, 'quickStats'])->name('invoices.quick-stats');
    Route::post('invoices/{invoice}/send', [InvoiceController::class, 'send'])->name('invoices.send');
    Route::post('invoices/{invoice}/cancel', [InvoiceController::class, 'cancel'])->name('invoices.cancel');
    Route::post('invoices/bulk-actions', [InvoiceController::class, 'bulkActions'])->name('invoices.bulk-actions');
    Route::get('invoices/export/excel', [InvoiceController::class, 'exportExcel'])->name('invoices.export.excel');
    Route::get('invoices/advanced-report', [InvoiceController::class, 'advancedReport'])->name('invoices.advanced-report');
    Route::get('invoices/payment-tracking', [InvoiceController::class, 'paymentTrackingReport'])->name('invoices.payment-tracking');

    // إدارة المستخدمين
    Route::resource('users', UserController::class);
    Route::post('users/{user}/suspend', [UserController::class, 'suspend'])->name('users.suspend');
    Route::post('users/{user}/activate', [UserController::class, 'activate'])->name('users.activate');
    Route::post('users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.reset-password');
    Route::post('users/{user}/send-welcome', [UserController::class, 'sendWelcome'])->name('users.send-welcome');
    Route::post('users/{user}/login-as', [UserController::class, 'loginAs'])->name('users.login-as');
    Route::get('users/{user}/activity-log', [UserController::class, 'activityLog'])->name('users.activity-log');
    Route::get('users/{user}/login-history', [UserController::class, 'loginHistory'])->name('users.login-history');
    Route::post('users/bulk-actions', [UserController::class, 'bulkActions'])->name('users.bulk-actions');
    Route::get('users-export', [UserController::class, 'export'])->name('users.export');

    // التقارير
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/daily-ship-movement', [ReportController::class, 'dailyShipMovement'])->name('daily-ship-movement');
        Route::get('/monthly-financial', [ReportController::class, 'monthlyFinancial'])->name('monthly-financial');
        Route::get('/berths-utilization', [ReportController::class, 'berthsUtilization'])->name('berths-utilization');
        Route::get('/warehouses-storage', [ReportController::class, 'warehousesStorage'])->name('warehouses-storage');
        Route::get('/agents-performance', [ReportController::class, 'agentsPerformance'])->name('agents-performance');
    });
});

// مسارات المصادقة
require __DIR__.'/auth.php';
