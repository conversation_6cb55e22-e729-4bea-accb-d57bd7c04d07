<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Berth;
use App\Models\Ship;
use Illuminate\Support\Facades\Gate;
use Carbon\Carbon;

class BerthController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Gate::authorize('berths.view');

        $query = Berth::withCount(['ships', 'history']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // بحث في اسم الرصيف أو الكود
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('code', 'like', '%' . $request->search . '%');
            });
        }

        $berths = $query->orderBy('code')->paginate(15);

        return view('berths.index', compact('berths'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        Gate::authorize('berths.create');

        return view('berths.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Gate::authorize('berths.create');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:berths,code',
            'length' => 'nullable|numeric|min:0',
            'depth' => 'nullable|numeric|min:0',
            'max_tonnage' => 'nullable|integer|min:0',
            'status' => 'required|in:available,occupied,maintenance,out_of_service',
            'description' => 'nullable|string'
        ]);

        Berth::create($validated);

        return redirect()->route('berths.index')
            ->with('success', 'تم إضافة الرصيف بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Berth $berth)
    {
        Gate::authorize('berths.view');

        $berth->load([
            'currentShip',
            'history' => function($query) {
                $query->with('ship')->orderBy('berthing_start', 'desc')->take(10);
            }
        ]);

        // إحصائيات الرصيف
        $stats = [
            'total_ships' => $berth->ships()->count(),
            'current_occupancy' => $berth->status === 'occupied' ? 1 : 0,
            'avg_stay_duration' => $berth->history()
                ->whereNotNull('berthing_end')
                ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, berthing_start, berthing_end)) as avg_hours')
                ->value('avg_hours'),
            'monthly_usage' => $berth->history()
                ->whereMonth('berthing_start', Carbon::now()->month)
                ->count()
        ];

        return view('berths.show', compact('berth', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Berth $berth)
    {
        Gate::authorize('berths.edit');

        return view('berths.edit', compact('berth'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Berth $berth)
    {
        Gate::authorize('berths.edit');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:berths,code,' . $berth->id,
            'length' => 'nullable|numeric|min:0',
            'depth' => 'nullable|numeric|min:0',
            'max_tonnage' => 'nullable|integer|min:0',
            'status' => 'required|in:available,occupied,maintenance,out_of_service',
            'description' => 'nullable|string'
        ]);

        $berth->update($validated);

        return redirect()->route('berths.index')
            ->with('success', 'تم تحديث بيانات الرصيف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Berth $berth)
    {
        Gate::authorize('berths.delete');

        // تحقق من عدم وجود سفن مرتبطة بهذا الرصيف
        if ($berth->ships()->count() > 0) {
            return redirect()->route('berths.index')
                ->with('error', 'لا يمكن حذف هذا الرصيف لأنه مرتبط بسفن موجودة');
        }

        $berth->delete();

        return redirect()->route('berths.index')
            ->with('success', 'تم حذف الرصيف بنجاح');
    }

    /**
     * Assign ship to berth
     */
    public function assignShip(Request $request, Berth $berth)
    {
        Gate::authorize('berths.edit');

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id'
        ]);

        $ship = Ship::findOrFail($validated['ship_id']);

        // تحقق من أن الرصيف متاح
        if ($berth->status !== 'available') {
            return back()->with('error', 'الرصيف غير متاح حالياً');
        }

        // تحقق من أن السفينة ليست مرسية في رصيف آخر
        if ($ship->status === 'berthed') {
            return back()->with('error', 'السفينة مرسية بالفعل في رصيف آخر');
        }

        // تحديث حالة الرصيف والسفينة
        $berth->update([
            'status' => 'occupied',
            'current_ship_id' => $ship->id,
            'occupied_since' => Carbon::now()
        ]);

        $ship->update(['status' => 'berthed']);

        // إضافة سجل في تاريخ الرصيف
        $berth->history()->create([
            'ship_id' => $ship->id,
            'berthing_start' => Carbon::now(),
            'hourly_rate' => 50.00, // تعريفة افتراضية
            'status' => 'active'
        ]);

        return back()->with('success', 'تم ربط السفينة بالرصيف بنجاح');
    }

    /**
     * Release ship from berth
     */
    public function releaseShip(Berth $berth)
    {
        Gate::authorize('berths.edit');

        if ($berth->status !== 'occupied' || !$berth->current_ship_id) {
            return back()->with('error', 'الرصيف غير مشغول حالياً');
        }

        $ship = Ship::find($berth->current_ship_id);

        // تحديث حالة الرصيف
        $berth->update([
            'status' => 'available',
            'current_ship_id' => null,
            'occupied_since' => null
        ]);

        // تحديث حالة السفينة
        if ($ship) {
            $ship->update(['status' => 'outside_port']);
        }

        // تحديث سجل تاريخ الرصيف
        $activeRecord = $berth->history()
            ->where('ship_id', $berth->current_ship_id)
            ->where('status', 'active')
            ->first();

        if ($activeRecord) {
            $startTime = $activeRecord->berthing_start;
            $endTime = Carbon::now();
            $hours = $startTime->diffInHours($endTime);

            $activeRecord->update([
                'berthing_end' => $endTime,
                'berthing_hours' => $hours,
                'total_berthing_cost' => $hours * $activeRecord->hourly_rate,
                'status' => 'completed'
            ]);
        }

        return back()->with('success', 'تم تحرير الرصيف بنجاح');
    }
}
