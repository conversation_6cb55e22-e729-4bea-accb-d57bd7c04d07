<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cargo extends Model
{
    protected $fillable = [
        'ship_id',
        'cargo_type',
        'description',
        'quantity',
        'unit',
        'weight',
        'volume',
        'value',
        'currency',
        'origin_country',
        'destination_country',
        'status',
        'loading_date',
        'unloading_date',
        'notes'
    ];

    protected $casts = [
        'loading_date' => 'datetime',
        'unloading_date' => 'datetime',
        'quantity' => 'decimal:2',
        'weight' => 'decimal:2',
        'volume' => 'decimal:2',
        'value' => 'decimal:2'
    ];

    /**
     * العلاقة مع السفينة
     */
    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    /**
     * العلاقة مع التخزين
     */
    public function storages(): HasMany
    {
        return $this->hasMany(CargoStorage::class);
    }

    /**
     * الحصول على الحالة بالعربية
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'loading' => 'قيد التحميل',
            'loaded' => 'محمل',
            'in_transit' => 'في الطريق',
            'unloading' => 'قيد التفريغ',
            'unloaded' => 'مفرغ',
            'stored' => 'مخزن',
            'delivered' => 'مسلم',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على نوع الحمولة بالعربية
     */
    public function getCargoTypeLabelAttribute(): string
    {
        return match($this->cargo_type) {
            'container' => 'حاويات',
            'bulk' => 'بضائع سائبة',
            'liquid' => 'سوائل',
            'gas' => 'غازات',
            'vehicles' => 'مركبات',
            'general' => 'بضائع عامة',
            'dangerous' => 'مواد خطرة',
            'refrigerated' => 'مبردة',
            default => 'أخرى'
        };
    }

    /**
     * الحصول على الوحدة بالعربية
     */
    public function getUnitLabelAttribute(): string
    {
        return match($this->unit) {
            'tons' => 'طن',
            'kg' => 'كيلوغرام',
            'pieces' => 'قطعة',
            'containers' => 'حاوية',
            'pallets' => 'منصة',
            'cubic_meters' => 'متر مكعب',
            'liters' => 'لتر',
            default => $this->unit
        };
    }
}
