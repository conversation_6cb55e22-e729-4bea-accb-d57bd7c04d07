@extends('layouts.port-app')

@section('page-title', 'تعديل الرصيف: ' . $berth->name)

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('berths.show', $berth) }}" class="btn btn-info">
        <i class="fas fa-eye me-2"></i>
        عرض التفاصيل
    </a>
    <a href="{{ route('berths.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات الرصيف
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('berths.update', $berth) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم الرصيف <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name', $berth->name) }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="code" class="form-label">كود الرصيف <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                               id="code" name="code" value="{{ old('code', $berth->code) }}" required>
                                        <div class="form-text">مثال: B001, PIER-A, etc.</div>
                                        @error('code')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة الرصيف <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="available" {{ old('status', $berth->status) == 'available' ? 'selected' : '' }}>
                                        متاح
                                    </option>
                                    <option value="occupied" {{ old('status', $berth->status) == 'occupied' ? 'selected' : '' }}>
                                        مشغول
                                    </option>
                                    <option value="maintenance" {{ old('status', $berth->status) == 'maintenance' ? 'selected' : '' }}>
                                        قيد الصيانة
                                    </option>
                                    <option value="out_of_service" {{ old('status', $berth->status) == 'out_of_service' ? 'selected' : '' }}>
                                        خارج الخدمة
                                    </option>
                                </select>
                                @if($berth->status === 'occupied')
                                    <div class="form-text text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        تحذير: الرصيف مشغول حالياً بالسفينة {{ $berth->currentShip->name ?? 'غير محدد' }}
                                    </div>
                                @endif
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المواصفات التقنية -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-ruler me-2"></i>
                                المواصفات التقنية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="length" class="form-label">الطول (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('length') is-invalid @enderror" 
                                               id="length" name="length" value="{{ old('length', $berth->length) }}">
                                        @error('length')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="depth" class="form-label">العمق (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('depth') is-invalid @enderror" 
                                               id="depth" name="depth" value="{{ old('depth', $berth->depth) }}">
                                        @error('depth')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="max_tonnage" class="form-label">الحمولة القصوى (طن)</label>
                                        <input type="number" class="form-control @error('max_tonnage') is-invalid @enderror" 
                                               id="max_tonnage" name="max_tonnage" value="{{ old('max_tonnage', $berth->max_tonnage) }}">
                                        @error('max_tonnage')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الوصف -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-file-alt me-2"></i>
                                معلومات إضافية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف الرصيف</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4">{{ old('description', $berth->description) }}</textarea>
                                <div class="form-text">وصف مختصر عن الرصيف ومميزاته والخدمات المتاحة</div>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    @if($berth->currentShip)
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-warning mb-3">
                                <i class="fas fa-ship me-2"></i>
                                السفينة المرسية حالياً
                            </h6>
                            
                            <div class="alert alert-warning">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>اسم السفينة:</strong> {{ $berth->currentShip->name }}<br>
                                        <strong>نوع السفينة:</strong> {{ $berth->currentShip->ship_type }}<br>
                                        <strong>الجنسية:</strong> {{ $berth->currentShip->flag }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>تاريخ الرسو:</strong> {{ $berth->occupied_since ? $berth->occupied_since->format('Y/m/d H:i') : 'غير محدد' }}<br>
                                        <strong>مدة الرسو:</strong> {{ $berth->occupied_since ? $berth->occupied_since->diffForHumans() : 'غير محدد' }}<br>
                                        <strong>الوكيل الملاحي:</strong> {{ $berth->currentShip->agent->name ?? 'غير محدد' }}
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('ships.show', $berth->currentShip) }}" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض تفاصيل السفينة
                                    </a>
                                    @can('berths.edit')
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmReleaseShip()">
                                        <i class="fas fa-sign-out-alt me-1"></i>
                                        تحرير الرصيف
                                    </button>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <!-- أزرار الحفظ -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('berths.show', $berth) }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الرصيف
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="fw-bold">تاريخ الإنشاء:</td>
                                <td>{{ $berth->created_at->format('Y/m/d H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">آخر تحديث:</td>
                                <td>{{ $berth->updated_at->format('Y/m/d H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">إجمالي السفن:</td>
                                <td><span class="badge bg-info">{{ $berth->ships()->count() }}</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="fw-bold">الحالة الحالية:</td>
                                <td>
                                    @switch($berth->status)
                                        @case('available')
                                            <span class="badge bg-success">متاح</span>
                                            @break
                                        @case('occupied')
                                            <span class="badge bg-warning">مشغول</span>
                                            @break
                                        @case('maintenance')
                                            <span class="badge bg-info">صيانة</span>
                                            @break
                                        @case('out_of_service')
                                            <span class="badge bg-danger">خارج الخدمة</span>
                                            @break
                                    @endswitch
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">مدة الإشغال:</td>
                                <td>
                                    @if($berth->occupied_since)
                                        {{ $berth->occupied_since->diffForHumans() }}
                                    @else
                                        غير مشغول
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">آخر نشاط:</td>
                                <td>{{ $berth->updated_at->diffForHumans() }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmReleaseShip() {
    if (confirm('هل أنت متأكد من تحرير الرصيف؟ سيتم تحديث حالة السفينة إلى "خارج الحوض"')) {
        // إنشاء form وإرساله
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("berths.release-ship", $berth) }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

// تحديث كود الرصيف تلقائياً بناءً على الاسم (فقط إذا كان الكود فارغ)
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const codeField = document.getElementById('code');
    const originalCode = '{{ $berth->code }}';
    
    // فقط إذا تم تغيير الاسم وكان الكود هو نفس الكود الأصلي
    if (name && codeField.value === originalCode) {
        // تحويل الاسم إلى كود مقترح
        let suggestedCode = name
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]/g, '')
            .toUpperCase()
            .substring(0, 10);
        
        // عرض اقتراح للمستخدم
        if (confirm('هل تريد تحديث كود الرصيف إلى: ' + suggestedCode + '؟')) {
            codeField.value = suggestedCode;
        }
    }
});

// تحذير عند تغيير حالة رصيف مشغول
document.getElementById('status').addEventListener('change', function() {
    const currentStatus = '{{ $berth->status }}';
    const newStatus = this.value;
    
    if (currentStatus === 'occupied' && newStatus !== 'occupied') {
        if (!confirm('تحذير: الرصيف مشغول حالياً. تغيير الحالة قد يؤثر على السفينة المرسية. هل أنت متأكد؟')) {
            this.value = currentStatus; // إرجاع القيمة الأصلية
        }
    }
});
</script>
@endpush
