<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Berth extends Model
{
    protected $fillable = [
        'name',
        'code',
        'length',
        'depth',
        'max_tonnage',
        'status',
        'current_ship_id',
        'occupied_since',
        'notes'
    ];

    protected $casts = [
        'length' => 'decimal:2',
        'depth' => 'decimal:2',
        'occupied_since' => 'datetime'
    ];

    /**
     * Get the current ship occupying this berth
     */
    public function currentShip(): BelongsTo
    {
        return $this->belongsTo(Ship::class, 'current_ship_id');
    }

    /**
     * Get berth history
     */
    public function berthHistory(): HasMany
    {
        return $this->hasMany(ShipBerthHistory::class);
    }

    /**
     * Alias for berthHistory - for backward compatibility
     */
    public function history(): HasMany
    {
        return $this->berthHistory();
    }

    /**
     * Get all ships that have used this berth through history
     */
    public function ships(): BelongsToMany
    {
        return $this->belongsToMany(Ship::class, 'ship_berth_history')
            ->withPivot(['berthing_start', 'berthing_end', 'status', 'notes'])
            ->withTimestamps();
    }

    public function shipBerthHistory()
    {
        return $this->hasMany(\App\Models\ShipBerthHistory::class);
    }
}
