<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير المستودعات والتخزين</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body, * {
            font-family: 'Cairo', '<PERSON>ja<PERSON>u Sans', Aria<PERSON>, Tahoma, sans-serif !important;
        }
        body {
            font-size: 12px;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .amount {
            font-weight: bold;
        }
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <h2>تقرير المستودعات والتخزين</h2>
    <p>الفترة: {{ $date_from->format('Y/m/d') }} - {{ $date_to->format('Y/m/d') }}</p>
    <table>
        <thead>
            <tr>
                <th>اسم المستودع</th>
                <th>السعة الكلية</th>
                <th>الإشغال الحالي</th>
                <th>نسبة الإشغال</th>
                <th>المساحة المتاحة</th>
                <th>إجمالي الإيرادات (ل.س)</th>
            </tr>
        </thead>
        <tbody>
            @foreach($warehouse_stats as $stat)
            <tr>
                <td>{{ $stat['warehouse']->name }}</td>
                <td>{{ $stat['total_capacity'] }}</td>
                <td>{{ $stat['current_occupancy'] }}</td>
                <td>{{ number_format($stat['occupancy_rate'], 1) }}%</td>
                <td>{{ $stat['available_space'] }}</td>
                <td class="amount">{{ number_format($stat['total_revenue'], 0) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المرفأ البحري</p>
        <p>{{ config('app.name') }} - تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>
</body>
</html> 