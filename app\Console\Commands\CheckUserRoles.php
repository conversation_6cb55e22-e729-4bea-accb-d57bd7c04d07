<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckUserRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:check-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check user roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = \App\Models\User::first();
        
        if ($user) {
            $this->info("User: {$user->name} ({$user->email})");
            $this->info("Roles:");
            foreach ($user->roles as $role) {
                $this->line("- {$role->name} ({$role->display_name})");
            }
            
            $this->info("Has admin role: " . ($user->hasRole('admin') ? 'Yes' : 'No'));
        } else {
            $this->error("No users found");
        }
    }
}
