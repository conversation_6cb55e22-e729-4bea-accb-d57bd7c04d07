<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('username')->nullable()->unique()->after('name');
            $table->string('phone')->nullable()->after('email');
            $table->text('bio')->nullable()->after('phone');
            $table->string('department')->nullable()->after('bio');
            $table->string('position')->nullable()->after('department');
            $table->date('hire_date')->nullable()->after('position');
            $table->string('avatar')->nullable()->after('hire_date');
            $table->boolean('is_active')->default(true)->after('avatar');
            $table->boolean('is_suspended')->default(false)->after('is_active');
            $table->timestamp('last_login_at')->nullable()->after('is_suspended');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'username',
                'phone',
                'bio',
                'department',
                'position',
                'hire_date',
                'avatar',
                'is_active',
                'is_suspended',
                'last_login_at'
            ]);
        });
    }
};
