@extends('layouts.port-app')

@section('page-title', 'إدارة الفواتير')

@section('page-actions')
<div class="btn-group">
    @can('invoices.create')
    <a href="{{ route('invoices.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إنشاء فاتورة جديدة
    </a>
    @endcan
    <a href="{{ route('invoices.export.excel') }}" class="btn btn-outline-success">
        <i class="fas fa-file-excel me-2"></i>
        تصدير Excel
    </a>
    <a href="{{ route('invoices.report') }}" class="btn btn-outline-info">
        <i class="fas fa-chart-bar me-2"></i>
        التقارير المالية
    </a>
    <button type="button" class="btn btn-outline-warning" onclick="sendOverdueReminders()">
        <i class="fas fa-bell me-2"></i>
        تذكيرات متأخرة
    </button>
</div>
@endsection

@section('content')
<!-- إحصائيات مالية محسنة -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">إجمالي الفواتير</h6>
                        <h3 class="mb-0">{{ $stats['total'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مدفوعة</h6>
                        <h3 class="mb-0">{{ $stats['paid'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مرسلة</h6>
                        <h3 class="mb-0">{{ $stats['sent'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-paper-plane fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">متأخرة</h6>
                        <h3 class="mb-0">{{ $stats['overdue'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مسودات</h6>
                        <h3 class="mb-0">{{ $stats['draft'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-edit fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">دفع جزئي</h6>
                        <h3 class="mb-0">{{ $stats['partial'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-coins fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات مالية -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">إجمالي المبلغ</h6>
                        <h3 class="mb-0">${{ number_format($stats['total_amount'], 2) }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">المبلغ المدفوع</h6>
                        <h3 class="mb-0">${{ number_format($stats['paid_amount'], 2) }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">المبلغ المتبقي</h6>
                        <h3 class="mb-0">${{ number_format($stats['remaining_amount'], 2) }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">متوسط الفاتورة</h6>
                        <h3 class="mb-0">${{ number_format($stats['average_invoice'], 2) }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calculator fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- شريط البحث والفلترة -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('invoices.index') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في رقم الفاتورة أو السفينة..." 
                               value="{{ request('search') }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status" onchange="this.form.submit()">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>مسودة</option>
                        <option value="sent" {{ request('status') == 'sent' ? 'selected' : '' }}>مرسلة</option>
                        <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>مدفوعة</option>
                        <option value="partial" {{ request('status') == 'partial' ? 'selected' : '' }}>دفع جزئي</option>
                        <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>متأخرة</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغاة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="agent_id" onchange="this.form.submit()">
                        <option value="">جميع الوكلاء</option>
                        @foreach($agents as $agent)
                            <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                {{ $agent->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_from" 
                           value="{{ request('date_from') }}" 
                           onchange="this.form.submit()" 
                           placeholder="من تاريخ">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_to" 
                           value="{{ request('date_to') }}" 
                           onchange="this.form.submit()" 
                           placeholder="إلى تاريخ">
                </div>
                <div class="col-md-1">
                    <a href="{{ route('invoices.index') }}" class="btn btn-outline-secondary w-100" title="إعادة تعيين">
                        <i class="fas fa-redo"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="card">
    @if($invoices->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>رقم الفاتورة</th>
                        <th>السفينة</th>
                        <th>الوكيل</th>
                        <th>تاريخ الفاتورة</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>المبلغ الإجمالي</th>
                        <th>المبلغ المدفوع</th>
                        <th>المتبقي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoices as $invoice)
                    <tr>
                        <td>
                            <input type="checkbox" class="invoice-checkbox" value="{{ $invoice->id }}">
                        </td>
                        <td>
                            <div class="fw-bold">{{ $invoice->invoice_number }}</div>
                            <small class="text-muted">{{ $invoice->invoice_date->format('Y/m/d') }}</small>
                        </td>
                        <td>
                            <a href="{{ route('ships.show', $invoice->ship) }}" class="text-decoration-none">
                                {{ $invoice->ship->name }}
                            </a>
                            <br>
                            <small class="text-muted">{{ $invoice->ship->imo_number ?? 'بدون IMO' }}</small>
                        </td>
                        <td>
                            <a href="{{ route('agents.show', $invoice->agent) }}" class="text-decoration-none">
                                {{ $invoice->agent->name }}
                            </a>
                            <br>
                            <small class="text-muted">{{ $invoice->agent->phone ?? '' }}</small>
                        </td>
                        <td>{{ $invoice->invoice_date->format('Y/m/d') }}</td>
                        <td>
                            {{ $invoice->due_date ? $invoice->due_date->format('Y/m/d') : '-' }}
                            @if($invoice->due_date && $invoice->due_date->isPast() && $invoice->status !== 'paid')
                                <br><small class="text-danger">متأخر {{ $invoice->due_date->diffForHumans() }}</small>
                            @endif
                        </td>
                        <td>
                            <strong>${{ number_format($invoice->total_amount, 2) }}</strong>
                        </td>
                        <td>
                            <span class="text-success">${{ number_format($invoice->paid_amount, 2) }}</span>
                        </td>
                        <td>
                            <span class="text-warning">${{ number_format($invoice->total_amount - $invoice->paid_amount, 2) }}</span>
                        </td>
                        <td>
                            @switch($invoice->status)
                                @case('draft')
                                    <span class="badge bg-secondary">مسودة</span>
                                    @break
                                @case('sent')
                                    <span class="badge bg-primary">مرسلة</span>
                                    @break
                                @case('paid')
                                    <span class="badge bg-success">مدفوعة</span>
                                    @break
                                @case('partial')
                                    <span class="badge bg-info">دفع جزئي</span>
                                    @break
                                @case('overdue')
                                    <span class="badge bg-danger">متأخرة</span>
                                    @break
                                @case('cancelled')
                                    <span class="badge bg-dark">ملغاة</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $invoice->status }}</span>
                            @endswitch
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                @can('invoices.view')
                                <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-outline-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endcan
                                @can('invoices.edit')
                                <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endcan
                                <a href="{{ route('invoices.print', $invoice) }}" class="btn btn-outline-secondary" title="طباعة" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                                @if($invoice->status === 'draft')
                                    @can('invoices.edit')
                                    <button type="button" class="btn btn-outline-success" title="إرسال" onclick="sendInvoice({{ $invoice->id }})">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    @endcan
                                @endif
                                @if($invoice->status !== 'paid' && $invoice->status !== 'cancelled')
                                    @can('invoices.edit')
                                    <button type="button" class="btn btn-outline-warning" title="إضافة دفعة" onclick="addPayment({{ $invoice->id }})">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    @endcan
                                @endif
                                @can('invoices.delete')
                                <button type="button" class="btn btn-outline-danger" title="حذف" onclick="confirmDelete({{ $invoice->id }}, '{{ $invoice->invoice_number }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer">
            {{ $invoices->appends(request()->query())->links() }}
        </div>
    @else
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد فواتير</h5>
            <p class="text-muted">لم يتم العثور على أي فواتير مطابقة للبحث</p>
            @can('invoices.create')
            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </a>
            @endcan
        </div>
    @endif
</div>

<!-- مودال إضافة دفعة -->
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPaymentForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">مبلغ الدفعة</label>
                        <input type="number" class="form-control" id="payment_amount" name="amount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash">نقداً</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">تاريخ الدفع</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="{{ date('Y-m-d') }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الدفعة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الفاتورة: <strong id="invoiceNumber"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addPayment(invoiceId) {
    document.getElementById('addPaymentForm').action = `/invoices/${invoiceId}/add-payment`;
    new bootstrap.Modal(document.getElementById('addPaymentModal')).show();
}

function sendInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من إرسال هذه الفاتورة؟')) {
        fetch(`/invoices/${invoiceId}/send`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء إرسال الفاتورة');
            }
        });
    }
}

function confirmDelete(invoiceId, invoiceNumber) {
    document.getElementById('invoiceNumber').textContent = invoiceNumber;
    document.getElementById('deleteForm').action = `/invoices/${invoiceId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function sendOverdueReminders() {
    if (confirm('هل أنت متأكد من إرسال تذكيرات للفواتير المتأخرة؟')) {
        fetch('/invoices/send-overdue-reminders', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
        });
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.invoice-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}
</script>
@endpush
