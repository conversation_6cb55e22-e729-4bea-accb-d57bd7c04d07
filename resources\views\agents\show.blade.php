@extends('layouts.port-app')

@section('page-title', 'تفاصيل الوكيل: ' . $agent->name)

@section('page-actions')
<div class="btn-group">
    @can('agents.edit')
    <a href="{{ route('agents.edit', $agent) }}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i>
        تعديل
    </a>
    @endcan
    
    <a href="{{ route('agents.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <!-- المعلومات الأساسية -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">اسم الوكيل:</td>
                                <td>{{ $agent->name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">اسم الشركة:</td>
                                <td>{{ $agent->company_name ?? 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">رقم الترخيص:</td>
                                <td>{{ $agent->license_number ?? 'غير محدد' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">رقم الهاتف:</td>
                                <td>
                                    @if($agent->phone)
                                        <a href="tel:{{ $agent->phone }}" class="text-decoration-none">
                                            {{ $agent->phone }}
                                        </a>
                                    @else
                                        غير محدد
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">البريد الإلكتروني:</td>
                                <td>
                                    @if($agent->email)
                                        <a href="mailto:{{ $agent->email }}" class="text-decoration-none">
                                            {{ $agent->email }}
                                        </a>
                                    @else
                                        غير محدد
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الحالة:</td>
                                <td>
                                    @if($agent->is_active)
                                        <span class="badge bg-success">نشط</span>
                                    @else
                                        <span class="badge bg-secondary">غير نشط</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($agent->address)
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="text-muted">العنوان:</h6>
                        <p class="bg-light p-3 rounded">{{ $agent->address }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
        
        <!-- السفن المرتبطة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ship me-2"></i>
                    السفن المرتبطة ({{ $agent->ships->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($agent->ships->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم السفينة</th>
                                    <th>نوع السفينة</th>
                                    <th>الجنسية</th>
                                    <th>الحالة</th>
                                    <th>عدد الحمولات</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($agent->ships as $ship)
                                <tr>
                                    <td>
                                        <strong>{{ $ship->name }}</strong>
                                        @if($ship->owner_company)
                                            <br><small class="text-muted">{{ $ship->owner_company }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $ship->ship_type }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $ship->flag }}</span>
                                    </td>
                                    <td>
                                        @switch($ship->status)
                                            @case('announced')
                                                <span class="badge bg-primary">إعلان وصول</span>
                                                @break
                                            @case('outside_port')
                                                <span class="badge bg-warning">خارج الحوض</span>
                                                @break
                                            @case('berthed')
                                                <span class="badge bg-success">مرسية</span>
                                                @break
                                            @case('departed')
                                                <span class="badge bg-secondary">غادرت</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $ship->cargos->count() }}</span>
                                    </td>
                                    <td>{{ $ship->created_at->format('Y/m/d') }}</td>
                                    <td>
                                        @can('ships.view')
                                        <a href="{{ route('ships.show', $ship) }}" class="btn btn-sm btn-outline-info" title="عرض تفاصيل السفينة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-ship fa-2x text-muted mb-2"></i>
                        <p class="text-muted">لا توجد سفن مرتبطة بهذا الوكيل</p>
                        @can('ships.create')
                        <a href="{{ route('ships.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة سفينة جديدة
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
        
        <!-- الفواتير -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    الفواتير ({{ $agent->invoices->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($agent->invoices->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>تاريخ الفاتورة</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($agent->invoices as $invoice)
                                <tr>
                                    <td><strong>{{ $invoice->invoice_number }}</strong></td>
                                    <td>{{ $invoice->invoice_date->format('Y/m/d') }}</td>
                                    <td>{{ number_format($invoice->total_amount, 2) }} ل.س</td>
                                    <td>{{ number_format($invoice->paid_amount, 2) }} ل.س</td>
                                    <td>
                                        @switch($invoice->status)
                                            @case('draft')
                                                <span class="badge bg-secondary">مسودة</span>
                                                @break
                                            @case('sent')
                                                <span class="badge bg-warning">مرسلة</span>
                                                @break
                                            @case('paid')
                                                <span class="badge bg-success">مدفوعة</span>
                                                @break
                                            @case('overdue')
                                                <span class="badge bg-danger">متأخرة</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge bg-dark">ملغية</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        @can('invoices.view')
                                        <button class="btn btn-sm btn-outline-info" title="عرض الفاتورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @endcan
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                        <p class="text-muted">لا توجد فواتير لهذا الوكيل</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-md-4">
        <!-- إحصائيات سريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ $agent->ships->count() }}</h4>
                            <small class="text-muted">إجمالي السفن</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success">{{ $agent->ships->whereIn('status', ['outside_port', 'berthed'])->count() }}</h4>
                            <small class="text-muted">سفن في الميناء</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-warning">{{ $agent->invoices->count() }}</h4>
                            <small class="text-muted">إجمالي الفواتير</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-info">{{ $agent->invoices->where('status', 'paid')->sum('total_amount') }}</h4>
                            <small class="text-muted">إجمالي المدفوعات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- السفن الحالية في الميناء -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-anchor me-2"></i>
                    السفن في الميناء
                </h5>
            </div>
            <div class="card-body">
                @php
                    $shipsInPort = $agent->ships->whereIn('status', ['outside_port', 'berthed']);
                @endphp
                
                @if($shipsInPort->count() > 0)
                    @foreach($shipsInPort as $ship)
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <div>
                            <strong>{{ $ship->name }}</strong><br>
                            <small class="text-muted">{{ $ship->ship_type }}</small>
                        </div>
                        <div class="text-end">
                            @if($ship->status == 'berthed')
                                <span class="badge bg-success">مرسية</span>
                            @else
                                <span class="badge bg-warning">خارج الحوض</span>
                            @endif
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-anchor fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد سفن في الميناء</p>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @can('ships.create')
                    <a href="{{ route('ships.create') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سفينة جديدة
                    </a>
                    @endcan
                    
                    @can('invoices.create')
                    <button class="btn btn-outline-success btn-sm">
                        <i class="fas fa-file-invoice me-2"></i>
                        إنشاء فاتورة
                    </button>
                    @endcan
                    
                    @can('reports.view')
                    <button class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقرير الوكيل
                    </button>
                    @endcan
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
