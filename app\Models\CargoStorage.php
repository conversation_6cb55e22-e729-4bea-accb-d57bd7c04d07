<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CargoStorage extends Model
{
    protected $table = 'cargo_storage';

    protected $fillable = [
        'cargo_id',
        'warehouse_id',
        'stored_quantity',
        'storage_start',
        'storage_end',
        'storage_days',
        'daily_rate',
        'total_storage_cost',
        'status',
        'notes'
    ];

    protected $casts = [
        'storage_start' => 'datetime',
        'storage_end' => 'datetime',
        'stored_quantity' => 'decimal:2',
        'daily_rate' => 'decimal:2',
        'total_storage_cost' => 'decimal:2',
        'storage_days' => 'integer'
    ];

    public function cargo(): BelongsTo
    {
        return $this->belongsTo(Cargo::class);
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    // خصائص مساعدة
    public function getQuantityAttribute()
    {
        return $this->stored_quantity;
    }

    public function getEntryDateAttribute()
    {
        return $this->storage_start;
    }

    public function getExitDateAttribute()
    {
        return $this->storage_end;
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active' && is_null($this->storage_end);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')->whereNull('storage_end');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed')->whereNotNull('storage_end');
    }
}
