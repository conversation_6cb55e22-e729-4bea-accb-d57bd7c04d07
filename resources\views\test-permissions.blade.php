@extends('layouts.port-app')

@section('page-title', 'اختبار الصلاحيات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        اختبار الصلاحيات والأدوار
                    </h5>
                </div>
                <div class="card-body">
                    <h6>معلومات المستخدم الحالي:</h6>
                    <ul>
                        <li><strong>الاسم:</strong> {{ Auth::user()->name }}</li>
                        <li><strong>البريد الإلكتروني:</strong> {{ Auth::user()->email }}</li>
                        <li><strong>الأدوار:</strong> 
                            @if(Auth::user()->roles->count() > 0)
                                @foreach(Auth::user()->roles as $role)
                                    <span class="badge bg-primary me-1">{{ $role->name }}</span>
                                @endforeach
                            @else
                                <span class="text-muted">لا توجد أدوار</span>
                            @endif
                        </li>
                    </ul>

                    <hr>

                    <h6>اختبار الصلاحيات:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h6>إدارة المستخدمين</h6>
                                    @can('users.view')
                                        <span class="badge bg-success">مسموح</span>
                                    @else
                                        <span class="badge bg-danger">غير مسموح</span>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-invoice fa-2x text-primary mb-2"></i>
                                    <h6>الفواتير</h6>
                                    @can('invoices.view')
                                        <span class="badge bg-success">مسموح</span>
                                    @else
                                        <span class="badge bg-danger">غير مسموح</span>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-ship fa-2x text-primary mb-2"></i>
                                    <h6>السفن</h6>
                                    @can('ships.view')
                                        <span class="badge bg-success">مسموح</span>
                                    @else
                                        <span class="badge bg-danger">غير مسموح</span>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-2x text-primary mb-2"></i>
                                    <h6>التقارير</h6>
                                    @can('reports.view')
                                        <span class="badge bg-success">مسموح</span>
                                    @else
                                        <span class="badge bg-danger">غير مسموح</span>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <h6>الروابط المهمة:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        @can('users.view')
                            <a href="{{ route('users.index') }}" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>
                                إدارة المستخدمين
                            </a>
                        @endcan
                        
                        @can('invoices.view')
                            <a href="{{ route('invoices.index') }}" class="btn btn-success">
                                <i class="fas fa-file-invoice me-2"></i>
                                الفواتير
                            </a>
                        @endcan
                        
                        @can('ships.view')
                            <a href="{{ route('ships.index') }}" class="btn btn-info">
                                <i class="fas fa-ship me-2"></i>
                                السفن
                            </a>
                        @endcan
                        
                        @can('reports.view')
                            <a href="{{ route('reports.index') }}" class="btn btn-warning">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 