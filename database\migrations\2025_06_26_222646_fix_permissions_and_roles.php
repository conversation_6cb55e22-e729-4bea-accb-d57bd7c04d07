<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إنشاء الصلاحيات المطلوبة
        $permissions = [
            // صلاحيات السفن
            ['name' => 'ships.view', 'display_name' => 'عرض السفن', 'guard_name' => 'web'],
            ['name' => 'ships.create', 'display_name' => 'إضافة سفن', 'guard_name' => 'web'],
            ['name' => 'ships.edit', 'display_name' => 'تعديل السفن', 'guard_name' => 'web'],
            ['name' => 'ships.delete', 'display_name' => 'حذف السفن', 'guard_name' => 'web'],

            // صلاحيات الوكلاء
            ['name' => 'agents.view', 'display_name' => 'عرض الوكلاء', 'guard_name' => 'web'],
            ['name' => 'agents.create', 'display_name' => 'إضافة وكلاء', 'guard_name' => 'web'],
            ['name' => 'agents.edit', 'display_name' => 'تعديل الوكلاء', 'guard_name' => 'web'],
            ['name' => 'agents.delete', 'display_name' => 'حذف الوكلاء', 'guard_name' => 'web'],

            // صلاحيات الأرصفة
            ['name' => 'berths.view', 'display_name' => 'عرض الأرصفة', 'guard_name' => 'web'],
            ['name' => 'berths.create', 'display_name' => 'إضافة أرصفة', 'guard_name' => 'web'],
            ['name' => 'berths.edit', 'display_name' => 'تعديل الأرصفة', 'guard_name' => 'web'],
            ['name' => 'berths.delete', 'display_name' => 'حذف الأرصفة', 'guard_name' => 'web'],

            // صلاحيات الحمولات
            ['name' => 'cargos.view', 'display_name' => 'عرض الحمولات', 'guard_name' => 'web'],
            ['name' => 'cargos.create', 'display_name' => 'إضافة حمولات', 'guard_name' => 'web'],
            ['name' => 'cargos.edit', 'display_name' => 'تعديل الحمولات', 'guard_name' => 'web'],
            ['name' => 'cargos.delete', 'display_name' => 'حذف الحمولات', 'guard_name' => 'web'],

            // صلاحيات المستودعات
            ['name' => 'warehouses.view', 'display_name' => 'عرض المستودعات', 'guard_name' => 'web'],
            ['name' => 'warehouses.create', 'display_name' => 'إضافة مستودعات', 'guard_name' => 'web'],
            ['name' => 'warehouses.edit', 'display_name' => 'تعديل المستودعات', 'guard_name' => 'web'],
            ['name' => 'warehouses.delete', 'display_name' => 'حذف المستودعات', 'guard_name' => 'web'],

            // صلاحيات التفتيش
            ['name' => 'inspections.view', 'display_name' => 'عرض التفتيشات', 'guard_name' => 'web'],
            ['name' => 'inspections.create', 'display_name' => 'إضافة تفتيشات', 'guard_name' => 'web'],
            ['name' => 'inspections.edit', 'display_name' => 'تعديل التفتيشات', 'guard_name' => 'web'],
            ['name' => 'inspections.delete', 'display_name' => 'حذف التفتيشات', 'guard_name' => 'web'],




            // صلاحيات الفواتير
            ['name' => 'invoices.view', 'display_name' => 'عرض الفواتير', 'guard_name' => 'web'],
            ['name' => 'invoices.create', 'display_name' => 'إضافة فواتير', 'guard_name' => 'web'],
            ['name' => 'invoices.edit', 'display_name' => 'تعديل الفواتير', 'guard_name' => 'web'],
            ['name' => 'invoices.delete', 'display_name' => 'حذف الفواتير', 'guard_name' => 'web'],

            // صلاحيات التقارير
            ['name' => 'reports.view', 'display_name' => 'عرض التقارير', 'guard_name' => 'web'],
            ['name' => 'reports.export', 'display_name' => 'تصدير التقارير', 'guard_name' => 'web'],

            // صلاحيات إدارة المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين', 'guard_name' => 'web'],
            ['name' => 'users.create', 'display_name' => 'إضافة مستخدمين', 'guard_name' => 'web'],
            ['name' => 'users.edit', 'display_name' => 'تعديل المستخدمين', 'guard_name' => 'web'],
            ['name' => 'users.delete', 'display_name' => 'حذف المستخدمين', 'guard_name' => 'web'],

            // صلاحيات النظام
            ['name' => 'system.settings', 'display_name' => 'إعدادات النظام', 'guard_name' => 'web'],
            ['name' => 'system.backup', 'display_name' => 'النسخ الاحتياطي', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            \Spatie\Permission\Models\Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }

        // إنشاء الأدوار
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'مدير النظام',
                'guard_name' => 'web',
                'permissions' => array_column($permissions, 'name')
            ],
            [
                'name' => 'port_manager',
                'display_name' => 'مدير المرفأ',
                'guard_name' => 'web',
                'permissions' => [
                    'ships.view', 'ships.create', 'ships.edit',
                    'agents.view', 'agents.create', 'agents.edit',
                    'berths.view', 'berths.create', 'berths.edit',
                    'cargos.view', 'cargos.create', 'cargos.edit',
                    'warehouses.view', 'warehouses.create', 'warehouses.edit',
                    'inspections.view', 'inspections.create', 'inspections.edit',
                    'invoices.view', 'invoices.create', 'invoices.edit',
                    'reports.view', 'reports.export'
                ]
            ],
            [
                'name' => 'operations_manager',
                'display_name' => 'مدير العمليات',
                'guard_name' => 'web',
                'permissions' => [
                    'ships.view', 'ships.create', 'ships.edit',
                    'berths.view', 'berths.edit',
                    'cargos.view', 'cargos.create', 'cargos.edit',
                    'warehouses.view', 'warehouses.edit',
                    'reports.view'
                ]
            ],
            [
                'name' => 'financial_manager',
                'display_name' => 'مدير مالي',
                'guard_name' => 'web',
                'permissions' => [
                    'ships.view',
                    'agents.view',
                    'invoices.view', 'invoices.create', 'invoices.edit',
                    'reports.view', 'reports.export'
                ]
            ],
            [
                'name' => 'inspector',
                'display_name' => 'مفتش',
                'guard_name' => 'web',
                'permissions' => [
                    'ships.view',
                    'inspections.view', 'inspections.create', 'inspections.edit',
                    'reports.view'
                ]
            ],
            [
                'name' => 'operator',
                'display_name' => 'مشغل',
                'guard_name' => 'web',
                'permissions' => [
                    'ships.view',
                    'berths.view',
                    'cargos.view',
                    'warehouses.view',
                    'reports.view'
                ]
            ]
        ];

        foreach ($roles as $roleData) {
            $role = \Spatie\Permission\Models\Role::firstOrCreate(
                ['name' => $roleData['name'], 'guard_name' => $roleData['guard_name']],
                ['display_name' => $roleData['display_name']]
            );

            // ربط الصلاحيات بالدور
            $role->syncPermissions($roleData['permissions']);
        }

        // إنشاء المستخدم الافتراضي وربطه بدور super_admin
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => \Illuminate\Support\Facades\Hash::make('password123'),
                'email_verified_at' => now()
            ]
        );

        $user->assignRole('super_admin');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // حذف جميع الصلاحيات والأدوار
        \Spatie\Permission\Models\Permission::truncate();
        \Spatie\Permission\Models\Role::truncate();
        \Illuminate\Support\Facades\DB::table('model_has_permissions')->truncate();
        \Illuminate\Support\Facades\DB::table('model_has_roles')->truncate();
        \Illuminate\Support\Facades\DB::table('role_has_permissions')->truncate();
    }
};
