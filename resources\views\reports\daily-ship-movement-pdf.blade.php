<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تقرير حركة السفن اليومي - {{ $report_date->format('Y/m/d') }}</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body, * {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        body {
            font-size: 12px;
            line-height: 1.4;
            direction: rtl;
            text-align: right;
            font-family: 'Noto Sans Arabic', '<PERSON>ja<PERSON><PERSON> Sans', Arial, Tahoma, sans-serif !important;
            font-weight: 400;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .header h1, .header h2, .header h3 {
            font-family: 'Noto Sans Arabic', '<PERSON><PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif !important;
            font-weight: 600;
        }
        
        .header h1 {
            font-size: 18px;
            margin: 5px 0;
            color: #2c3e50;
        }
        
        .header h2 {
            font-size: 16px;
            margin: 5px 0;
            color: #34495e;
        }
        
        .header h3 {
            font-size: 14px;
            margin: 5px 0;
            color: #7f8c8d;
        }
        
        .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        
        .section-title {
            background-color: #3498db;
            color: white;
            padding: 8px 12px;
            margin-bottom: 10px;
            font-weight: 600;
            font-size: 14px;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .section-title.warning {
            background-color: #f39c12;
        }
        
        .section-title.success {
            background-color: #27ae60;
        }
        
        .section-title.danger {
            background-color: #e74c3c;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #bdc3c7;
            padding: 6px 8px;
            text-align: right;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        th {
            background-color: #ecf0f1;
            font-weight: 600;
            color: #2c3e50;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .summary {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }
        
        .summary-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #bdc3c7;
            background-color: #f8f9fa;
            flex: 1;
            margin: 0 5px;
        }
        
        .summary-number {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .summary-label {
            font-size: 10px;
            color: #7f8c8d;
            margin-top: 5px;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #bdc3c7;
            font-size: 10px;
            color: #7f8c8d;
        }
        
        .footer p {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <h1>الجمهورية العربية السورية</h1>
        <h2>وزارة النقل - المؤسسة العامة للموانئ</h2>
        <h2>مرفأ اللاذقية</h2>
        <h3>تقرير حركة السفن اليومي</h3>
        <p>التاريخ: {{ $report_date->format('Y/m/d') }} - {{ $report_date->locale('ar')->translatedFormat('l') }}</p>
    </div>

    <!-- إعلانات الوصول -->
    <div class="section">
        <div class="section-title">إعلانات الوصول</div>
        
        @if($arrival_announcements && $arrival_announcements->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم السفينة</th>
                        <th>الوكيل الملاحي</th>
                        <th>نوع الحمولة</th>
                        <th>الكمية</th>
                        <th>تاريخ الوصول المتوقع</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($arrival_announcements as $ship)
                    <tr>
                        <td>{{ $ship->name }}</td>
                        <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->pluck('cargo_type')->join(', ') }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->sum('quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>{{ $ship->expected_arrival_date ? $ship->expected_arrival_date->format('Y/m/d H:i') : 'غير محدد' }}</td>
                        <td>{{ $ship->notes ?? '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="no-data">لا توجد إعلانات وصول لهذا التاريخ</div>
        @endif
    </div>

    <!-- السفن الموجودة خارج الحوض -->
    <div class="section">
        <div class="section-title warning">السفن الموجودة خارج الحوض</div>
        
        @if($ships_outside_port && $ships_outside_port->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم السفينة</th>
                        <th>تاريخ الوصول</th>
                        <th>نوع الحمولة</th>
                        <th>الكمية</th>
                        <th>الوكيل الملاحي</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($ships_outside_port as $ship)
                    <tr>
                        <td>{{ $ship->name }}</td>
                        <td>{{ $ship->actual_arrival_date ? $ship->actual_arrival_date->format('Y/m/d') : 'غير محدد' }}</td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->pluck('cargo_type')->join(', ') }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->sum('quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                        <td>{{ $ship->notes ?? '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="no-data">لا توجد سفن خارج الحوض</div>
        @endif
    </div>

    <!-- السفن الموجودة على الأرصفة -->
    <div class="section">
        <div class="section-title success">السفن الموجودة على الأرصفة والمكسر</div>
        
        @if($ships_on_berths && $ships_on_berths->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم السفينة</th>
                        <th>تاريخ الرسو</th>
                        <th>الرصيف</th>
                        <th>نوع الحمولة</th>
                        <th>الكمية</th>
                        <th>الوكيل الملاحي</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($ships_on_berths as $ship)
                    <tr>
                        <td>{{ $ship->name }}</td>
                        <td>{{ $ship->actual_arrival_date ? $ship->actual_arrival_date->format('Y/m/d') : 'غير محدد' }}</td>
                        <td>
                            @if($ship->currentBerth && $ship->currentBerth->berth)
                                {{ $ship->currentBerth->berth->name }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->pluck('cargo_type')->join(', ') }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->sum('quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                        <td>{{ $ship->notes ?? '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="no-data">لا توجد سفن مرسية على الأرصفة</div>
        @endif
    </div>

    <!-- السفن التي غادرت -->
    <div class="section">
        <div class="section-title danger">السفن التي غادرت خلال 24 ساعة الأخيرة</div>
        
        @if($departed_ships && $departed_ships->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th>اسم السفينة</th>
                        <th>تاريخ المغادرة</th>
                        <th>الكمية المصدرة</th>
                        <th>الوكيل الملاحي</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($departed_ships as $ship)
                    <tr>
                        <td>{{ $ship->name }}</td>
                        <td>{{ $ship->departure_date ? $ship->departure_date->format('Y/m/d H:i') : 'غير محدد' }}</td>
                        <td>
                            @if($ship->cargos->count() > 0)
                                {{ $ship->cargos->where('operation_type', 'loading')->sum('completed_quantity') }} {{ $ship->cargos->first()->unit ?? '' }}
                            @else
                                غير محدد
                            @endif
                        </td>
                        <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                        <td>{{ $ship->notes ?? '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="no-data">لا توجد سفن غادرت خلال 24 ساعة الأخيرة</div>
        @endif
    </div>

    <!-- ملخص التقرير -->
    <div class="section">
        <div class="section-title">ملخص التقرير</div>
        <div class="summary">
            <div class="summary-item">
                <div class="summary-number">{{ $arrival_announcements ? $arrival_announcements->count() : 0 }}</div>
                <div class="summary-label">إعلانات الوصول</div>
            </div>
            <div class="summary-item">
                <div class="summary-number">{{ $ships_outside_port ? $ships_outside_port->count() : 0 }}</div>
                <div class="summary-label">سفن خارج الحوض</div>
            </div>
            <div class="summary-item">
                <div class="summary-number">{{ $ships_on_berths ? $ships_on_berths->count() : 0 }}</div>
                <div class="summary-label">سفن مرسية</div>
            </div>
            <div class="summary-item">
                <div class="summary-number">{{ $departed_ships ? $departed_ships->count() : 0 }}</div>
                <div class="summary-label">سفن مغادرة</div>
            </div>
        </div>
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بتاريخ {{ now()->format('Y/m/d H:i') }}</p>
        <p>نظام إدارة المرفأ البحري - الإصدار 1.0</p>
    </div>
</body>
</html>
