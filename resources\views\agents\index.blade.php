@extends('layouts.port-app')

@section('page-title', 'إدارة الوكلاء الملاحيين')

@section('page-actions')
<div class="btn-group">
    @can('agents.create')
    <a href="{{ route('agents.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة وكيل ملاحي جديد
    </a>
    @endcan
</div>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-md-12">
        <!-- فلاتر البحث -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر البحث
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('agents.index') }}">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث في اسم الوكيل أو الشركة</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="اسم الوكيل أو الشركة...">
                        </div>
                        <div class="col-md-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="">جميع الحالات</option>
                                <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>
                                    نشط
                                </option>
                                <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>
                                    غير نشط
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="{{ route('agents.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    قائمة الوكلاء الملاحيين ({{ $agents->total() }} وكيل)
                </h5>
            </div>
            <div class="card-body">
                @if($agents->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الوكيل</th>
                                    <th>اسم الشركة</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الترخيص</th>
                                    <th>عدد السفن</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($agents as $agent)
                                <tr>
                                    <td>
                                        <strong>{{ $agent->name }}</strong>
                                    </td>
                                    <td>{{ $agent->company_name ?? 'غير محدد' }}</td>
                                    <td>
                                        @if($agent->phone)
                                            <a href="tel:{{ $agent->phone }}" class="text-decoration-none">
                                                {{ $agent->phone }}
                                            </a>
                                        @else
                                            غير محدد
                                        @endif
                                    </td>
                                    <td>
                                        @if($agent->email)
                                            <a href="mailto:{{ $agent->email }}" class="text-decoration-none">
                                                {{ $agent->email }}
                                            </a>
                                        @else
                                            غير محدد
                                        @endif
                                    </td>
                                    <td>{{ $agent->license_number ?? 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $agent->ships_count }}</span>
                                    </td>
                                    <td>
                                        @if($agent->is_active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-secondary">غير نشط</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @can('agents.view')
                                            <a href="{{ route('agents.show', $agent) }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('agents.edit')
                                            <a href="{{ route('agents.edit', $agent) }}" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('agents.delete')
                                            <button type="button" class="btn btn-outline-danger" title="حذف"
                                                    onclick="confirmDelete('{{ $agent->id }}', '{{ $agent->name }}', {{ $agent->ships_count }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $agents->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد وكلاء ملاحيون</h5>
                        <p class="text-muted">لم يتم العثور على أي وكلاء ملاحيين تطابق معايير البحث</p>
                        @can('agents.create')
                        <a href="{{ route('agents.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة وكيل ملاحي جديد
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الوكيل الملاحي <strong id="agentName"></strong>؟</p>
                <div id="warningMessage" class="text-danger" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا يمكن حذف هذا الوكيل لأنه مرتبط بسفن موجودة
                </div>
                <p id="confirmMessage" class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم حذف جميع البيانات المرتبطة بهذا الوكيل نهائياً
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="deleteButton">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(agentId, agentName, shipsCount) {
    document.getElementById('agentName').textContent = agentName;
    document.getElementById('deleteForm').action = '/agents/' + agentId;
    
    const warningMessage = document.getElementById('warningMessage');
    const confirmMessage = document.getElementById('confirmMessage');
    const deleteButton = document.getElementById('deleteButton');
    
    if (shipsCount > 0) {
        warningMessage.style.display = 'block';
        confirmMessage.style.display = 'none';
        deleteButton.style.display = 'none';
    } else {
        warningMessage.style.display = 'none';
        confirmMessage.style.display = 'block';
        deleteButton.style.display = 'inline-block';
    }
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
