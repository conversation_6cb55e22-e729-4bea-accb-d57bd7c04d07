<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ship;
use App\Models\Agent;
use Illuminate\Support\Facades\Gate;

class ShipController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Gate::authorize('ships.view');

        $query = Ship::with(['agent', 'cargos']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب الوكيل
        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        // بحث في اسم السفينة
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $ships = $query->orderBy('created_at', 'desc')->paginate(15);
        $agents = Agent::where('is_active', true)->get();

        return view('ships.index', compact('ships', 'agents'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        Gate::authorize('ships.create');

        $agents = Agent::where('is_active', true)->get();

        return view('ships.create', compact('agents'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Gate::authorize('ships.create');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'imo_number' => 'nullable|string|max:255',
            'flag' => 'required|string|max:255',
            'ship_type' => 'required|string|max:255',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'draft' => 'nullable|numeric|min:0',
            'gross_tonnage' => 'nullable|integer|min:0',
            'agent_id' => 'required|exists:agents,id',
            'owner_company' => 'nullable|string|max:255',
            'arrival_notice_date' => 'nullable|date',
            'expected_arrival_date' => 'nullable|date',
            'actual_arrival_date' => 'nullable|date',
            'departure_date' => 'nullable|date',
            'status' => 'required|in:announced,outside_port,berthed,departed',
            'notes' => 'nullable|string'
        ]);

        Ship::create($validated);

        return redirect()->route('ships.index')
            ->with('success', 'تم إضافة السفينة بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Ship $ship)
    {
        Gate::authorize('ships.view');

        $ship->load(['agent', 'cargos', 'inspections', 'invoices', 'berthHistory.berth']);

        return view('ships.show', compact('ship'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Ship $ship)
    {
        Gate::authorize('ships.edit');

        $agents = Agent::where('is_active', true)->get();

        return view('ships.edit', compact('ship', 'agents'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Ship $ship)
    {
        Gate::authorize('ships.edit');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'imo_number' => 'nullable|string|max:255',
            'flag' => 'required|string|max:255',
            'ship_type' => 'required|string|max:255',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'draft' => 'nullable|numeric|min:0',
            'gross_tonnage' => 'nullable|integer|min:0',
            'agent_id' => 'required|exists:agents,id',
            'owner_company' => 'nullable|string|max:255',
            'arrival_notice_date' => 'nullable|date',
            'expected_arrival_date' => 'nullable|date',
            'actual_arrival_date' => 'nullable|date',
            'departure_date' => 'nullable|date',
            'status' => 'required|in:announced,outside_port,berthed,departed',
            'notes' => 'nullable|string'
        ]);

        $ship->update($validated);

        return redirect()->route('ships.index')
            ->with('success', 'تم تحديث بيانات السفينة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Ship $ship)
    {
        Gate::authorize('ships.delete');

        $ship->delete();

        return redirect()->route('ships.index')
            ->with('success', 'تم حذف السفينة بنجاح');
    }
}
