<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير استخدام الأرصفة</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body, * {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON><PERSON>', <PERSON><PERSON>, Tahoma, sans-serif !important;
        }
        body {
            font-size: 12px;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .amount {
            font-weight: bold;
        }
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <h2>تقرير استخدام الأرصفة</h2>
    <p>الفترة: {{ $date_from->format('Y/m/d') }} - {{ $date_to->format('Y/m/d') }}</p>
    <table>
        <thead>
            <tr>
                <th>اسم الرصيف</th>
                <th>عدد الأيام الكلي</th>
                <th>عدد الأيام المشغولة</th>
                <th>نسبة الإشغال</th>
                <th>عدد السفن</th>
                <th>إجمالي الإيرادات (ل.س)</th>
                <th>متوسط الإيراد اليومي (ل.س)</th>
            </tr>
        </thead>
        <tbody>
            @foreach($berth_stats as $stat)
            <tr>
                <td>{{ $stat['berth']->name }}</td>
                <td>{{ $stat['total_days'] }}</td>
                <td>{{ $stat['occupied_days'] }}</td>
                <td>{{ number_format($stat['utilization_rate'], 1) }}%</td>
                <td>{{ $stat['ships_count'] }}</td>
                <td class="amount">{{ number_format($stat['total_revenue'], 0) }}</td>
                <td class="amount">{{ number_format($stat['average_revenue_per_day'], 0) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المرفأ البحري</p>
        <p>{{ config('app.name') }} - تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>
</body>
</html> 