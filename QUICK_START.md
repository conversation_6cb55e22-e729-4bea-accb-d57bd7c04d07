# دليل البدء السريع - نظام إدارة المرفأ البحري

## 🚀 البدء السريع

### 1. إعداد قاعدة البيانات MySQL

```sql
-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE port_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE port_management;
```

### 2. تحديث ملف .env

```env
APP_NAME="نظام إدارة المرفأ البحري"
APP_LOCALE=ar
APP_FALLBACK_LOCALE=ar
APP_TIMEZONE=Asia/Damascus

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=port_management
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
```

### 3. تشغيل الجداول والبيانات الأولية

```bash
# إنشاء الجداول وإدخال البيانات الأولية
php artisan migrate:fresh --seed
```

### 4. تشغيل الخادم

```bash
php artisan serve
```

### 5. تسجيل الدخول

افتح المتصفح وانتقل إلى: `http://localhost:8000`

**بيانات المدير الافتراضي:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password123`

## 📋 الوحدات المتاحة حالياً

### ✅ جاهزة للاستخدام:
1. **لوحة التحكم** - عرض الإحصائيات العامة
2. **التقارير** - تقرير حركة السفن اليومي مع تصدير PDF
3. **نظام الصلاحيات** - إدارة المستخدمين والأدوار

### 🚧 قيد التطوير:
1. **إدارة السفن** - إضافة وتعديل بيانات السفن
2. **إدارة الوكلاء** - إدارة الوكلاء الملاحيين
3. **إدارة الأرصفة** - تخصيص وإدارة الأرصفة
4. **إدارة الحمولات** - متابعة البضائع
5. **المستودعات** - إدارة التخزين
6. **التفتيش** - تسجيل نتائج التفتيش
7. **الفواتير** - النظام المالي

## 🔐 الأدوار والصلاحيات

### الأدوار المتاحة:
1. **مدير عام** - جميع الصلاحيات
2. **موظف سفن** - إدارة السفن والوكلاء والأرصفة
3. **موظف مالي** - الفواتير والتقارير المالية
4. **موظف مستودعات** - إدارة المستودعات والتخزين
5. **مفتش** - تسجيل نتائج التفتيش
6. **موظف تقارير** - عرض وتصدير التقارير

## 📊 التقارير المتاحة

### 1. تقرير حركة السفن اليومي
- **الوصول**: التقارير > تقرير حركة السفن اليومي
- **المحتوى**: 
  - إعلانات الوصول
  - السفن خارج الحوض
  - السفن المرسية
  - السفن المغادرة
- **التصدير**: PDF مع تنسيق احترافي

### 2. التقارير المالية (قريباً)
- تقارير الإيرادات الشهرية
- تقارير الفواتير المعلقة
- تحليل الربحية

## 🗄️ هيكل قاعدة البيانات

### الجداول الرئيسية:
```
agents              - الوكلاء الملاحيون
ships               - السفن
berths              - الأرصفة
cargos              - الحمولات
warehouses          - المستودعات
inspections         - التفتيش
invoices            - الفواتير
cargo_storage       - تخزين البضائع
ship_berth_history  - تاريخ رسو السفن
```

### جداول الصلاحيات:
```
users               - المستخدمون
roles               - الأدوار
permissions         - الصلاحيات
model_has_roles     - ربط المستخدمين بالأدوار
role_has_permissions - ربط الأدوار بالصلاحيات
```

## 🛠️ أوامر مفيدة

```bash
# إعادة تشغيل الجداول مع البيانات الأولية
php artisan migrate:fresh --seed

# إنشاء مستخدم جديد (عبر Tinker)
php artisan tinker
>>> $user = \App\Models\User::create(['name' => 'اسم المستخدم', 'email' => '<EMAIL>', 'password' => bcrypt('password')]);
>>> $user->assignRole('مدير عام');

# عرض جميع الأدوار
php artisan tinker
>>> \Spatie\Permission\Models\Role::all();

# عرض جميع الصلاحيات
php artisan tinker
>>> \Spatie\Permission\Models\Permission::all();
```

## 🐛 حل المشاكل الشائعة

### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل MySQL
# تحقق من إعدادات .env
# تأكد من وجود قاعدة البيانات
```

### 2. خطأ في الصلاحيات
```bash
# تأكد من تشغيل Seeder
php artisan db:seed --class=RolePermissionSeeder
```

### 3. مشاكل في عرض التقارير
```bash
# تأكد من تثبيت DomPDF
composer require barryvdh/laravel-dompdf
```

## 📝 خطوات التطوير التالية

1. **إضافة Laravel Breeze للمصادقة**
2. **إكمال Controllers للوحدات**
3. **إنشاء صفحات CRUD للسفن والوكلاء**
4. **إضافة المزيد من التقارير**
5. **تحسين واجهة المستخدم**

## 📞 الدعم

للمساعدة أو الاستفسارات:
1. راجع ملف `PORT_SYSTEM_README.md` للتفاصيل الكاملة
2. راجع ملف `DATABASE_SETUP.md` لإعداد قاعدة البيانات
3. تحقق من ملفات الوثائق في مجلد المشروع

---

**نظام إدارة المرفأ البحري** - جاهز للاستخدام والتطوير! 🚢
