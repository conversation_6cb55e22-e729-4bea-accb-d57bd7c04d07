@extends('layouts.port-app')

@section('page-title', 'تعديل السفينة: ' . $ship->name)

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('ships.show', $ship) }}" class="btn btn-info">
        <i class="fas fa-eye me-2"></i>
        عرض التفاصيل
    </a>
    <a href="{{ route('ships.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات السفينة
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('ships.update', $ship) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم السفينة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $ship->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="imo_number" class="form-label">رقم IMO</label>
                                <input type="text" class="form-control @error('imo_number') is-invalid @enderror" 
                                       id="imo_number" name="imo_number" value="{{ old('imo_number', $ship->imo_number) }}">
                                @error('imo_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="flag" class="form-label">جنسية السفينة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('flag') is-invalid @enderror" 
                                       id="flag" name="flag" value="{{ old('flag', $ship->flag) }}" required>
                                @error('flag')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="ship_type" class="form-label">نوع السفينة <span class="text-danger">*</span></label>
                                <select class="form-select @error('ship_type') is-invalid @enderror" 
                                        id="ship_type" name="ship_type" required>
                                    <option value="">اختر نوع السفينة</option>
                                    <option value="حاويات" {{ old('ship_type', $ship->ship_type) == 'حاويات' ? 'selected' : '' }}>حاويات</option>
                                    <option value="بضائع عامة" {{ old('ship_type', $ship->ship_type) == 'بضائع عامة' ? 'selected' : '' }}>بضائع عامة</option>
                                    <option value="صب جاف" {{ old('ship_type', $ship->ship_type) == 'صب جاف' ? 'selected' : '' }}>صب جاف</option>
                                    <option value="صب سائل" {{ old('ship_type', $ship->ship_type) == 'صب سائل' ? 'selected' : '' }}>صب سائل</option>
                                    <option value="ركاب" {{ old('ship_type', $ship->ship_type) == 'ركاب' ? 'selected' : '' }}>ركاب</option>
                                    <option value="صيد" {{ old('ship_type', $ship->ship_type) == 'صيد' ? 'selected' : '' }}>صيد</option>
                                    <option value="أخرى" {{ old('ship_type', $ship->ship_type) == 'أخرى' ? 'selected' : '' }}>أخرى</option>
                                </select>
                                @error('ship_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="owner_company" class="form-label">الشركة المالكة</label>
                                <input type="text" class="form-control @error('owner_company') is-invalid @enderror" 
                                       id="owner_company" name="owner_company" value="{{ old('owner_company', $ship->owner_company) }}">
                                @error('owner_company')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- المواصفات التقنية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-ruler me-2"></i>
                                المواصفات التقنية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="length" class="form-label">الطول (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('length') is-invalid @enderror" 
                                               id="length" name="length" value="{{ old('length', $ship->length) }}">
                                        @error('length')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="width" class="form-label">العرض (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('width') is-invalid @enderror" 
                                               id="width" name="width" value="{{ old('width', $ship->width) }}">
                                        @error('width')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="draft" class="form-label">العمق (متر)</label>
                                        <input type="number" step="0.01" class="form-control @error('draft') is-invalid @enderror" 
                                               id="draft" name="draft" value="{{ old('draft', $ship->draft) }}">
                                        @error('draft')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="gross_tonnage" class="form-label">الحمولة الإجمالية (طن)</label>
                                        <input type="number" class="form-control @error('gross_tonnage') is-invalid @enderror" 
                                               id="gross_tonnage" name="gross_tonnage" value="{{ old('gross_tonnage', $ship->gross_tonnage) }}">
                                        @error('gross_tonnage')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="agent_id" class="form-label">الوكيل الملاحي <span class="text-danger">*</span></label>
                                <select class="form-select @error('agent_id') is-invalid @enderror" 
                                        id="agent_id" name="agent_id" required>
                                    <option value="">اختر الوكيل الملاحي</option>
                                    @foreach($agents as $agent)
                                        <option value="{{ $agent->id }}" {{ old('agent_id', $ship->agent_id) == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('agent_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة السفينة <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="">اختر الحالة</option>
                                    <option value="announced" {{ old('status', $ship->status) == 'announced' ? 'selected' : '' }}>
                                        إعلان وصول
                                    </option>
                                    <option value="outside_port" {{ old('status', $ship->status) == 'outside_port' ? 'selected' : '' }}>
                                        خارج الحوض
                                    </option>
                                    <option value="berthed" {{ old('status', $ship->status) == 'berthed' ? 'selected' : '' }}>
                                        مرسية
                                    </option>
                                    <option value="departed" {{ old('status', $ship->status) == 'departed' ? 'selected' : '' }}>
                                        غادرت
                                    </option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- التواريخ -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-calendar me-2"></i>
                                التواريخ المهمة
                            </h6>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="arrival_notice_date" class="form-label">تاريخ إشعار الوصول</label>
                                <input type="datetime-local" class="form-control @error('arrival_notice_date') is-invalid @enderror" 
                                       id="arrival_notice_date" name="arrival_notice_date" 
                                       value="{{ old('arrival_notice_date', $ship->arrival_notice_date ? $ship->arrival_notice_date->format('Y-m-d\TH:i') : '') }}">
                                @error('arrival_notice_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="expected_arrival_date" class="form-label">تاريخ الوصول المتوقع</label>
                                <input type="datetime-local" class="form-control @error('expected_arrival_date') is-invalid @enderror" 
                                       id="expected_arrival_date" name="expected_arrival_date" 
                                       value="{{ old('expected_arrival_date', $ship->expected_arrival_date ? $ship->expected_arrival_date->format('Y-m-d\TH:i') : '') }}">
                                @error('expected_arrival_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="actual_arrival_date" class="form-label">تاريخ الوصول الفعلي</label>
                                <input type="datetime-local" class="form-control @error('actual_arrival_date') is-invalid @enderror" 
                                       id="actual_arrival_date" name="actual_arrival_date" 
                                       value="{{ old('actual_arrival_date', $ship->actual_arrival_date ? $ship->actual_arrival_date->format('Y-m-d\TH:i') : '') }}">
                                @error('actual_arrival_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="departure_date" class="form-label">تاريخ المغادرة</label>
                                <input type="datetime-local" class="form-control @error('departure_date') is-invalid @enderror" 
                                       id="departure_date" name="departure_date" 
                                       value="{{ old('departure_date', $ship->departure_date ? $ship->departure_date->format('Y-m-d\TH:i') : '') }}">
                                @error('departure_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3">{{ old('notes', $ship->notes) }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('ships.show', $ship) }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
