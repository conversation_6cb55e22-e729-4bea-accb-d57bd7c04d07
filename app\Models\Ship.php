<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Ship extends Model
{
    protected $fillable = [
        'name',
        'imo_number',
        'flag',
        'ship_type',
        'length',
        'width',
        'draft',
        'gross_tonnage',
        'agent_id',
        'owner_company',
        'arrival_notice_date',
        'expected_arrival_date',
        'actual_arrival_date',
        'departure_date',
        'status',
        'notes'
    ];

    protected $casts = [
        'arrival_notice_date' => 'datetime',
        'expected_arrival_date' => 'datetime',
        'actual_arrival_date' => 'datetime',
        'departure_date' => 'datetime',
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'draft' => 'decimal:2'
    ];

    /**
     * Get the agent for this ship
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get all cargos for this ship
     */
    public function cargos(): HasMany
    {
        return $this->hasMany(Cargo::class);
    }

    /**
     * Get all inspections for this ship
     */
    public function inspections(): HasMany
    {
        return $this->hasMany(Inspection::class);
    }

    /**
     * Get all invoices for this ship
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get berth history for this ship
     */
    public function berthHistory(): HasMany
    {
        return $this->hasMany(ShipBerthHistory::class);
    }

    /**
     * Get current berth for this ship
     */
    public function currentBerth(): HasOne
    {
        return $this->hasOne(ShipBerthHistory::class)->where('status', 'active');
    }
}
