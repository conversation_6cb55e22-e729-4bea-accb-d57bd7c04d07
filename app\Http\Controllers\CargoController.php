<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Cargo;
use App\Models\Ship;
use App\Models\Warehouse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CargosExport;
use Carbon\Carbon;

class CargoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Gate::authorize('cargos.view');

        $query = Cargo::with(['ship', 'storages.warehouse']);

        // فلترة حسب نوع الحمولة
        if ($request->filled('cargo_type')) {
            $query->where('cargo_type', $request->cargo_type);
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب السفينة
        if ($request->filled('ship_id')) {
            $query->where('ship_id', $request->ship_id);
        }

        // البحث في الوصف
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('description', 'like', '%' . $request->search . '%')
                  ->orWhere('origin_country', 'like', '%' . $request->search . '%')
                  ->orWhere('destination_country', 'like', '%' . $request->search . '%');
            });
        }

        $cargos = $query->orderBy('created_at', 'desc')->paginate(15);
        $ships = Ship::orderBy('name')->get();

        // إحصائيات شاملة محسنة
        $stats = [
            'total_cargos' => Cargo::count(),
            'total_weight' => Cargo::sum('weight'),
            'total_volume' => Cargo::sum('volume'),
            'total_value' => Cargo::sum('value'),
            
            // حسب الحالة
            'loading' => Cargo::where('status', 'loading')->count(),
            'loaded' => Cargo::where('status', 'loaded')->count(),
            'unloading' => Cargo::where('status', 'unloading')->count(),
            'unloaded' => Cargo::where('status', 'unloaded')->count(),
            'stored' => Cargo::where('status', 'stored')->count(),
            'delivered' => Cargo::where('status', 'delivered')->count(),
            
            // حسب النوع
            'general_cargo' => Cargo::where('cargo_type', 'general')->count(),
            'container_cargo' => Cargo::where('cargo_type', 'container')->count(),
            'bulk_cargo' => Cargo::where('cargo_type', 'bulk')->count(),
            'liquid_cargo' => Cargo::where('cargo_type', 'liquid')->count(),
            'dangerous_cargo' => Cargo::where('cargo_type', 'dangerous')->count(),
            
            // إحصائيات زمنية
            'today' => Cargo::whereDate('created_at', today())->count(),
            'this_week' => Cargo::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month' => Cargo::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            
            // متوسطات
            'avg_weight' => Cargo::avg('weight'),
            'avg_volume' => Cargo::avg('volume'),
            'avg_value' => Cargo::avg('value'),
        ];

        return view('cargos.index', compact('cargos', 'ships', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        Gate::authorize('cargos.create');

        $ships = Ship::orderBy('name')->get();
        return view('cargos.create', compact('ships'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Gate::authorize('cargos.create');

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id',
            'cargo_type' => 'required|in:container,bulk,liquid,gas,vehicles,general,dangerous,refrigerated',
            'description' => 'required|string|max:500',
            'quantity' => 'required|numeric|min:0',
            'unit' => 'required|in:tons,kg,pieces,containers,pallets,cubic_meters,liters',
            'weight' => 'nullable|numeric|min:0',
            'volume' => 'nullable|numeric|min:0',
            'value' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'origin_country' => 'required|string|max:100',
            'destination_country' => 'required|string|max:100',
            'status' => 'required|in:loading,loaded,in_transit,unloading,unloaded,stored,delivered',
            'loading_date' => 'nullable|date',
            'unloading_date' => 'nullable|date|after_or_equal:loading_date',
            'notes' => 'nullable|string|max:1000'
        ]);

        Cargo::create($validated);

        return redirect()->route('cargos.index')
            ->with('success', 'تم إضافة الحمولة بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Cargo $cargo)
    {
        Gate::authorize('cargos.view');

        $cargo->load(['ship.agent', 'storages.warehouse']);
        return view('cargos.show', compact('cargo'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Cargo $cargo)
    {
        Gate::authorize('cargos.edit');

        $ships = Ship::orderBy('name')->get();
        return view('cargos.edit', compact('cargo', 'ships'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Cargo $cargo)
    {
        Gate::authorize('cargos.edit');

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id',
            'cargo_type' => 'required|in:container,bulk,liquid,gas,vehicles,general,dangerous,refrigerated',
            'description' => 'required|string|max:500',
            'quantity' => 'required|numeric|min:0',
            'unit' => 'required|in:tons,kg,pieces,containers,pallets,cubic_meters,liters',
            'weight' => 'nullable|numeric|min:0',
            'volume' => 'nullable|numeric|min:0',
            'value' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'origin_country' => 'required|string|max:100',
            'destination_country' => 'required|string|max:100',
            'status' => 'required|in:loading,loaded,in_transit,unloading,unloaded,stored,delivered',
            'loading_date' => 'nullable|date',
            'unloading_date' => 'nullable|date|after_or_equal:loading_date',
            'notes' => 'nullable|string|max:1000'
        ]);

        $cargo->update($validated);

        return redirect()->route('cargos.show', $cargo)
            ->with('success', 'تم تحديث الحمولة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Cargo $cargo)
    {
        Gate::authorize('cargos.delete');

        // التحقق من وجود تخزين مرتبط
        if ($cargo->storages()->count() > 0) {
            return redirect()->route('cargos.index')
                ->with('error', 'لا يمكن حذف الحمولة لوجود سجلات تخزين مرتبطة بها');
        }

        $cargo->delete();

        return redirect()->route('cargos.index')
            ->with('success', 'تم حذف الحمولة بنجاح');
    }

    /**
     * تحديث حالة الحمولة
     */
    public function updateStatus(Request $request, Cargo $cargo)
    {
        Gate::authorize('cargos.edit');

        $validated = $request->validate([
            'status' => 'required|in:loading,loaded,in_transit,unloading,unloaded,stored,delivered',
            'notes' => 'nullable|string|max:500'
        ]);

        $cargo->update([
            'status' => $validated['status'],
            'notes' => $validated['notes'] ?? $cargo->notes
        ]);

        return redirect()->back()
            ->with('success', 'تم تحديث حالة الحمولة بنجاح');
    }

    /**
     * Export cargos to Excel
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('cargos.view');

        $query = Cargo::with(['ship.agent']);

        // تطبيق نفس الفلاتر المستخدمة في الفهرس
        if ($request->filled('cargo_type')) {
            $query->where('cargo_type', $request->cargo_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('ship_id')) {
            $query->where('ship_id', $request->ship_id);
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('description', 'like', '%' . $request->search . '%')
                  ->orWhere('origin_country', 'like', '%' . $request->search . '%')
                  ->orWhere('destination_country', 'like', '%' . $request->search . '%');
            });
        }

        $cargos = $query->orderBy('created_at', 'desc')->get();

        return Excel::download(
            new CargosExport($cargos), 
            'cargos-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Bulk actions for cargos
     */
    public function bulkActions(Request $request)
    {
        Gate::authorize('cargos.edit');

        $validated = $request->validate([
            'action' => 'required|in:update_status,delete',
            'cargo_ids' => 'required|array',
            'cargo_ids.*' => 'exists:cargos,id',
            'status' => 'required_if:action,update_status|in:loading,loaded,in_transit,unloading,unloaded,stored,delivered'
        ]);

        $cargos = Cargo::whereIn('id', $validated['cargo_ids']);

        switch ($validated['action']) {
            case 'update_status':
                $cargos->update(['status' => $validated['status']]);
                return redirect()->back()
                    ->with('success', 'تم تحديث حالة الحمولات المحددة بنجاح');
                break;

            case 'delete':
                if (!Gate::allows('cargos.delete')) {
                    abort(403);
                }
                $cargos->delete();
                return redirect()->back()
                    ->with('success', 'تم حذف الحمولات المحددة بنجاح');
                break;
        }

        return redirect()->back();
    }

    /**
     * Advanced cargo report
     */
    public function advancedReport(Request $request)
    {
        Gate::authorize('cargos.view');

        $dateFrom = $request->input('date_from', now()->startOfMonth());
        $dateTo = $request->input('date_to', now()->endOfMonth());

        // إحصائيات شاملة
        $stats = [
            'total_cargos' => Cargo::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'total_weight' => Cargo::whereBetween('created_at', [$dateFrom, $dateTo])->sum('weight'),
            'total_volume' => Cargo::whereBetween('created_at', [$dateFrom, $dateTo])->sum('volume'),
            'total_value' => Cargo::whereBetween('created_at', [$dateFrom, $dateTo])->sum('value'),
            'avg_handling_time' => Cargo::whereBetween('created_at', [$dateFrom, $dateTo])
                ->whereNotNull('handling_start_time')
                ->whereNotNull('handling_end_time')
                ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, handling_start_time, handling_end_time)) as avg_time')
                ->value('avg_time'),
        ];

        // إحصائيات حسب النوع
        $cargoTypeStats = Cargo::selectRaw('cargo_type, COUNT(*) as count, SUM(weight) as total_weight, SUM(volume) as total_volume')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('cargo_type')
            ->orderByDesc('count')
            ->get();

        // إحصائيات حسب الحالة
        $statusStats = Cargo::selectRaw('status, COUNT(*) as count')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('status')
            ->orderByDesc('count')
            ->get();

        // إحصائيات شهرية
        $monthlyStats = Cargo::selectRaw('
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                COUNT(*) as cargo_count,
                SUM(weight) as total_weight,
                SUM(volume) as total_volume
            ')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at) DESC, MONTH(created_at) DESC')
            ->get();

        // أفضل السفن من حيث الحمولة
        $topShips = Cargo::with('ship')
            ->selectRaw('ship_id, COUNT(*) as cargo_count, SUM(weight) as total_weight')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('ship_id')
            ->orderByDesc('total_weight')
            ->limit(10)
            ->get();

        return view('cargos.advanced-report', compact(
            'stats', 'cargoTypeStats', 'statusStats', 'monthlyStats', 'topShips', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * Cargo tracking report
     */
    public function trackingReport(Request $request)
    {
        Gate::authorize('cargos.view');

        $query = Cargo::with(['ship', 'storages.warehouse']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('cargo_type')) {
            $query->where('cargo_type', $request->cargo_type);
        }

        if ($request->filled('ship_id')) {
            $query->where('ship_id', $request->ship_id);
        }

        $cargos = $query->orderBy('created_at', 'desc')->paginate(20);
        $ships = Ship::orderBy('name')->get();

        return view('cargos.tracking-report', compact('cargos', 'ships'));
    }

    /**
     * Quick stats for dashboard
     */
    public function quickStats()
    {
        Gate::authorize('cargos.view');

        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        $stats = [
            'today' => [
                'count' => Cargo::whereDate('created_at', $today)->count(),
                'weight' => Cargo::whereDate('created_at', $today)->sum('weight'),
            ],
            'this_week' => [
                'count' => Cargo::where('created_at', '>=', $thisWeek)->count(),
                'weight' => Cargo::where('created_at', '>=', $thisWeek)->sum('weight'),
            ],
            'this_month' => [
                'count' => Cargo::where('created_at', '>=', $thisMonth)->count(),
                'weight' => Cargo::where('created_at', '>=', $thisMonth)->sum('weight'),
            ],
            'pending' => [
                'loading' => Cargo::where('status', 'loading')->count(),
                'unloading' => Cargo::where('status', 'unloading')->count(),
                'stored' => Cargo::where('status', 'stored')->count(),
            ]
        ];

        return response()->json($stats);
    }
}
