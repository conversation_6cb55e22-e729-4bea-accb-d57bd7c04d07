<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ListPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $permissions = \Spatie\Permission\Models\Permission::all();
        
        $this->info('Available Permissions:');
        foreach ($permissions as $permission) {
            $this->line("- {$permission->name}");
        }
        
        $this->info("\nTotal: " . $permissions->count() . " permissions");
    }
}
