@extends('layouts.port-app')

@section('title', 'تقرير أداء الوكلاء الملاحيين')

@section('content')
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="text-primary">
                    <i class="fas fa-users me-2"></i>
                    تقرير أداء الوكلاء الملاحيين
                </h2>
                <div class="btn-group">
                    <a href="{{ route('reports.agents-performance', array_merge(request()->all(), ['format' => 'pdf'])) }}" 
                       class="btn btn-danger">
                        <i class="fas fa-file-pdf me-2"></i>
                        تحميل PDF
                    </a>
                    <a href="{{ route('reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
            <p class="text-muted">
                الفترة: {{ $date_from->format('Y/m/d') }} - {{ $date_to->format('Y/m/d') }}
                ({{ $date_from->diffInDays($date_to) + 1 }} يوم)
            </p>
        </div>
    </div>

    <!-- فلتر التاريخ -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar me-2"></i>
                اختيار الفترة الزمنية
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('reports.agents-performance') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" 
                               value="{{ request('date_from', $date_from->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" 
                               value="{{ request('date_to', $date_to->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search me-2"></i>
                            عرض التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_agents'] }}</h3>
                    <p class="mb-0">إجمالي الوكلاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['active_agents'] }}</h3>
                    <p class="mb-0">الوكلاء النشطون</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_ships'] }}</h3>
                    <p class="mb-0">إجمالي السفن</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_invoices'] }}</h3>
                    <p class="mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_amount'], 0) }}</h3>
                    <p class="mb-0">إجمالي المبلغ (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_pending'], 0) }}</h3>
                    <p class="mb-0">المبلغ المعلق (ل.س)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل أداء الوكلاء -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل أداء الوكلاء الملاحيين
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الترتيب</th>
                            <th>الوكيل الملاحي</th>
                            <th>معلومات الاتصال</th>
                            <th>عدد السفن</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبلغ</th>
                            <th>المبلغ المحصل</th>
                            <th>المبلغ المعلق</th>
                            <th>معدل السداد</th>
                            <th>متوسط الفاتورة</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($agent_stats as $index => $stat)
                        <tr>
                            <td>
                                <span class="badge bg-{{ $index < 3 ? 'warning' : 'secondary' }} fs-6">
                                    {{ $index + 1 }}
                                </span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $stat['agent']->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $stat['agent']->company_name }}</small>
                                    <br>
                                    @if($stat['agent']->is_active)
                                        <span class="badge bg-success">نشط</span>
                                    @else
                                        <span class="badge bg-danger">غير نشط</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <small>
                                    @if($stat['agent']->phone)
                                        <i class="fas fa-phone me-1"></i>{{ $stat['agent']->phone }}<br>
                                    @endif
                                    @if($stat['agent']->email)
                                        <i class="fas fa-envelope me-1"></i>{{ $stat['agent']->email }}
                                    @endif
                                </small>
                            </td>
                            <td>
                                @if($stat['ships_count'] > 0)
                                    <span class="badge bg-primary">{{ $stat['ships_count'] }}</span>
                                @else
                                    <span class="text-muted">0</span>
                                @endif
                            </td>
                            <td>
                                @if($stat['invoices_count'] > 0)
                                    <span class="badge bg-info">{{ $stat['invoices_count'] }}</span>
                                @else
                                    <span class="text-muted">0</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($stat['total_amount'], 0) }} ل.س</strong>
                            </td>
                            <td>
                                <span class="text-success">{{ number_format($stat['paid_amount'], 0) }} ل.س</span>
                            </td>
                            <td>
                                @if($stat['pending_amount'] > 0)
                                    <span class="text-danger">{{ number_format($stat['pending_amount'], 0) }} ل.س</span>
                                @else
                                    <span class="text-success">0 ل.س</span>
                                @endif
                            </td>
                            <td>
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar 
                                        @if($stat['payment_rate'] >= 90) bg-success
                                        @elseif($stat['payment_rate'] >= 70) bg-info
                                        @elseif($stat['payment_rate'] >= 50) bg-warning
                                        @else bg-danger
                                        @endif" 
                                        role="progressbar" 
                                        style="width: {{ $stat['payment_rate'] }}%">
                                        {{ number_format($stat['payment_rate'], 1) }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                {{ number_format($stat['average_invoice_amount'], 0) }} ل.س
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th>{{ $agent_stats->sum('ships_count') }}</th>
                            <th>{{ $agent_stats->sum('invoices_count') }}</th>
                            <th>{{ number_format($agent_stats->sum('total_amount'), 0) }} ل.س</th>
                            <th>{{ number_format($agent_stats->sum('paid_amount'), 0) }} ل.س</th>
                            <th>{{ number_format($agent_stats->sum('pending_amount'), 0) }} ل.س</th>
                            <th>{{ number_format($agent_stats->avg('payment_rate'), 1) }}%</th>
                            <th>{{ number_format($agent_stats->avg('average_invoice_amount'), 0) }} ل.س</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- تحليل الأداء -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        أفضل 3 وكلاء من حيث الإيرادات
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($agent_stats->take(3) as $index => $stat)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge bg-{{ $index == 0 ? 'warning' : ($index == 1 ? 'secondary' : 'dark') }} me-2">
                                {{ $index + 1 }}
                            </span>
                            <strong>{{ $stat['agent']->name }}</strong>
                            <br>
                            <small class="text-muted">{{ $stat['ships_count'] }} سفينة، {{ $stat['invoices_count'] }} فاتورة</small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success">{{ number_format($stat['total_amount'], 0) }} ل.س</strong>
                            <br>
                            <small class="text-muted">{{ number_format($stat['payment_rate'], 1) }}% سداد</small>
                        </div>
                    </div>
                    @if(!$loop->last)
                        <hr>
                    @endif
                    @endforeach
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تصنيف الوكلاء حسب معدل السداد
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $excellent_payment = $agent_stats->where('payment_rate', '>=', 90)->count();
                        $good_payment = $agent_stats->where('payment_rate', '>=', 70)->where('payment_rate', '<', 90)->count();
                        $fair_payment = $agent_stats->where('payment_rate', '>=', 50)->where('payment_rate', '<', 70)->count();
                        $poor_payment = $agent_stats->where('payment_rate', '<', 50)->count();
                    @endphp
                    
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3 bg-success text-white">
                                <h3>{{ $excellent_payment }}</h3>
                                <p class="mb-0">ممتاز (≥90%)</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3 bg-info text-white">
                                <h3>{{ $good_payment }}</h3>
                                <p class="mb-0">جيد (70-89%)</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3 bg-warning text-dark">
                                <h3>{{ $fair_payment }}</h3>
                                <p class="mb-0">مقبول (50-69%)</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3 bg-danger text-white">
                                <h3>{{ $poor_payment }}</h3>
                                <p class="mb-0">ضعيف (<50%)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        وكلاء يحتاجون متابعة
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $agents_need_follow_up = $agent_stats->where('payment_rate', '<', 70)->where('pending_amount', '>', 0);
                    @endphp
                    
                    @if($agents_need_follow_up->count() > 0)
                        @foreach($agents_need_follow_up->take(5) as $stat)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ $stat['agent']->name }}</strong>
                                <br>
                                <small class="text-muted">معدل السداد: {{ number_format($stat['payment_rate'], 1) }}%</small>
                            </div>
                            <div class="text-end">
                                <span class="text-danger">{{ number_format($stat['pending_amount'], 0) }} ل.س</span>
                                <br>
                                <small class="text-muted">معلق</small>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr>
                        @endif
                        @endforeach
                    @else
                        <div class="text-center text-muted">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>جميع الوكلاء بحالة جيدة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- تحذيرات وتنبيهات -->
    @if($agent_stats->where('payment_rate', '<', 50)->count() > 0)
    <div class="alert alert-danger mt-4">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير: وكلاء بمعدل سداد منخفض</h5>
        <p class="mb-0">
            يوجد {{ $agent_stats->where('payment_rate', '<', 50)->count() }} وكيل/وكلاء بمعدل سداد أقل من 50%. 
            يُنصح بالمتابعة الفورية مع هؤلاء الوكلاء.
        </p>
    </div>
    @endif

    @if($agent_stats->where('pending_amount', '>', 100000)->count() > 0)
    <div class="alert alert-warning mt-4">
        <h5><i class="fas fa-exclamation-circle me-2"></i>تنبيه: مبالغ معلقة كبيرة</h5>
        <p class="mb-0">
            يوجد {{ $agent_stats->where('pending_amount', '>', 100000)->count() }} وكيل/وكلاء لديهم مبالغ معلقة تزيد عن 100,000 ل.س. 
            يُنصح بمراجعة هذه الحسابات.
        </p>
    </div>
    @endif
</div>

@push('scripts')
<script>
// يمكن إضافة مخططات بيانية هنا
</script>
@endpush
@endsection