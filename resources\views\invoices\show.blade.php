@extends('layouts.port-app')

@section('title', 'تفاصيل الفاتورة - ' . $invoice->invoice_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-file-invoice me-2"></i>
                    تفاصيل الفاتورة {{ $invoice->invoice_number }}
                </h2>
                <div class="btn-group">
                    <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                    @can('invoices.view')
                    <a href="{{ route('invoices.print', $invoice) }}" class="btn btn-info" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </a>
                    @endcan
                    @can('invoices.edit')
                    @if(!in_array($invoice->status, ['paid', 'cancelled']))
                    <a href="{{ route('invoices.edit', $invoice) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    @endif
                    @endcan
                    
                    <!-- قائمة منسدلة للإجراءات الإضافية -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>
                            إجراءات إضافية
                        </button>
                        <ul class="dropdown-menu">
                            @can('invoices.edit')
                            @if($invoice->status !== 'cancelled')
                            <li>
                                <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#statusModal">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    تغيير الحالة
                                </button>
                            </li>
                            @endif
                            
                            @if($invoice->remaining_amount > 0 && $invoice->status !== 'cancelled')
                            <li>
                                <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#paymentModal">
                                    <i class="fas fa-money-bill me-2"></i>
                                    إضافة دفعة
                                </button>
                            </li>
                            @endif
                            
                            @if($invoice->status === 'draft')
                            <li>
                                <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#discountModal">
                                    <i class="fas fa-percentage me-2"></i>
                                    تطبيق خصم
                                </button>
                            </li>
                            @endif
                            
                            <li><hr class="dropdown-divider"></li>
                            
                            <li>
                                <a class="dropdown-item" href="{{ route('invoices.print', $invoice) }}?format=pdf" target="_blank">
                                    <i class="fas fa-file-pdf me-2"></i>
                                    تحميل PDF
                                </a>
                            </li>
                            
                            @if($invoice->agent->email)
                            <li>
                                <button type="button" class="dropdown-item" onclick="sendInvoiceEmail()">
                                    <i class="fas fa-envelope me-2"></i>
                                    إرسال بالبريد الإلكتروني
                                </button>
                            </li>
                            @endif
                            @endcan
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-md-8">
                    <!-- المعلومات الأساسية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الفاتورة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الفاتورة:</strong></td>
                                            <td>{{ $invoice->invoice_number }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الفاتورة:</strong></td>
                                            <td>{{ $invoice->invoice_date->format('Y/m/d') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الاستحقاق:</strong></td>
                                            <td>
                                                @if($invoice->due_date)
                                                    {{ $invoice->due_date->format('Y/m/d') }}
                                                    @if($invoice->is_overdue)
                                                        <span class="badge bg-danger ms-2">متأخرة</span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">غير محدد</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $invoice->status_color }} fs-6">
                                                    {{ $invoice->status_label }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>المبلغ الإجمالي:</strong></td>
                                            <td><strong class="text-primary">{{ number_format($invoice->total_amount, 2) }} ل.س</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المبلغ المدفوع:</strong></td>
                                            <td><strong class="text-success">{{ number_format($invoice->paid_amount, 2) }} ل.س</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المبلغ المتبقي:</strong></td>
                                            <td>
                                                @if($invoice->remaining_amount > 0)
                                                    <strong class="text-danger">{{ number_format($invoice->remaining_amount, 2) }} ل.س</strong>
                                                @else
                                                    <strong class="text-success">0.00 ل.س</strong>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>{{ $invoice->created_at->format('Y/m/d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات السفينة والوكيل -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-ship me-2"></i>
                                معلومات السفينة والوكيل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-ship me-2"></i>
                                        السفينة
                                    </h6>
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>اسم السفينة:</strong></td>
                                            <td>
                                                <a href="{{ route('ships.show', $invoice->ship) }}" class="text-decoration-none">
                                                    {{ $invoice->ship->name }}
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>نوع السفينة:</strong></td>
                                            <td>{{ $invoice->ship->ship_type }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الجنسية:</strong></td>
                                            <td>{{ $invoice->ship->flag }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>رقم IMO:</strong></td>
                                            <td>{{ $invoice->ship->imo_number ?? 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحمولة الإجمالية:</strong></td>
                                            <td>{{ $invoice->ship->gross_tonnage ?? 'غير محدد' }} طن</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-user-tie me-2"></i>
                                        الوكيل الملاحي
                                    </h6>
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>اسم الوكيل:</strong></td>
                                            <td>
                                                <a href="{{ route('agents.show', $invoice->agent) }}" class="text-decoration-none">
                                                    {{ $invoice->agent->name }}
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>اسم الشركة:</strong></td>
                                            <td>{{ $invoice->agent->company_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الهاتف:</strong></td>
                                            <td>{{ $invoice->agent->phone }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>البريد الإلكتروني:</strong></td>
                                            <td>{{ $invoice->agent->email }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>رقم الترخيص:</strong></td>
                                            <td>{{ $invoice->agent->license_number }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل الرسوم -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-calculator me-2"></i>
                                تفاصيل الرسوم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>نوع الرسوم</th>
                                            <th class="text-end">المبلغ (ل.س)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>رسوم الرسو</td>
                                            <td class="text-end">{{ number_format($invoice->berthing_fees, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td>رسوم مناولة البضائع</td>
                                            <td class="text-end">{{ number_format($invoice->cargo_handling_fees, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td>رسوم التخزين</td>
                                            <td class="text-end">{{ number_format($invoice->storage_fees, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td>رسوم الإرشاد</td>
                                            <td class="text-end">{{ number_format($invoice->pilotage_fees, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td>رسوم أخرى</td>
                                            <td class="text-end">{{ number_format($invoice->other_fees, 2) }}</td>
                                        </tr>
                                    </tbody>
                                    <tfoot class="table-dark">
                                        <tr>
                                            <th>المجموع الإجمالي</th>
                                            <th class="text-end">{{ number_format($invoice->total_amount, 2) }} ل.س</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    @if($invoice->notes)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sticky-note me-2"></i>
                                ملاحظات
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ $invoice->notes }}</p>
                        </div>
                    </div>
                    @endif

                    <!-- المدفوعات -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-money-bill me-2"></i>
                                سجل المدفوعات ({{ $invoice->payments->count() }})
                            </h5>
                            @can('invoices.edit')
                            @if($invoice->remaining_amount > 0 && !in_array($invoice->status, ['cancelled']))
                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                                <i class="fas fa-plus me-1"></i>
                                إضافة دفعة
                            </button>
                            @endif
                            @endcan
                        </div>
                        <div class="card-body">
                            @if($invoice->payments->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th>تاريخ الدفع</th>
                                                <th>المبلغ</th>
                                                <th>طريقة الدفع</th>
                                                <th>رقم المرجع</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($invoice->payments as $payment)
                                            <tr>
                                                <td>{{ $payment->payment_date->format('Y/m/d') }}</td>
                                                <td><strong class="text-success">{{ number_format($payment->amount, 2) }} ل.س</strong></td>
                                                <td>{{ $payment->payment_method_label }}</td>
                                                <td>{{ $payment->reference_number ?? 'غير محدد' }}</td>
                                                <td>{{ $payment->notes ?? '-' }}</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-money-bill fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">لا توجد مدفوعات لهذه الفاتورة</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-md-4">
                    <!-- إجراءات سريعة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            @can('invoices.edit')
                            @if($invoice->status === 'draft')
                            <form method="POST" action="{{ route('invoices.update-status', $invoice) }}" class="mb-2">
                                @csrf
                                <input type="hidden" name="status" value="sent">
                                <button type="submit" class="btn btn-warning btn-sm w-100">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    إرسال الفاتورة
                                </button>
                            </form>
                            @endif

                            @if(in_array($invoice->status, ['draft', 'sent']) && $invoice->remaining_amount > 0)
                            <form method="POST" action="{{ route('invoices.update-status', $invoice) }}" class="mb-2">
                                @csrf
                                <input type="hidden" name="status" value="paid">
                                <button type="submit" class="btn btn-success btn-sm w-100" 
                                        onclick="return confirm('هل تم دفع الفاتورة بالكامل؟')">
                                    <i class="fas fa-check me-1"></i>
                                    تأكيد الدفع الكامل
                                </button>
                            </form>
                            @endif

                            @if(!in_array($invoice->status, ['paid', 'cancelled']))
                            <form method="POST" action="{{ route('invoices.update-status', $invoice) }}" class="mb-2">
                                @csrf
                                <input type="hidden" name="status" value="cancelled">
                                <button type="submit" class="btn btn-danger btn-sm w-100" 
                                        onclick="return confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')">
                                    <i class="fas fa-ban me-1"></i>
                                    إلغاء الفاتورة
                                </button>
                            </form>
                            @endif
                            @endcan

                            <a href="{{ route('invoices.print', $invoice) }}" class="btn btn-info btn-sm w-100">
                                <i class="fas fa-download me-1"></i>
                                تحميل PDF
                            </a>
                        </div>
                    </div>

                    <!-- ملخص مالي -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                الملخص المالي
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-12 mb-3">
                                    <div class="bg-primary text-white p-3 rounded">
                                        <h4 class="mb-1">{{ number_format($invoice->total_amount, 0) }}</h4>
                                        <small>المبلغ الإجمالي (ل.س)</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="bg-success text-white p-2 rounded">
                                        <h5 class="mb-1">{{ number_format($invoice->paid_amount, 0) }}</h5>
                                        <small>مدفوع</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="bg-{{ $invoice->remaining_amount > 0 ? 'danger' : 'success' }} text-white p-2 rounded">
                                        <h5 class="mb-1">{{ number_format($invoice->remaining_amount, 0) }}</h5>
                                        <small>متبقي</small>
                                    </div>
                                </div>
                            </div>
                            
                            @if($invoice->total_amount > 0)
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ ($invoice->paid_amount / $invoice->total_amount) * 100 }}%">
                                </div>
                            </div>
                            <small class="text-muted">
                                نسبة الدفع: {{ number_format(($invoice->paid_amount / $invoice->total_amount) * 100, 1) }}%
                            </small>
                            @endif
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info me-2"></i>
                                معلومات إضافية
                            </h5>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">
                                <strong>تاريخ الإنشاء:</strong><br>
                                {{ $invoice->created_at->format('Y/m/d H:i') }}<br><br>
                                
                                <strong>آخر تحديث:</strong><br>
                                {{ $invoice->updated_at->format('Y/m/d H:i') }}<br><br>
                                
                                @if($invoice->due_date)
                                <strong>أيام حتى الاستحقاق:</strong><br>
                                @if($invoice->due_date->isFuture())
                                    {{ $invoice->due_date->diffInDays(now()) }} يوم
                                @else
                                    <span class="text-danger">متأخرة بـ {{ now()->diffInDays($invoice->due_date) }} يوم</span>
                                @endif
                                @endif
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة دفعة -->
@can('invoices.edit')
@if($invoice->remaining_amount > 0 && !in_array($invoice->status, ['cancelled']))
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('invoices.add-payment', $invoice) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                        <input type="date" name="payment_date" id="payment_date" 
                               class="form-control" value="{{ date('Y-m-d') }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="amount" class="form-label">المبلغ (ل.س) <span class="text-danger">*</span></label>
                        <input type="number" name="amount" id="amount" 
                               class="form-control" step="0.01" min="0.01" 
                               max="{{ $invoice->remaining_amount }}" 
                               value="{{ $invoice->remaining_amount }}" required>
                        <small class="text-muted">الحد الأقصى: {{ number_format($invoice->remaining_amount, 2) }} ل.س</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                        <select name="payment_method" id="payment_method" class="form-select" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash">نقداً</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">رقم المرجع</label>
                        <input type="text" name="reference_number" id="reference_number" 
                               class="form-control" placeholder="رقم الشيك، رقم التحويل، إلخ...">
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="payment_notes" rows="3" 
                                  class="form-control" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>
                        إضافة الدفعة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<!-- Modal تغيير الحالة -->
@if($invoice->status !== 'cancelled')
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير حالة الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('invoices.update-status', $invoice) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_status" class="form-label">الحالة الجديدة <span class="text-danger">*</span></label>
                        <select name="status" id="new_status" class="form-select" required>
                            <option value="">اختر الحالة</option>
                            @if($invoice->status === 'draft')
                            <option value="sent">مرسلة</option>
                            @endif
                            @if(in_array($invoice->status, ['draft', 'sent']))
                            <option value="paid">مدفوعة</option>
                            @endif
                            @if($invoice->status !== 'cancelled')
                            <option value="cancelled">ملغية</option>
                            @endif
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status_notes" class="form-label">ملاحظات التغيير</label>
                        <textarea name="notes" id="status_notes" rows="3" 
                                  class="form-control" placeholder="سبب تغيير الحالة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        تحديث الحالة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<!-- Modal تطبيق خصم -->
@if($invoice->status === 'draft')
<div class="modal fade" id="discountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تطبيق خصم على الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('invoices.apply-discount', $invoice) }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        المبلغ الحالي للفاتورة: <strong>{{ number_format($invoice->total_amount, 2) }} ل.س</strong>
                    </div>
                    
                    <div class="mb-3">
                        <label for="discount_amount" class="form-label">مبلغ الخصم (ل.س) <span class="text-danger">*</span></label>
                        <input type="number" name="discount_amount" id="discount_amount" 
                               class="form-control" step="0.01" min="0.01" 
                               max="{{ $invoice->total_amount }}" required>
                        <small class="text-muted">الحد الأقصى: {{ number_format($invoice->total_amount, 2) }} ل.س</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="discount_reason" class="form-label">سبب الخصم <span class="text-danger">*</span></label>
                        <textarea name="discount_reason" id="discount_reason" rows="3" 
                                  class="form-control" placeholder="اذكر سبب تطبيق الخصم..." required></textarea>
                    </div>
                    
                    <div id="new_total_preview" class="alert alert-success" style="display: none;">
                        <strong>المبلغ الجديد بعد الخصم: <span id="new_total_amount"></span> ل.س</strong>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-percentage me-1"></i>
                        تطبيق الخصم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<!-- Modal إضافة دفعة (مكرر للاستخدام من الأزرار المختلفة) -->
@if($invoice->remaining_amount > 0 && $invoice->status !== 'cancelled')
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('invoices.add-payment', $invoice) }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        المبلغ المتبقي: <strong>{{ number_format($invoice->remaining_amount, 2) }} ل.س</strong>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_date2" class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                        <input type="date" name="payment_date" id="payment_date2" 
                               class="form-control" value="{{ date('Y-m-d') }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="amount2" class="form-label">المبلغ (ل.س) <span class="text-danger">*</span></label>
                        <input type="number" name="amount" id="amount2" 
                               class="form-control" step="0.01" min="0.01" 
                               max="{{ $invoice->remaining_amount }}" 
                               value="{{ $invoice->remaining_amount }}" required>
                        <div class="form-text">
                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                    onclick="document.getElementById('amount2').value = {{ $invoice->remaining_amount }}">
                                دفع المبلغ كاملاً
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_method2" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                        <select name="payment_method" id="payment_method2" class="form-select" required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="cash">نقداً</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reference_number2" class="form-label">رقم المرجع</label>
                        <input type="text" name="reference_number" id="reference_number2" 
                               class="form-control" placeholder="رقم الشيك، رقم التحويل، إلخ...">
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_notes2" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="payment_notes2" rows="3" 
                                  class="form-control" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>
                        إضافة الدفعة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endcan

@push('scripts')
<script>
// حساب المبلغ الجديد بعد الخصم
document.getElementById('discount_amount')?.addEventListener('input', function() {
    const discountAmount = parseFloat(this.value) || 0;
    const currentTotal = {{ $invoice->total_amount }};
    const newTotal = currentTotal - discountAmount;
    
    if (discountAmount > 0 && discountAmount <= currentTotal) {
        document.getElementById('new_total_amount').textContent = new Intl.NumberFormat('ar-SY').format(newTotal.toFixed(2));
        document.getElementById('new_total_preview').style.display = 'block';
    } else {
        document.getElementById('new_total_preview').style.display = 'none';
    }
});

// إرسال الفاتورة بالبريد الإلكتروني
function sendInvoiceEmail() {
    if (confirm('هل تريد إرسال الفاتورة إلى {{ $invoice->agent->email }}؟')) {
        // هنا يمكن إضافة AJAX request لإرسال البريد الإلكتروني
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}

// تحديث طريقة الدفع المرجعية
document.getElementById('payment_method')?.addEventListener('change', function() {
    const referenceField = document.getElementById('reference_number');
    const referenceLabel = referenceField.previousElementSibling;
    
    switch(this.value) {
        case 'check':
            referenceLabel.textContent = 'رقم الشيك';
            referenceField.placeholder = 'أدخل رقم الشيك';
            break;
        case 'bank_transfer':
            referenceLabel.textContent = 'رقم التحويل';
            referenceField.placeholder = 'أدخل رقم التحويل البنكي';
            break;
        case 'credit_card':
            referenceLabel.textContent = 'رقم العملية';
            referenceField.placeholder = 'أدخل رقم عملية البطاقة';
            break;
        default:
            referenceLabel.textContent = 'رقم المرجع';
            referenceField.placeholder = 'رقم الشيك، رقم التحويل، إلخ...';
    }
});

// نفس الشيء للنافذة الثانية
document.getElementById('payment_method2')?.addEventListener('change', function() {
    const referenceField = document.getElementById('reference_number2');
    const referenceLabel = referenceField.previousElementSibling;
    
    switch(this.value) {
        case 'check':
            referenceLabel.textContent = 'رقم الشيك';
            referenceField.placeholder = 'أدخل رقم الشيك';
            break;
        case 'bank_transfer':
            referenceLabel.textContent = 'رقم التحويل';
            referenceField.placeholder = 'أدخل رقم التحويل البنكي';
            break;
        case 'credit_card':
            referenceLabel.textContent = 'رقم العملية';
            referenceField.placeholder = 'أدخل رقم عملية البطاقة';
            break;
        default:
            referenceLabel.textContent = 'رقم المرجع';
            referenceField.placeholder = 'رقم الشيك، رقم التحويل، إلخ...';
    }
});
</script>
@endpush
@endsection
