@extends('layouts.port-app')

@section('title', 'تقرير المستودعات والتخزين')

@section('content')
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="text-primary">
                    <i class="fas fa-building me-2"></i>
                    تقرير المستودعات والتخزين
                </h2>
                <div class="btn-group">
                    <a href="{{ route('reports.warehouses-storage', array_merge(request()->all(), ['format' => 'pdf'])) }}" 
                       class="btn btn-danger">
                        <i class="fas fa-file-pdf me-2"></i>
                        تحميل PDF
                    </a>
                    <a href="{{ route('reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
            <p class="text-muted">
                الفترة: {{ $date_from->format('Y/m/d') }} - {{ $date_to->format('Y/m/d') }}
                ({{ $date_from->diffInDays($date_to) + 1 }} يوم)
            </p>
        </div>
    </div>

    <!-- فلتر التاريخ -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar me-2"></i>
                اختيار الفترة الزمنية
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('reports.warehouses-storage') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" 
                               value="{{ request('date_from', $date_from->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" 
                               value="{{ request('date_to', $date_to->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search me-2"></i>
                            عرض التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_warehouses'] }}</h3>
                    <p class="mb-0">إجمالي المستودعات</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_capacity'], 0) }}</h3>
                    <p class="mb-0">السعة الإجمالية (طن)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_occupancy'], 0) }}</h3>
                    <p class="mb-0">الإشغال الحالي (طن)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['average_occupancy_rate'], 1) }}%</h3>
                    <p class="mb-0">متوسط الإشغال</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_revenue'], 0) }}</h3>
                    <p class="mb-0">إجمالي الإيرادات (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_available_space'], 0) }}</h3>
                    <p class="mb-0">المساحة المتاحة (طن)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل المستودعات -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                تفاصيل المستودعات
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>المستودع</th>
                            <th>النوع</th>
                            <th>السعة الإجمالية</th>
                            <th>الإشغال الحالي</th>
                            <th>معدل الإشغال</th>
                            <th>المساحة المتاحة</th>
                            <th>عدد عمليات التخزين</th>
                            <th>إجمالي الإيرادات</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($warehouse_stats as $stat)
                        <tr>
                            <td>
                                <strong>{{ $stat['warehouse']->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $stat['warehouse']->code }}</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $stat['warehouse']->warehouse_type }}</span>
                            </td>
                            <td>
                                <strong>{{ number_format($stat['total_capacity'], 0) }} طن</strong>
                            </td>
                            <td>
                                <span class="badge bg-warning">{{ number_format($stat['current_occupancy'], 0) }} طن</span>
                            </td>
                            <td>
                                <div class="progress" style="height: 25px;">
                                    <div class="progress-bar 
                                        @if($stat['occupancy_rate'] >= 90) bg-danger
                                        @elseif($stat['occupancy_rate'] >= 70) bg-warning
                                        @elseif($stat['occupancy_rate'] >= 50) bg-info
                                        @else bg-success
                                        @endif" 
                                        role="progressbar" 
                                        style="width: {{ $stat['occupancy_rate'] }}%">
                                        {{ $stat['occupancy_rate'] }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ number_format($stat['available_space'], 0) }} طن</span>
                            </td>
                            <td>
                                @if($stat['storage_count'] > 0)
                                    <span class="badge bg-primary">{{ $stat['storage_count'] }}</span>
                                @else
                                    <span class="text-muted">0</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($stat['total_revenue'], 0) }} ل.س</strong>
                            </td>
                            <td>
                                @if($stat['warehouse']->is_active)
                                    <span class="badge bg-success">نشط</span>
                                @else
                                    <span class="badge bg-danger">غير نشط</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="2">الإجمالي</th>
                            <th>{{ number_format($warehouse_stats->sum('total_capacity'), 0) }} طن</th>
                            <th>{{ number_format($warehouse_stats->sum('current_occupancy'), 0) }} طن</th>
                            <th>{{ number_format($warehouse_stats->avg('occupancy_rate'), 1) }}%</th>
                            <th>{{ number_format($warehouse_stats->sum('available_space'), 0) }} طن</th>
                            <th>{{ $warehouse_stats->sum('storage_count') }}</th>
                            <th>{{ number_format($warehouse_stats->sum('total_revenue'), 0) }} ل.س</th>
                            <th>-</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- تحليل الأداء -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        تصنيف المستودعات حسب الإشغال
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $full_warehouses = $warehouse_stats->where('occupancy_rate', '>=', 90)->count();
                        $high_occupancy = $warehouse_stats->where('occupancy_rate', '>=', 70)->where('occupancy_rate', '<', 90)->count();
                        $medium_occupancy = $warehouse_stats->where('occupancy_rate', '>=', 50)->where('occupancy_rate', '<', 70)->count();
                        $low_occupancy = $warehouse_stats->where('occupancy_rate', '<', 50)->count();
                    @endphp
                    
                    <div class="row text-center">
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 bg-danger text-white">
                                <h3>{{ $full_warehouses }}</h3>
                                <p class="mb-0">مكتملة الإشغال (≥90%)</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 bg-warning text-dark">
                                <h3>{{ $high_occupancy }}</h3>
                                <p class="mb-0">إشغال عالي (70-89%)</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3 bg-info text-white">
                                <h3>{{ $medium_occupancy }}</h3>
                                <p class="mb-0">إشغال متوسط (50-69%)</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3 bg-success text-white">
                                <h3>{{ $low_occupancy }}</h3>
                                <p class="mb-0">إشغال منخفض (<50%)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        أفضل 3 مستودعات من حيث الإيرادات
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($warehouse_stats->sortByDesc('total_revenue')->take(3) as $index => $stat)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge bg-{{ $index == 0 ? 'warning' : ($index == 1 ? 'secondary' : 'dark') }} me-2">
                                {{ $index + 1 }}
                            </span>
                            <strong>{{ $stat['warehouse']->name }}</strong>
                            <br>
                            <small class="text-muted">{{ $stat['storage_count'] }} عملية تخزين</small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success">{{ number_format($stat['total_revenue'], 0) }} ل.س</strong>
                            <br>
                            <small class="text-muted">{{ $stat['occupancy_rate'] }}% إشغال</small>
                        </div>
                    </div>
                    @if(!$loop->last)
                        <hr>
                    @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- تحذيرات وتنبيهات -->
    @if($warehouse_stats->where('occupancy_rate', '>=', 90)->count() > 0)
    <div class="alert alert-warning mt-4">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>تنبيه: مستودعات مكتملة الإشغال</h5>
        <p class="mb-0">
            يوجد {{ $warehouse_stats->where('occupancy_rate', '>=', 90)->count() }} مستودع/مستودعات بإشغال 90% أو أكثر. 
            يُنصح بمراجعة هذه المستودعات لتجنب نقص المساحة.
        </p>
    </div>
    @endif

    @if($warehouse_stats->where('occupancy_rate', '<', 30)->count() > 0)
    <div class="alert alert-info mt-4">
        <h5><i class="fas fa-info-circle me-2"></i>معلومة: مستودعات بإشغال منخفض</h5>
        <p class="mb-0">
            يوجد {{ $warehouse_stats->where('occupancy_rate', '<', 30)->count() }} مستودع/مستودعات بإشغال أقل من 30%. 
            يمكن الاستفادة من هذه المساحات لتحسين الكفاءة.
        </p>
    </div>
    @endif
</div>

@push('scripts')
<script>
// يمكن إضافة مخططات بيانية هنا
</script>
@endpush
@endsection