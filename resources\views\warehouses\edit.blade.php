@extends('layouts.port-app')

@section('page-title', 'تعديل المستودع')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('warehouses.show', $warehouse) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى التفاصيل
    </a>
    <a href="{{ route('warehouses.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-list me-2"></i>
        القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل المستودع: {{ $warehouse->name }}
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('warehouses.update', $warehouse) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم المستودع <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name', $warehouse->name) }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="code" class="form-label">رمز المستودع <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                               id="code" name="code" value="{{ old('code', $warehouse->code) }}" required>
                                        <div class="form-text">رمز فريد للمستودع (مثل: WH001)</div>
                                        @error('code')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="type" class="form-label">نوع المستودع <span class="text-danger">*</span></label>
                                        <select class="form-select @error('type') is-invalid @enderror" 
                                                id="type" name="type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="warehouse" {{ old('type', $warehouse->type) == 'warehouse' ? 'selected' : '' }}>مستودع</option>
                                            <option value="yard" {{ old('type', $warehouse->type) == 'yard' ? 'selected' : '' }}>ساحة</option>
                                            <option value="cold_storage" {{ old('type', $warehouse->type) == 'cold_storage' ? 'selected' : '' }}>تبريد</option>
                                            <option value="tank" {{ old('type', $warehouse->type) == 'tank' ? 'selected' : '' }}>خزان</option>
                                            <option value="silo" {{ old('type', $warehouse->type) == 'silo' ? 'selected' : '' }}>صومعة</option>
                                        </select>
                                        @error('type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" 
                                                id="status" name="status" required>
                                            <option value="">اختر الحالة</option>
                                            <option value="active" {{ old('status', $warehouse->status) == 'active' ? 'selected' : '' }}>نشط</option>
                                            <option value="inactive" {{ old('status', $warehouse->status) == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                            <option value="maintenance" {{ old('status', $warehouse->status) == 'maintenance' ? 'selected' : '' }}>قيد الصيانة</option>
                                            <option value="full" {{ old('status', $warehouse->status) == 'full' ? 'selected' : '' }}>ممتلئ</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="location" class="form-label">الموقع</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                       id="location" name="location" value="{{ old('location', $warehouse->location) }}">
                                <div class="form-text">موقع المستودع داخل المرفأ</div>
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المساحة والسعة -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-expand-arrows-alt me-2"></i>
                                المساحة والسعة
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="total_area" class="form-label">المساحة الإجمالية (م²) <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control @error('total_area') is-invalid @enderror" 
                                               id="total_area" name="total_area" value="{{ old('total_area', $warehouse->total_area) }}" required>
                                        @error('total_area')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="available_area" class="form-label">المساحة المتاحة (م²) <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control @error('available_area') is-invalid @enderror" 
                                               id="available_area" name="available_area" value="{{ old('available_area', $warehouse->available_area) }}" required>
                                        @error('available_area')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="max_capacity" class="form-label">السعة القصوى (طن)</label>
                                        <input type="number" step="0.01" class="form-control @error('max_capacity') is-invalid @enderror" 
                                               id="max_capacity" name="max_capacity" value="{{ old('max_capacity', $warehouse->max_capacity) }}">
                                        <div class="form-text">اختياري - السعة القصوى بالطن</div>
                                        @error('max_capacity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- عرض السعة الحالية للمعلومات فقط -->
                            @if($warehouse->current_capacity > 0)
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>السعة الحالية:</strong> {{ number_format($warehouse->current_capacity, 2) }} طن
                                <br>
                                <small>هذه القيمة تُحدث تلقائياً بناءً على الحمولات المخزنة</small>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الخصائص والتعريفة -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cogs me-2"></i>
                                الخصائص والتعريفة
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <input type="hidden" name="is_covered" value="0">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_covered" name="is_covered" value="1" 
                                                   {{ old('is_covered', $warehouse->is_covered) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_covered">
                                                مستودع مغطى
                                            </label>
                                        </div>
                                        <div class="form-text">حدد إذا كان المستودع مغطى أم مكشوف</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="daily_rate" class="form-label">التعريفة اليومية ($)</label>
                                        <input type="number" step="0.01" class="form-control @error('daily_rate') is-invalid @enderror" 
                                               id="daily_rate" name="daily_rate" value="{{ old('daily_rate', $warehouse->daily_rate) }}">
                                        <div class="form-text">التعريفة اليومية للتخزين بالدولار</div>
                                        @error('daily_rate')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- معلومات المدير -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user-tie me-2"></i>
                                معلومات المدير
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="manager_name" class="form-label">اسم المدير</label>
                                        <input type="text" class="form-control @error('manager_name') is-invalid @enderror" 
                                               id="manager_name" name="manager_name" value="{{ old('manager_name', $warehouse->manager_name) }}">
                                        @error('manager_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="manager_phone" class="form-label">هاتف المدير</label>
                                        <input type="text" class="form-control @error('manager_phone') is-invalid @enderror" 
                                               id="manager_phone" name="manager_phone" value="{{ old('manager_phone', $warehouse->manager_phone) }}">
                                        @error('manager_phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الوصف -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-sticky-note me-2"></i>
                                وصف إضافي
                            </h6>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4">{{ old('description', $warehouse->description) }}</textarea>
                                <div class="form-text">وصف تفصيلي للمستودع وخصائصه</div>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('warehouses.show', $warehouse) }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// تحديث المساحة المتاحة تلقائياً عند تغيير المساحة الإجمالية
document.getElementById('total_area').addEventListener('input', function() {
    const totalArea = parseFloat(this.value) || 0;
    const availableAreaField = document.getElementById('available_area');
    const currentAvailable = parseFloat(availableAreaField.value) || 0;
    
    if (currentAvailable > totalArea) {
        availableAreaField.value = totalArea;
        alert('المساحة المتاحة لا يمكن أن تتجاوز المساحة الإجمالية');
    }
});

// التحقق من أن المساحة المتاحة لا تتجاوز المساحة الإجمالية
document.getElementById('available_area').addEventListener('input', function() {
    const totalArea = parseFloat(document.getElementById('total_area').value) || 0;
    const availableArea = parseFloat(this.value) || 0;
    
    if (availableArea > totalArea) {
        this.value = totalArea;
        alert('المساحة المتاحة لا يمكن أن تتجاوز المساحة الإجمالية');
    }
});

// تحديث الحالة تلقائياً بناءً على السعة
document.getElementById('max_capacity').addEventListener('input', function() {
    const maxCapacity = parseFloat(this.value) || 0;
    const currentCapacity = {{ $warehouse->current_capacity ?? 0 }};
    const statusField = document.getElementById('status');
    
    if (maxCapacity > 0 && currentCapacity >= maxCapacity && statusField.value !== 'maintenance') {
        statusField.value = 'full';
    } else if (statusField.value === 'full' && currentCapacity < maxCapacity) {
        statusField.value = 'active';
    }
});
</script>
@endpush
