<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Warehouse extends Model
{
    protected $fillable = [
        'name',
        'code',
        'type',
        'total_area',
        'available_area',
        'max_capacity',
        'current_capacity',
        'is_covered',
        'daily_rate',
        'status',
        'description',
        'location',
        'manager_name',
        'manager_phone'
    ];

    protected $casts = [
        'total_area' => 'decimal:2',
        'available_area' => 'decimal:2',
        'max_capacity' => 'decimal:2',
        'current_capacity' => 'decimal:2',
        'daily_rate' => 'decimal:2',
        'is_covered' => 'boolean'
    ];

    /**
     * العلاقة مع سجلات التخزين
     */
    public function cargoStorages(): HasMany
    {
        return $this->hasMany(CargoStorage::class);
    }

    /**
     * الحصول على الحمولات المخزنة حالياً
     */
    public function currentCargos(): HasMany
    {
        return $this->cargoStorages()->whereNull('storage_end');
    }

    /**
     * الحصول على نوع المستودع بالعربية
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            'warehouse' => 'مستودع',
            'yard' => 'ساحة',
            'cold_storage' => 'تبريد',
            'tank' => 'خزان',
            'silo' => 'صومعة',
            default => 'أخرى'
        };
    }

    /**
     * الحصول على الحالة بالعربية
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'maintenance' => 'قيد الصيانة',
            'full' => 'ممتلئ',
            default => 'غير محدد'
        };
    }

    /**
     * حساب نسبة الاستغلال
     */
    public function getOccupancyPercentageAttribute(): float
    {
        if ($this->max_capacity <= 0) {
            return 0;
        }

        return min(100, ($this->current_capacity / $this->max_capacity) * 100);
    }

    /**
     * حساب المساحة المستغلة
     */
    public function getUsedAreaAttribute(): float
    {
        return $this->total_area - $this->available_area;
    }

    /**
     * حساب نسبة استغلال المساحة
     */
    public function getAreaOccupancyPercentageAttribute(): float
    {
        if ($this->total_area <= 0) {
            return 0;
        }

        return min(100, ($this->used_area / $this->total_area) * 100);
    }

    /**
     * فلترة المستودعات النشطة
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * فلترة المستودعات المتاحة
     */
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('status', 'active')
                    ->where('available_area', '>', 0)
                    ->whereColumn('current_capacity', '<', 'max_capacity');
    }

    /**
     * فلترة حسب النوع
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

}
