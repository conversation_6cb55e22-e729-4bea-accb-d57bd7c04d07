@extends('layouts.port-app')

@section('page-title', 'الملف الشخصي')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('dashboard') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى لوحة التحكم
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <!-- معلومات المستخدم الأساسية -->
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body text-center">
                <div class="profile-avatar mb-3">
                    <div class="avatar-circle">
                        <i class="fas fa-user fa-3x text-primary"></i>
                    </div>
                </div>
                <h4 class="card-title">{{ Auth::user()->name }}</h4>
                <p class="text-muted">{{ Auth::user()->email }}</p>

                @if(Auth::user()->roles->isNotEmpty())
                    <div class="mb-3">
                        @foreach(Auth::user()->roles as $role)
                            <span class="badge bg-primary me-1">{{ $role->display_name }}</span>
                        @endforeach
                    </div>
                @endif

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary">{{ Auth::user()->created_at->format('Y') }}</h5>
                            <small class="text-muted">سنة الانضمام</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success">{{ Auth::user()->created_at->diffInDays() }}</h5>
                        <small class="text-muted">يوم في النظام</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات النشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>آخر تسجيل دخول</span>
                    <span class="text-muted">{{ Auth::user()->updated_at->diffForHumans() }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>تاريخ الانضمام</span>
                    <span class="text-muted">{{ Auth::user()->created_at->format('Y/m/d') }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>حالة الحساب</span>
                    <span class="badge bg-success">نشط</span>
                </div>
            </div>
        </div>
    </div>

    <!-- تحديث المعلومات -->
    <div class="col-md-8">
        <!-- تحديث المعلومات الشخصية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    تحديث المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('profile.update') }}">
                    @csrf
                    @method('patch')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name', Auth::user()->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror"
                                       id="email" name="email" value="{{ old('email', Auth::user()->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    @if (Auth::user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! Auth::user()->hasVerifiedEmail())
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            عنوان بريدك الإلكتروني غير مؤكد.
                            <button form="send-verification" class="btn btn-link p-0 text-decoration-underline">
                                انقر هنا لإعادة إرسال رسالة التأكيد.
                            </button>
                        </div>

                        @if (session('status') === 'verification-link-sent')
                            <div class="alert alert-success">
                                تم إرسال رابط تأكيد جديد إلى عنوان بريدك الإلكتروني.
                            </div>
                        @endif
                    @endif

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>

                    @if (session('status') === 'profile-updated')
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-2"></i>
                            تم تحديث المعلومات بنجاح.
                        </div>
                    @endif
                </form>
            </div>
        </div>

        <!-- تغيير كلمة المرور -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lock me-2"></i>
                    تغيير كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('password.update') }}">
                    @csrf
                    @method('put')

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                                <input type="password" class="form-control @error('current_password') is-invalid @enderror"
                                       id="current_password" name="current_password" required>
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror"
                                       id="password" name="password" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control @error('password_confirmation') is-invalid @enderror"
                                       id="password_confirmation" name="password_confirmation" required>
                                @error('password_confirmation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>
                            تحديث كلمة المرور
                        </button>
                    </div>

                    @if (session('status') === 'password-updated')
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-2"></i>
                            تم تحديث كلمة المرور بنجاح.
                        </div>
                    @endif
                </form>
            </div>
        </div>

        <!-- حذف الحساب -->
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    منطقة خطر - حذف الحساب
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    بمجرد حذف حسابك، سيتم حذف جميع موارده وبياناته نهائياً. قبل حذف حسابك،
                    يرجى تنزيل أي بيانات أو معلومات ترغب في الاحتفاظ بها.
                </p>

                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                    <i class="fas fa-trash me-2"></i>
                    حذف الحساب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد حذف الحساب -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد حذف الحساب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هل أنت متأكد من رغبتك في حذف حسابك؟
                </p>
                <p class="text-muted">
                    بمجرد حذف حسابك، سيتم حذف جميع موارده وبياناته نهائياً.
                    يرجى إدخال كلمة المرور لتأكيد رغبتك في حذف حسابك نهائياً.
                </p>

                <form method="POST" action="{{ route('profile.destroy') }}" id="deleteAccountForm">
                    @csrf
                    @method('delete')

                    <div class="mb-3">
                        <label for="delete_password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="delete_password" name="password"
                               placeholder="أدخل كلمة المرور للتأكيد" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>
                    حذف الحساب نهائياً
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Form إعادة إرسال التأكيد -->
@if (Auth::user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! Auth::user()->hasVerifiedEmail())
    <form id="send-verification" method="POST" action="{{ route('verification.send') }}" style="display: none;">
        @csrf
    </form>
@endif
@endsection

@push('styles')
<style>
.avatar-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.avatar-circle i {
    color: white;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
</style>
@endpush
