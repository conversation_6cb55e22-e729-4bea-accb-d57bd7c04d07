@extends('layouts.port-app')

@section('page-title', 'إضافة مستخدم جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h2>
                <a href="{{ route('users.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة لقائمة المستخدمين
                </a>
            </div>

            <form action="{{ route('users.store') }}" method="POST" enctype="multipart/form-data" id="userForm">
                @csrf
                <div class="row">
                    <!-- المعلومات الأساسية -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الأساسية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" name="name" id="name" 
                                                   class="form-control @error('name') is-invalid @enderror" 
                                                   value="{{ old('name') }}" required>
                                            @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">اسم المستخدم</label>
                                            <input type="text" name="username" id="username" 
                                                   class="form-control @error('username') is-invalid @enderror" 
                                                   value="{{ old('username') }}">
                                            <small class="form-text text-muted">اختياري - سيتم إنشاؤه تلقائياً إذا لم يتم تحديده</small>
                                            @error('username')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" name="email" id="email" 
                                                   class="form-control @error('email') is-invalid @enderror" 
                                                   value="{{ old('email') }}" required>
                                            @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" name="phone" id="phone" 
                                                   class="form-control @error('phone') is-invalid @enderror" 
                                                   value="{{ old('phone') }}">
                                            @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" name="password" id="password" 
                                                       class="form-control @error('password') is-invalid @enderror" required>
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('password')">
                                                    <i class="fas fa-eye" id="password-eye"></i>
                                                </button>
                                            </div>
                                            <small class="form-text text-muted">يجب أن تحتوي على 8 أحرف على الأقل</small>
                                            @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_confirmation" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" name="password_confirmation" id="password_confirmation" 
                                                       class="form-control" required>
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('password_confirmation')">
                                                    <i class="fas fa-eye" id="password_confirmation-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="bio" class="form-label">نبذة شخصية</label>
                                    <textarea name="bio" id="bio" rows="3" 
                                              class="form-control @error('bio') is-invalid @enderror" 
                                              placeholder="نبذة مختصرة عن المستخدم...">{{ old('bio') }}</textarea>
                                    @error('bio')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- الأدوار والصلاحيات -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    الأدوار والصلاحيات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">الأدوار <span class="text-danger">*</span></label>
                                    <div class="row">
                                        @foreach($roles ?? [] as $role)
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" name="roles[]" value="{{ $role->id }}" 
                                                       id="role_{{ $role->id }}" class="form-check-input"
                                                       {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}>
                                                <label for="role_{{ $role->id }}" class="form-check-label">
                                                    <strong>{{ $role->display_name ?? $role->name }}</strong>
                                                    @if($role->description)
                                                    <br><small class="text-muted">{{ $role->description }}</small>
                                                    @endif
                                                </label>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                    @error('roles')
                                    <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> يمكن تعديل الأدوار والصلاحيات لاحقاً من صفحة تعديل المستخدم.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الشريط الجانبي -->
                    <div class="col-md-4">
                        <!-- صورة المستخدم -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-image me-2"></i>
                                    صورة المستخدم
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <div class="avatar-lg mx-auto mb-3">
                                        <img id="avatar-preview" src="{{ asset('images/default-avatar.svg') }}" 
                                             class="avatar-img rounded-circle" alt="صورة المستخدم" style="width: 100px; height: 100px;">
                                    </div>
                                    <input type="file" name="avatar" id="avatar" class="form-control" 
                                           accept="image/*" onchange="previewAvatar(this)">
                                    <small class="form-text text-muted">
                                        الحد الأقصى: 2MB<br>
                                        الأنواع المدعومة: JPG, PNG, GIF
                                    </small>
                                    @error('avatar')
                                    <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الحساب -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    إعدادات الحساب
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input type="checkbox" name="is_active" id="is_active" 
                                               class="form-check-input" value="1" 
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label for="is_active" class="form-check-label">
                                            <strong>حساب نشط</strong>
                                            <br><small class="text-muted">يمكن للمستخدم تسجيل الدخول</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input type="checkbox" name="email_verified" id="email_verified" 
                                               class="form-check-input" value="1" 
                                               {{ old('email_verified', false) ? 'checked' : '' }}>
                                        <label for="email_verified" class="form-check-label">
                                            <strong>بريد إلكتروني مؤكد</strong>
                                            <br><small class="text-muted">تأكيد البريد الإلكتروني مسبقاً</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input type="checkbox" name="send_welcome_email" id="send_welcome_email" 
                                               class="form-check-input" value="1" 
                                               {{ old('send_welcome_email', true) ? 'checked' : '' }}>
                                        <label for="send_welcome_email" class="form-check-label">
                                            <strong>إرسال بريد ترحيبي</strong>
                                            <br><small class="text-muted">إرسال رسالة ترحيب للمستخدم</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input type="checkbox" name="force_password_change" id="force_password_change" 
                                               class="form-check-input" value="1" 
                                               {{ old('force_password_change', false) ? 'checked' : '' }}>
                                        <label for="force_password_change" class="form-check-label">
                                            <strong>إجبار تغيير كلمة المرور</strong>
                                            <br><small class="text-muted">المستخدم مطالب بتغيير كلمة المرور في أول تسجيل دخول</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات إضافية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="department" class="form-label">القسم</label>
                                    <input type="text" name="department" id="department" 
                                           class="form-control @error('department') is-invalid @enderror" 
                                           value="{{ old('department') }}" placeholder="مثل: الإدارة، المحاسبة، العمليات">
                                    @error('department')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="position" class="form-label">المنصب</label>
                                    <input type="text" name="position" id="position" 
                                           class="form-control @error('position') is-invalid @enderror" 
                                           value="{{ old('position') }}" placeholder="مثل: مدير، محاسب، موظف">
                                    @error('position')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="hire_date" class="form-label">تاريخ التوظيف</label>
                                    <input type="date" name="hire_date" id="hire_date" 
                                           class="form-control @error('hire_date') is-invalid @enderror" 
                                           value="{{ old('hire_date') }}">
                                    @error('hire_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المستخدم
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="saveAndAddAnother()">
                                        <i class="fas fa-plus me-2"></i>
                                        حفظ وإضافة آخر
                                    </button>
                                    <a href="{{ route('users.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// معاينة الصورة الشخصية
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatar-preview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// إنشاء اسم مستخدم تلقائياً
document.getElementById('name').addEventListener('input', function() {
    const usernameField = document.getElementById('username');
    if (!usernameField.value) {
        const name = this.value.toLowerCase()
            .replace(/\s+/g, '_')
            .replace(/[^a-z0-9_]/g, '');
        usernameField.value = name;
    }
});

// التحقق من قوة كلمة المرور
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    
    // إزالة الفئات السابقة
    this.classList.remove('is-valid', 'is-invalid');
    
    if (password.length > 0) {
        if (strength >= 3) {
            this.classList.add('is-valid');
        } else {
            this.classList.add('is-invalid');
        }
    }
});

function calculatePasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
}

// التحقق من تطابق كلمة المرور
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmation = this.value;
    
    this.classList.remove('is-valid', 'is-invalid');
    
    if (confirmation.length > 0) {
        if (password === confirmation) {
            this.classList.add('is-valid');
        } else {
            this.classList.add('is-invalid');
        }
    }
});

// حفظ وإضافة آخر
function saveAndAddAnother() {
    const form = document.getElementById('userForm');
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'save_and_add_another';
    input.value = '1';
    form.appendChild(input);
    form.submit();
}

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('userForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmation = document.getElementById('password_confirmation').value;
    const roles = document.querySelectorAll('input[name="roles[]"]:checked');
    
    if (password !== confirmation) {
        e.preventDefault();
        alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
        return;
    }
    
    if (roles.length === 0) {
        e.preventDefault();
        alert('يرجى اختيار دور واحد على الأقل للمستخدم');
        return;
    }
    
    if (calculatePasswordStrength(password) < 3) {
        e.preventDefault();
        alert('كلمة المرور ضعيفة. يرجى استخدام كلمة مرور أقوى تحتوي على أحرف كبيرة وصغيرة وأرقام');
        return;
    }
});
</script>
@endpush