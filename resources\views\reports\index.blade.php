@extends('layouts.port-app')

@section('page-title', 'التقارير')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير المتاحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- تقرير حركة السفن اليومي -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-ship me-2"></i>
                                    تقرير حركة السفن اليومي
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    تقرير شامل لحركة السفن اليومية مشابه لتقرير مرفأ اللاذقية، يتضمن:
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>إعلانات الوصول</li>
                                    <li><i class="fas fa-check text-success me-2"></i>السفن خارج الحوض</li>
                                    <li><i class="fas fa-check text-success me-2"></i>السفن على الأرصفة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>السفن المغادرة</li>
                                </ul>

                                <form action="{{ route('reports.daily-ship-movement') }}" method="GET" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="date" name="date" class="form-control" value="{{ date('Y-m-d') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-eye me-2"></i>عرض
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <div class="d-grid gap-2">
                                    <a href="{{ route('reports.daily-ship-movement', ['format' => 'pdf']) }}" class="btn btn-outline-danger">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تحميل PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التقرير المالي الشهري -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-dollar-sign me-2"></i>
                                    التقرير المالي الشهري
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    تقرير مالي شامل للإيرادات والمصروفات الشهرية، يتضمن:
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>إيرادات رسوم الرسو</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إيرادات التخزين</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إيرادات مناولة البضائع</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الفواتير المعلقة</li>
                                </ul>

                                <form action="{{ route('reports.monthly-financial') }}" method="GET" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="month" name="month" class="form-control" value="{{ date('Y-m') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <button type="submit" class="btn btn-success w-100">
                                                <i class="fas fa-eye me-2"></i>عرض
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <div class="d-grid gap-2">
                                    <a href="#" class="btn btn-outline-success">
                                        <i class="fas fa-file-excel me-2"></i>
                                        تحميل Excel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الأرصفة -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-warehouse me-2"></i>
                                    تقرير استخدام الأرصفة
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    تقرير تفصيلي لاستخدام الأرصفة وكفاءة التشغيل
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>معدل الإشغال</li>
                                    <li><i class="fas fa-check text-success me-2"></i>متوسط مدة الرسو</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الإيرادات لكل رصيف</li>
                                    <li><i class="fas fa-check text-success me-2"></i>عدد السفن المرسية</li>
                                </ul>

                                <form action="{{ route('reports.berths-utilization') }}" method="GET" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="date" name="date_from" class="form-control form-control-sm" 
                                                   value="{{ date('Y-m-d', strtotime('-30 days')) }}" placeholder="من تاريخ">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="date" name="date_to" class="form-control form-control-sm" 
                                                   value="{{ date('Y-m-d') }}" placeholder="إلى تاريخ">
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <button type="submit" class="btn btn-info w-100">
                                                <i class="fas fa-eye me-2"></i>عرض التقرير
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <div class="d-grid gap-2">
                                    <a href="{{ route('reports.berths-utilization', ['format' => 'pdf']) }}" class="btn btn-outline-info">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تحميل PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير المستودعات -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-building me-2"></i>
                                    تقرير المستودعات والتخزين
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    تقرير شامل لاستخدام المستودعات ورسوم التخزين
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>معدل الإشغال</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إيرادات التخزين</li>
                                    <li><i class="fas fa-check text-success me-2"></i>البضائع المخزنة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>المساحة المتاحة</li>
                                </ul>

                                <form action="{{ route('reports.warehouses-storage') }}" method="GET" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="date" name="date_from" class="form-control form-control-sm" 
                                                   value="{{ date('Y-m-d', strtotime('-30 days')) }}" placeholder="من تاريخ">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="date" name="date_to" class="form-control form-control-sm" 
                                                   value="{{ date('Y-m-d') }}" placeholder="إلى تاريخ">
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <button type="submit" class="btn btn-warning w-100">
                                                <i class="fas fa-eye me-2"></i>عرض التقرير
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <div class="d-grid gap-2">
                                    <a href="{{ route('reports.warehouses-storage', ['format' => 'pdf']) }}" class="btn btn-outline-warning">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تحميل PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير أداء الوكلاء -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-secondary">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    تقرير أداء الوكلاء الملاحيين
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    تقرير شامل لأداء الوكلاء الملاحيين والإيرادات
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>عدد السفن لكل وكيل</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إجمالي الفواتير</li>
                                    <li><i class="fas fa-check text-success me-2"></i>معدل السداد</li>
                                    <li><i class="fas fa-check text-success me-2"></i>المبالغ المعلقة</li>
                                </ul>

                                <form action="{{ route('reports.agents-performance') }}" method="GET" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="date" name="date_from" class="form-control form-control-sm" 
                                                   value="{{ date('Y-m-d', strtotime('-30 days')) }}" placeholder="من تاريخ">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="date" name="date_to" class="form-control form-control-sm" 
                                                   value="{{ date('Y-m-d') }}" placeholder="إلى تاريخ">
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <button type="submit" class="btn btn-secondary w-100">
                                                <i class="fas fa-eye me-2"></i>عرض التقرير
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <div class="d-grid gap-2">
                                    <a href="{{ route('reports.agents-performance', ['format' => 'pdf']) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تحميل PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تقرير الفواتير المتقدم -->
                    <div class="col-md-6 mb-4">
                        <div class="card border-dark">
                            <div class="card-header bg-dark text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-file-invoice-dollar me-2"></i>
                                    تقرير الفواتير المتقدم
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    تقرير تفصيلي للفواتير مع إحصائيات متقدمة
                                </p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>تحليل الفواتير حسب الحالة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الفواتير المتأخرة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>تحليل الإيرادات</li>
                                    <li><i class="fas fa-check text-success me-2"></i>مقارنات شهرية</li>
                                </ul>

                                <div class="d-grid gap-2">
                                    <a href="{{ route('invoices.report') }}" class="btn btn-dark">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-primary">{{ \App\Models\Ship::count() }}</h3>
                            <p class="text-muted mb-0">إجمالي السفن</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-success">{{ \App\Models\Ship::where('status', 'berthed')->count() }}</h3>
                            <p class="text-muted mb-0">السفن المرسية</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-warning">{{ \App\Models\Agent::where('is_active', true)->count() }}</h3>
                            <p class="text-muted mb-0">الوكلاء النشطون</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h3 class="text-info">{{ \App\Models\Invoice::where('status', 'paid')->whereMonth('created_at', date('m'))->sum('total_amount') }}</h3>
                            <p class="text-muted mb-0">إيرادات الشهر</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
