@extends('layouts.port-app')

@section('page-title', 'تفاصيل الحمولة')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('cargos.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
    @can('cargos.edit')
    <a href="{{ route('cargos.edit', $cargo) }}" class="btn btn-primary">
        <i class="fas fa-edit me-2"></i>
        تعديل
    </a>
    @endcan
    @can('cargos.delete')
    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
        <i class="fas fa-trash me-2"></i>
        حذف
    </button>
    @endcan
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8">
        <!-- معلومات الحمولة الأساسية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحمولة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">نوع الحمولة:</td>
                                <td>
                                    <span class="badge bg-info fs-6">{{ $cargo->cargo_type_label }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الوصف:</td>
                                <td>{{ $cargo->description }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الكمية:</td>
                                <td>
                                    <strong>{{ number_format($cargo->quantity, 2) }}</strong>
                                    {{ $cargo->unit_label }}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الوزن:</td>
                                <td>
                                    @if($cargo->weight)
                                        {{ number_format($cargo->weight, 2) }} طن
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الحجم:</td>
                                <td>
                                    @if($cargo->volume)
                                        {{ number_format($cargo->volume, 2) }} متر مكعب
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">القيمة:</td>
                                <td>
                                    @if($cargo->value)
                                        {{ number_format($cargo->value, 2) }} {{ $cargo->currency ?? 'USD' }}
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">بلد المنشأ:</td>
                                <td>{{ $cargo->origin_country }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">بلد الوجهة:</td>
                                <td>{{ $cargo->destination_country }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">تاريخ التحميل:</td>
                                <td>
                                    @if($cargo->loading_date)
                                        {{ $cargo->loading_date->format('Y/m/d H:i') }}
                                        <br>
                                        <small class="text-muted">{{ $cargo->loading_date->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">تاريخ التفريغ:</td>
                                <td>
                                    @if($cargo->unloading_date)
                                        {{ $cargo->unloading_date->format('Y/m/d H:i') }}
                                        <br>
                                        <small class="text-muted">{{ $cargo->unloading_date->diffForHumans() }}</small>
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($cargo->notes)
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-primary">ملاحظات:</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $cargo->notes }}
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- معلومات السفينة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ship me-2"></i>
                    معلومات السفينة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">اسم السفينة:</td>
                                <td>
                                    <a href="{{ route('ships.show', $cargo->ship) }}" class="text-decoration-none">
                                        {{ $cargo->ship->name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">رقم IMO:</td>
                                <td>{{ $cargo->ship->imo_number }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">العلم:</td>
                                <td>{{ $cargo->ship->flag }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold text-muted">نوع السفينة:</td>
                                <td>{{ $cargo->ship->ship_type }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">الوكيل الملاحي:</td>
                                <td>
                                    @if($cargo->ship->agent)
                                        <a href="{{ route('agents.show', $cargo->ship->agent) }}" class="text-decoration-none">
                                            {{ $cargo->ship->agent->name }}
                                        </a>
                                    @else
                                        <span class="text-muted">بدون وكيل</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold text-muted">حالة السفينة:</td>
                                <td>
                                    @switch($cargo->ship->status)
                                        @case('announced')
                                            <span class="badge bg-info">معلنة</span>
                                            @break
                                        @case('outside_port')
                                            <span class="badge bg-warning">خارج المرفأ</span>
                                            @break
                                        @case('berthed')
                                            <span class="badge bg-success">مرسية</span>
                                            @break
                                        @case('departed')
                                            <span class="badge bg-secondary">غادرت</span>
                                            @break
                                        @default
                                            <span class="badge bg-light text-dark">غير محدد</span>
                                    @endswitch
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجلات التخزين -->
        @if($cargo->storages->count() > 0)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    سجلات التخزين
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المستودع</th>
                                <th>الكمية</th>
                                <th>تاريخ الدخول</th>
                                <th>تاريخ الخروج</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cargo->storages as $storage)
                            <tr>
                                <td>
                                    <a href="#" class="text-decoration-none">
                                        {{ $storage->warehouse->name }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ $storage->warehouse->code }}</small>
                                </td>
                                <td>
                                    {{ number_format($storage->stored_quantity, 2) }}
                                    {{ $cargo->unit_label }}
                                </td>
                                <td>
                                    @if($storage->storage_start)
                                        {{ $storage->storage_start->format('Y/m/d H:i') }}
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                                <td>
                                    @if($storage->storage_end)
                                        {{ $storage->storage_end->format('Y/m/d H:i') }}
                                    @else
                                        <span class="text-muted">لا يزال مخزن</span>
                                    @endif
                                </td>
                                <td>
                                    @if($storage->storage_end)
                                        <span class="badge bg-secondary">خرج</span>
                                    @else
                                        <span class="badge bg-success">مخزن</span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-md-4">
        <!-- حالة الحمولة -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    حالة الحمولة
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    @switch($cargo->status)
                        @case('loading')
                            <span class="badge bg-warning fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @case('loaded')
                            <span class="badge bg-primary fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @case('in_transit')
                            <span class="badge bg-info fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @case('unloading')
                            <span class="badge bg-warning fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @case('unloaded')
                            <span class="badge bg-secondary fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @case('stored')
                            <span class="badge bg-dark fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @case('delivered')
                            <span class="badge bg-success fs-5">{{ $cargo->status_label }}</span>
                            @break
                        @default
                            <span class="badge bg-light text-dark fs-5">{{ $cargo->status_label }}</span>
                    @endswitch
                </div>

                @can('cargos.edit')
                <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#statusModal">
                    <i class="fas fa-edit me-1"></i>
                    تحديث الحالة
                </button>
                @endcan
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-1">{{ number_format($cargo->quantity, 0) }}</h5>
                            <small class="text-muted">الكمية</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1">
                            @if($cargo->value)
                                ${{ number_format($cargo->value, 0) }}
                            @else
                                --
                            @endif
                        </h5>
                        <small class="text-muted">القيمة</small>
                    </div>
                </div>

                @if($cargo->weight)
                <hr>
                <div class="text-center">
                    <h6 class="text-info mb-1">{{ number_format($cargo->weight, 2) }} طن</h6>
                    <small class="text-muted">الوزن الإجمالي</small>
                </div>
                @endif
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted d-block mb-2">
                    <i class="fas fa-plus me-1"></i>
                    تم الإنشاء: {{ $cargo->created_at->format('Y/m/d H:i') }}
                </small>
                <small class="text-muted d-block">
                    <i class="fas fa-edit me-1"></i>
                    آخر تحديث: {{ $cargo->updated_at->format('Y/m/d H:i') }}
                </small>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تحديث الحالة -->
@can('cargos.edit')
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الحمولة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('cargos.update-status', $cargo) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة الجديدة</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="loading" {{ $cargo->status == 'loading' ? 'selected' : '' }}>قيد التحميل</option>
                            <option value="loaded" {{ $cargo->status == 'loaded' ? 'selected' : '' }}>محمل</option>
                            <option value="in_transit" {{ $cargo->status == 'in_transit' ? 'selected' : '' }}>في الطريق</option>
                            <option value="unloading" {{ $cargo->status == 'unloading' ? 'selected' : '' }}>قيد التفريغ</option>
                            <option value="unloaded" {{ $cargo->status == 'unloaded' ? 'selected' : '' }}>مفرغ</option>
                            <option value="stored" {{ $cargo->status == 'stored' ? 'selected' : '' }}>مخزن</option>
                            <option value="delivered" {{ $cargo->status == 'delivered' ? 'selected' : '' }}>مسلم</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ $cargo->notes }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endcan

<!-- نموذج تأكيد الحذف -->
@can('cargos.delete')
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه الحمولة؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ route('cargos.destroy', $cargo) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
