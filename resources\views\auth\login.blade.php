<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تسجيل الدخول - نظام إدارة مرفأ اللاذقية</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            line-height: 1.6;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .login-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
        }

        .port-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .login-title {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .login-subtitle {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.1rem;
            font-weight: 400;
            opacity: 0.9;
        }

        .login-form {
            padding: 3rem 2rem;
        }

        .login-form h2 {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 600;
        }

        .login-form p {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 400;
        }

        .form-label {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .remember-me {
            margin: 1.5rem 0;
        }

        .remember-me .form-check-input {
            border-radius: 6px;
            border: 2px solid #dee2e6;
        }

        .remember-me .form-check-input:checked {
            background-color: #2a5298;
            border-color: #2a5298;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .forgot-password:hover {
            color: #5a6fd8;
        }

        .alert {
            font-family: 'Noto Sans Arabic', sans-serif;
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .waves {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" fill="%23ffffff" opacity="0.1"/></svg>') no-repeat;
            background-size: cover;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                border-radius: 15px;
            }

            .login-header {
                padding: 2rem 1rem;
            }

            .login-title {
                font-size: 2rem;
            }

            .login-form {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- Header Section -->
                        <div class="col-lg-5">
                            <div class="login-header h-100 d-flex flex-column justify-content-center">
                                <div class="position-relative">
                                    <i class="fas fa-anchor port-icon"></i>
                                    <h1 class="login-title">مرفأ اللاذقية</h1>
                                    <p class="login-subtitle">نظام إدارة العمليات البحرية</p>
                                    <div class="waves"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Section -->
                        <div class="col-lg-7">
                            <div class="login-form">
                                <div class="text-center mb-4">
                                    <h2 class="h3 text-dark mb-2">مرحباً بك</h2>
                                    <p class="text-muted">يرجى تسجيل الدخول للوصول إلى النظام</p>
                                </div>

                                <!-- Session Status -->
                                @if (session('status'))
                                    <div class="alert alert-success" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        {{ session('status') }}
                                    </div>
                                @endif

                                <!-- Login Form -->
                                <form method="POST" action="{{ route('login') }}">
                                    @csrf

                                    <!-- Email Address -->
                                    <div class="form-floating">
                                        <input type="email"
                                               class="form-control @error('email') is-invalid @enderror"
                                               id="email"
                                               name="email"
                                               value="{{ old('email') }}"
                                               placeholder="البريد الإلكتروني"
                                               required
                                               autofocus
                                               autocomplete="username">
                                        <label for="email">
                                            <i class="fas fa-envelope me-2"></i>
                                            البريد الإلكتروني
                                        </label>
                                        @error('email')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>

                                    <!-- Password -->
                                    <div class="form-floating">
                                        <input type="password"
                                               class="form-control @error('password') is-invalid @enderror"
                                               id="password"
                                               name="password"
                                               placeholder="كلمة المرور"
                                               required
                                               autocomplete="current-password">
                                        <label for="password">
                                            <i class="fas fa-lock me-2"></i>
                                            كلمة المرور
                                        </label>
                                        @error('password')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>

                                    <!-- Remember Me -->
                                    <div class="remember-me">
                                        <div class="form-check">
                                            <input class="form-check-input"
                                                   type="checkbox"
                                                   id="remember_me"
                                                   name="remember">
                                            <label class="form-check-label" for="remember_me">
                                                تذكرني
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <button type="submit" class="btn btn-login">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول
                                    </button>

                                    <!-- Forgot Password -->
                                    @if (Route::has('password.request'))
                                        <div class="text-center mt-3">
                                            <a href="{{ route('password.request') }}" class="forgot-password">
                                                <i class="fas fa-key me-1"></i>
                                                نسيت كلمة المرور؟
                                            </a>
                                        </div>
                                    @endif
                                </form>

                                <!-- Demo Credentials -->
                                <div class="mt-4 p-3 bg-light rounded">
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-info-circle me-2"></i>
                                        بيانات تجريبية:
                                    </h6>
                                    <small class="text-muted">
                                        <strong>البريد:</strong> <EMAIL><br>
                                        <strong>كلمة المرور:</strong> password123
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
