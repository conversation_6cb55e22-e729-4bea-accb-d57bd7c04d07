<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipBerthHistory extends Model
{
    protected $table = 'ship_berth_history';

    protected $fillable = [
        'ship_id',
        'berth_id',
        'berthing_start',
        'berthing_end',
        'berthing_hours',
        'hourly_rate',
        'total_berthing_cost',
        'status',
        'notes'
    ];

    protected $casts = [
        'berthing_start' => 'datetime',
        'berthing_end' => 'datetime',
        'hourly_rate' => 'decimal:2',
        'total_berthing_cost' => 'decimal:2'
    ];

    // Aliases for backward compatibility
    public function getBerthedAtAttribute()
    {
        return $this->berthing_start;
    }

    public function getDepartedAtAttribute()
    {
        return $this->berthing_end;
    }

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function berth(): BelongsTo
    {
        return $this->belongsTo(Berth::class);
    }
}
