@extends('layouts.port-app')

@section('page-title', 'إضافة تفتيش جديد')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('inspections.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة تفتيش جديد
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('inspections.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- معلومات السفينة -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-ship me-2"></i>
                                معلومات السفينة
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ship_id" class="form-label">السفينة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('ship_id') is-invalid @enderror" 
                                                id="ship_id" name="ship_id" required>
                                            <option value="">اختر السفينة</option>
                                            @foreach($ships as $ship)
                                            <option value="{{ $ship->id }}" {{ old('ship_id') == $ship->id ? 'selected' : '' }}>
                                                {{ $ship->name }} - {{ $ship->flag }} ({{ $ship->agent->name }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('ship_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات التفتيش -->
                        <div class="col-md-12 mt-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-search me-2"></i>
                                معلومات التفتيش
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="inspection_type" class="form-label">نوع التفتيش <span class="text-danger">*</span></label>
                                        <select class="form-select @error('inspection_type') is-invalid @enderror" 
                                                id="inspection_type" name="inspection_type" required>
                                            <option value="">اختر نوع التفتيش</option>
                                            <option value="customs" {{ old('inspection_type') == 'customs' ? 'selected' : '' }}>
                                                <i class="fas fa-file-invoice"></i> جمركي
                                            </option>
                                            <option value="health" {{ old('inspection_type') == 'health' ? 'selected' : '' }}>
                                                <i class="fas fa-heartbeat"></i> صحي
                                            </option>
                                            <option value="security" {{ old('inspection_type') == 'security' ? 'selected' : '' }}>
                                                <i class="fas fa-shield-alt"></i> أمني
                                            </option>
                                            <option value="technical" {{ old('inspection_type') == 'technical' ? 'selected' : '' }}>
                                                <i class="fas fa-tools"></i> فني
                                            </option>
                                        </select>
                                        @error('inspection_type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="inspection_date" class="form-label">تاريخ ووقت التفتيش <span class="text-danger">*</span></label>
                                        <input type="datetime-local" class="form-control @error('inspection_date') is-invalid @enderror" 
                                               id="inspection_date" name="inspection_date" 
                                               value="{{ old('inspection_date', now()->format('Y-m-d\TH:i')) }}" required>
                                        @error('inspection_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات المفتش -->
                        <div class="col-md-12 mt-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user-tie me-2"></i>
                                معلومات المفتش
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="inspector_name" class="form-label">اسم المفتش <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('inspector_name') is-invalid @enderror" 
                                               id="inspector_name" name="inspector_name" value="{{ old('inspector_name') }}" required>
                                        @error('inspector_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="inspector_authority" class="form-label">الجهة المفتشة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('inspector_authority') is-invalid @enderror" 
                                               id="inspector_authority" name="inspector_authority" value="{{ old('inspector_authority') }}" required>
                                        @error('inspector_authority')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نتيجة التفتيش -->
                        <div class="col-md-12 mt-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-clipboard-check me-2"></i>
                                نتيجة التفتيش
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="result" class="form-label">النتيجة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('result') is-invalid @enderror" 
                                                id="result" name="result" required>
                                            <option value="">اختر النتيجة</option>
                                            <option value="approved" {{ old('result') == 'approved' ? 'selected' : '' }}>موافق</option>
                                            <option value="rejected" {{ old('result') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                            <option value="conditional" {{ old('result') == 'conditional' ? 'selected' : '' }}>موافق مشروط</option>
                                        </select>
                                        @error('result')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="valid_until" class="form-label">صالح حتى</label>
                                        <input type="date" class="form-control @error('valid_until') is-invalid @enderror" 
                                               id="valid_until" name="valid_until" value="{{ old('valid_until') }}">
                                        @error('valid_until')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">اتركه فارغاً إذا كان التفتيش صالح إلى أجل غير مسمى</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- التفاصيل الإضافية -->
                        <div class="col-md-12 mt-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                التفاصيل الإضافية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                                  id="notes" name="notes" rows="3" 
                                                  placeholder="أدخل أي ملاحظات حول التفتيش">{{ old('notes') }}</textarea>
                                        @error('notes')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row" id="violations-section" style="display: none;">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="violations" class="form-label">المخالفات</label>
                                        <textarea class="form-control @error('violations') is-invalid @enderror" 
                                                  id="violations" name="violations" rows="3" 
                                                  placeholder="اذكر المخالفات المكتشفة (في حالة الرفض)">{{ old('violations') }}</textarea>
                                        @error('violations')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row" id="conditions-section" style="display: none;">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="conditions" class="form-label">الشروط</label>
                                        <textarea class="form-control @error('conditions') is-invalid @enderror" 
                                                  id="conditions" name="conditions" rows="3" 
                                                  placeholder="اذكر الشروط المطلوبة (في حالة الموافقة المشروطة)">{{ old('conditions') }}</textarea>
                                        @error('conditions')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ route('inspections.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التفتيش
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resultSelect = document.getElementById('result');
    const violationsSection = document.getElementById('violations-section');
    const conditionsSection = document.getElementById('conditions-section');
    
    function toggleSections() {
        const result = resultSelect.value;
        
        // إخفاء جميع الأقسام أولاً
        violationsSection.style.display = 'none';
        conditionsSection.style.display = 'none';
        
        // إظهار القسم المناسب
        if (result === 'rejected') {
            violationsSection.style.display = 'block';
        } else if (result === 'conditional') {
            conditionsSection.style.display = 'block';
        }
    }
    
    // تشغيل الدالة عند تغيير النتيجة
    resultSelect.addEventListener('change', toggleSections);
    
    // تشغيل الدالة عند تحميل الصفحة
    toggleSections();
});
</script>
@endsection
