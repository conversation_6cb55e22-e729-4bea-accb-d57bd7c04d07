<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Agent;
use App\Models\Ship;
use App\Models\Berth;
use App\Models\Cargo;
use App\Models\CargoStorage;
use App\Models\Warehouse;
use App\Models\Inspection;
use App\Models\Invoice;
use Carbon\Carbon;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إضافة وكلاء ملاحيين تجريبيين
        $agents = [
            [
                'name' => 'محمد أحمد العلي',
                'company_name' => 'شركة العلي للنقل البحري',
                'phone' => '+963-41-123456',
                'email' => '<EMAIL>',
                'address' => 'شارع الميناء، اللاذقية، سوريا',
                'license_number' => 'SH-2023-001',
                'is_active' => true
            ],
            [
                'name' => 'فاطمة خالد الشامي',
                'company_name' => 'مجموعة الشام للخدمات البحرية',
                'phone' => '+963-41-234567',
                'email' => '<EMAIL>',
                'address' => 'شارع الكورنيش، اللاذقية، سوريا',
                'license_number' => 'SH-2023-002',
                'is_active' => true
            ],
            [
                'name' => 'عمر محمود الحلبي',
                'company_name' => 'شركة حلب للنقل والتجارة',
                'phone' => '+963-41-345678',
                'email' => '<EMAIL>',
                'address' => 'شارع التجارة، حلب، سوريا',
                'license_number' => 'SH-2023-003',
                'is_active' => true
            ],
            [
                'name' => 'لينا سعيد الدمشقي',
                'company_name' => 'مؤسسة دمشق للخدمات اللوجستية',
                'phone' => '+963-11-456789',
                'email' => '<EMAIL>',
                'address' => 'شارع بغداد، دمشق، سوريا',
                'license_number' => 'SH-2023-004',
                'is_active' => false
            ]
        ];

        foreach ($agents as $agentData) {
            Agent::create($agentData);
        }

        // إضافة أرصفة تجريبية
        $berths = [
            [
                'name' => 'الرصيف الأول',
                'code' => 'B001',
                'length' => 200.00,
                'depth' => 12.00,
                'max_tonnage' => 50000,
                'status' => 'available'
            ],
            [
                'name' => 'الرصيف الثاني',
                'code' => 'B002',
                'length' => 180.00,
                'depth' => 10.00,
                'max_tonnage' => 40000,
                'status' => 'available'
            ],
            [
                'name' => 'الرصيف الثالث',
                'code' => 'B003',
                'length' => 250.00,
                'depth' => 15.00,
                'max_tonnage' => 70000,
                'status' => 'occupied'
            ],
            [
                'name' => 'رصيف الحاويات',
                'code' => 'B004',
                'length' => 300.00,
                'depth' => 16.00,
                'max_tonnage' => 80000,
                'status' => 'available'
            ]
        ];

        foreach ($berths as $berthData) {
            Berth::create($berthData);
        }

        // إضافة مستودعات تجريبية
        $warehouses = [
            [
                'name' => 'مستودع رقم 1',
                'code' => 'WH001',
                'type' => 'warehouse',
                'total_area' => 5000.00,
                'available_area' => 3000.00,
                'max_capacity' => 10000.00,
                'current_capacity' => 2000.00,
                'is_covered' => true,
                'daily_rate' => 50.00,
                'status' => 'active',
                'description' => 'مستودع مغطى للبضائع العامة',
                'location' => 'المنطقة الشمالية',
                'manager_name' => 'أحمد محمد',
                'manager_phone' => '+963-41-123456'
            ],
            [
                'name' => 'ساحة الحاويات',
                'code' => 'YD001',
                'type' => 'yard',
                'total_area' => 10000.00,
                'available_area' => 7500.00,
                'max_capacity' => 500.00,
                'current_capacity' => 150.00,
                'is_covered' => false,
                'daily_rate' => 30.00,
                'status' => 'active',
                'description' => 'ساحة مفتوحة لتخزين الحاويات',
                'location' => 'المنطقة الشرقية',
                'manager_name' => 'فاطمة علي',
                'manager_phone' => '+963-41-234567'
            ],
            [
                'name' => 'مستودع التبريد',
                'code' => 'CS001',
                'type' => 'cold_storage',
                'total_area' => 2000.00,
                'available_area' => 1200.00,
                'max_capacity' => 3000.00,
                'current_capacity' => 800.00,
                'is_covered' => true,
                'daily_rate' => 80.00,
                'status' => 'active',
                'description' => 'مستودع تبريد للمواد الغذائية',
                'location' => 'المنطقة الغربية',
                'manager_name' => 'عمر خالد',
                'manager_phone' => '+963-41-345678'
            ],
            [
                'name' => 'خزان الزيوت',
                'code' => 'TK001',
                'type' => 'tank',
                'total_area' => 1500.00,
                'available_area' => 500.00,
                'max_capacity' => 5000.00,
                'current_capacity' => 3500.00,
                'is_covered' => true,
                'daily_rate' => 60.00,
                'status' => 'active',
                'description' => 'خزان لتخزين الزيوت والسوائل',
                'location' => 'المنطقة الجنوبية',
                'manager_name' => 'لينا أحمد',
                'manager_phone' => '+963-41-456789'
            ],
            [
                'name' => 'صومعة الحبوب',
                'code' => 'SL001',
                'type' => 'silo',
                'total_area' => 3000.00,
                'available_area' => 1000.00,
                'max_capacity' => 8000.00,
                'current_capacity' => 5000.00,
                'is_covered' => true,
                'daily_rate' => 40.00,
                'status' => 'active',
                'description' => 'صومعة لتخزين الحبوب والقمح',
                'location' => 'المنطقة الوسطى',
                'manager_name' => 'محمد علي',
                'manager_phone' => '+963-41-567890'
            ],
            [
                'name' => 'مستودع قيد الصيانة',
                'code' => 'WH002',
                'type' => 'warehouse',
                'total_area' => 4000.00,
                'available_area' => 4000.00,
                'max_capacity' => 6000.00,
                'current_capacity' => 0.00,
                'is_covered' => true,
                'daily_rate' => 45.00,
                'status' => 'maintenance',
                'description' => 'مستودع قيد الصيانة والتطوير',
                'location' => 'المنطقة الشمالية',
                'manager_name' => 'سارة محمد',
                'manager_phone' => '+963-41-678901'
            ]
        ];

        foreach ($warehouses as $warehouseData) {
            Warehouse::create($warehouseData);
        }

        // إضافة سفن تجريبية
        $ships = [
            [
                'name' => 'سفينة الأمل',
                'imo_number' => '9123456',
                'flag' => 'سوريا',
                'ship_type' => 'حاويات',
                'length' => 180.50,
                'width' => 25.00,
                'draft' => 8.50,
                'gross_tonnage' => 15000,
                'agent_id' => 1,
                'owner_company' => 'شركة النقل البحري السورية',
                'arrival_notice_date' => Carbon::now()->subDays(2),
                'expected_arrival_date' => Carbon::now()->addHours(6),
                'actual_arrival_date' => null,
                'departure_date' => null,
                'status' => 'announced',
                'notes' => 'سفينة حاويات قادمة من ميناء بيروت'
            ],
            [
                'name' => 'بحر العرب',
                'imo_number' => '9234567',
                'flag' => 'لبنان',
                'ship_type' => 'بضائع عامة',
                'length' => 150.00,
                'width' => 20.00,
                'draft' => 7.00,
                'gross_tonnage' => 8000,
                'agent_id' => 2,
                'owner_company' => 'شركة النقل اللبنانية',
                'arrival_notice_date' => Carbon::now()->subDays(1),
                'expected_arrival_date' => Carbon::now()->subHours(2),
                'actual_arrival_date' => Carbon::now()->subHours(3),
                'departure_date' => null,
                'status' => 'outside_port',
                'notes' => 'في انتظار تخصيص رصيف'
            ],
            [
                'name' => 'نجمة الشرق',
                'imo_number' => '9345678',
                'flag' => 'الإمارات',
                'ship_type' => 'صب جاف',
                'length' => 220.00,
                'width' => 32.00,
                'draft' => 12.00,
                'gross_tonnage' => 35000,
                'agent_id' => 1,
                'owner_company' => 'شركة الإمارات للنقل',
                'arrival_notice_date' => Carbon::now()->subDays(3),
                'expected_arrival_date' => Carbon::now()->subDays(1),
                'actual_arrival_date' => Carbon::now()->subDay(),
                'departure_date' => null,
                'status' => 'berthed',
                'notes' => 'مرسية في الرصيف الثالث'
            ],
            [
                'name' => 'فينيقيا الجميلة',
                'imo_number' => '9456789',
                'flag' => 'إيطاليا',
                'ship_type' => 'ركاب',
                'length' => 120.00,
                'width' => 18.00,
                'draft' => 5.50,
                'gross_tonnage' => 5000,
                'agent_id' => 3,
                'owner_company' => 'شركة النقل الإيطالية',
                'arrival_notice_date' => Carbon::now()->subDays(5),
                'expected_arrival_date' => Carbon::now()->subDays(3),
                'actual_arrival_date' => Carbon::now()->subDays(3),
                'departure_date' => Carbon::now()->subDay(),
                'status' => 'departed',
                'notes' => 'غادرت بعد إنهاء عمليات الصيانة'
            ]
        ];

        foreach ($ships as $shipData) {
            Ship::create($shipData);
        }



        // إضافة فحوصات تجريبية
        $inspections = [
            [
                'ship_id' => 2,
                'inspection_type' => 'customs',
                'inspector_name' => 'أحمد محمد العلي',
                'inspector_authority' => 'الجمارك السورية',
                'inspection_date' => Carbon::now()->subHours(2),
                'result' => 'approved',
                'notes' => 'تم الفحص بنجاح ولا توجد مخالفات',
                'valid_until' => Carbon::now()->addMonths(6)
            ],
            [
                'ship_id' => 3,
                'inspection_type' => 'health',
                'inspector_name' => 'د. فاطمة الزهراء',
                'inspector_authority' => 'وزارة الصحة',
                'inspection_date' => Carbon::now()->subDay(),
                'result' => 'approved',
                'notes' => 'الفحص الصحي مطابق للمعايير',
                'valid_until' => Carbon::now()->addMonths(3)
            ],
            [
                'ship_id' => 1,
                'inspection_type' => 'security',
                'inspector_name' => 'عمر خالد الأمين',
                'inspector_authority' => 'أمن المرفأ',
                'inspection_date' => Carbon::now()->subHours(6),
                'result' => 'conditional',
                'notes' => 'تفتيش أمني مع ملاحظات',
                'conditions' => 'يجب تعزيز إجراءات الأمان وتحديث أنظمة المراقبة',
                'valid_until' => Carbon::now()->addWeeks(2)
            ],
            [
                'ship_id' => 4,
                'inspection_type' => 'technical',
                'inspector_name' => 'مهندس سامر التقني',
                'inspector_authority' => 'هيئة النقل البحري',
                'inspection_date' => Carbon::now()->subDays(3),
                'result' => 'approved',
                'notes' => 'فحص فني شامل للسفينة ومعداتها',
                'valid_until' => Carbon::now()->addYear()
            ],
            [
                'ship_id' => 3,
                'inspection_type' => 'customs',
                'inspector_name' => 'لينا محمود الجمركية',
                'inspector_authority' => 'الجمارك السورية',
                'inspection_date' => Carbon::now()->subDays(5),
                'result' => 'rejected',
                'notes' => 'تم رفض التفتيش بسبب مخالفات جمركية',
                'violations' => 'عدم اكتمال الوثائق الجمركية المطلوبة وعدم تطابق بيانات الحمولة مع البيان الجمركي'
            ],
            [
                'ship_id' => 2,
                'inspection_type' => 'technical',
                'inspector_name' => 'مهندس أحمد الفني',
                'inspector_authority' => 'هيئة النقل البحري',
                'inspection_date' => Carbon::now()->subWeek(),
                'result' => 'approved',
                'notes' => 'فحص فني دوري للسفينة',
                'valid_until' => Carbon::now()->addMonths(6)
            ]
        ];

        foreach ($inspections as $inspectionData) {
            Inspection::create($inspectionData);
        }

        // إضافة حمولات تجريبية
        $cargos = [
            [
                'ship_id' => 1,
                'cargo_type' => 'container',
                'description' => 'حاويات معدات وآلات',
                'quantity' => 50.00,
                'unit' => 'containers',
                'weight' => 1250.00,
                'volume' => 1400.00,
                'value' => 500000.00,
                'currency' => 'USD',
                'origin_country' => 'الصين',
                'destination_country' => 'سوريا',
                'status' => 'unloading',
                'loading_date' => Carbon::now()->subDays(5),
                'unloading_date' => Carbon::now(),
                'notes' => 'حمولة معدات وآلات صناعية'
            ],
            [
                'ship_id' => 1,
                'cargo_type' => 'general',
                'description' => 'بضائع عامة ومواد غذائية',
                'quantity' => 200.00,
                'unit' => 'tons',
                'weight' => 200.00,
                'volume' => 300.00,
                'value' => 150000.00,
                'currency' => 'USD',
                'origin_country' => 'تركيا',
                'destination_country' => 'سوريا',
                'status' => 'unloaded',
                'loading_date' => Carbon::now()->subDays(4),
                'unloading_date' => Carbon::now()->subHours(6),
                'notes' => 'بضائع عامة متنوعة'
            ],
            [
                'ship_id' => 2,
                'cargo_type' => 'bulk',
                'description' => 'قمح وحبوب',
                'quantity' => 5000.00,
                'unit' => 'tons',
                'weight' => 5000.00,
                'volume' => 6500.00,
                'value' => 2000000.00,
                'currency' => 'USD',
                'origin_country' => 'روسيا',
                'destination_country' => 'سوريا',
                'status' => 'loading',
                'loading_date' => Carbon::now()->subDays(2),
                'notes' => 'قمح عالي الجودة'
            ],
            [
                'ship_id' => 3,
                'cargo_type' => 'liquid',
                'description' => 'زيوت نباتية',
                'quantity' => 1000.00,
                'unit' => 'tons',
                'weight' => 1000.00,
                'volume' => 1100.00,
                'value' => 800000.00,
                'currency' => 'EUR',
                'origin_country' => 'إسبانيا',
                'destination_country' => 'سوريا',
                'status' => 'in_transit',
                'loading_date' => Carbon::now()->subDays(7),
                'notes' => 'زيت زيتون ممتاز'
            ],
            [
                'ship_id' => 4,
                'cargo_type' => 'vehicles',
                'description' => 'سيارات ركوب',
                'quantity' => 100.00,
                'unit' => 'pieces',
                'weight' => 150.00,
                'volume' => 800.00,
                'value' => 2500000.00,
                'currency' => 'USD',
                'origin_country' => 'اليابان',
                'destination_country' => 'سوريا',
                'status' => 'delivered',
                'loading_date' => Carbon::now()->subDays(10),
                'unloading_date' => Carbon::now()->subDays(3),
                'notes' => 'سيارات يابانية حديثة'
            ],
            [
                'ship_id' => 4,
                'cargo_type' => 'refrigerated',
                'description' => 'فواكه وخضروات مجمدة',
                'quantity' => 300.00,
                'unit' => 'tons',
                'weight' => 300.00,
                'volume' => 400.00,
                'value' => 180000.00,
                'currency' => 'USD',
                'origin_country' => 'هولندا',
                'destination_country' => 'سوريا',
                'status' => 'stored',
                'loading_date' => Carbon::now()->subDays(6),
                'unloading_date' => Carbon::now()->subDays(2),
                'notes' => 'فواكه وخضروات طازجة'
            ]
        ];

        foreach ($cargos as $cargoData) {
            Cargo::create($cargoData);
        }

        // تحديث حالة الرصيف الثالث
        Berth::where('code', 'B003')->update([
            'current_ship_id' => 3,
            'occupied_since' => Carbon::now()->subDay()
        ]);

        // إضافة بيانات تخزين تجريبية
        $cargoStorages = [
            [
                'cargo_id' => 1, // حاويات معدات
                'warehouse_id' => 2, // ساحة الحاويات
                'stored_quantity' => 25.00,
                'storage_start' => Carbon::now()->subDays(3),
                'storage_end' => null,
                'storage_days' => 3,
                'daily_rate' => 30.00,
                'total_storage_cost' => 90.00,
                'status' => 'active',
                'notes' => 'تخزين مؤقت في انتظار النقل'
            ],
            [
                'cargo_id' => 4, // فواكه مجمدة
                'warehouse_id' => 3, // مستودع التبريد
                'stored_quantity' => 150.00,
                'storage_start' => Carbon::now()->subDays(2),
                'storage_end' => null,
                'storage_days' => 2,
                'daily_rate' => 80.00,
                'total_storage_cost' => 160.00,
                'status' => 'active',
                'notes' => 'تخزين في التبريد'
            ],
            [
                'cargo_id' => 5, // زيوت نباتية
                'warehouse_id' => 4, // خزان الزيوت
                'stored_quantity' => 2000.00,
                'storage_start' => Carbon::now()->subDays(5),
                'storage_end' => null,
                'storage_days' => 5,
                'daily_rate' => 60.00,
                'total_storage_cost' => 300.00,
                'status' => 'active',
                'notes' => 'تخزين في خزان مخصص'
            ],
            [
                'cargo_id' => 6, // قمح
                'warehouse_id' => 5, // صومعة الحبوب
                'stored_quantity' => 3000.00,
                'storage_start' => Carbon::now()->subDays(7),
                'storage_end' => null,
                'storage_days' => 7,
                'daily_rate' => 40.00,
                'total_storage_cost' => 280.00,
                'status' => 'active',
                'notes' => 'تخزين في الصومعة'
            ],
            [
                'cargo_id' => 2, // معدات ثقيلة (منتهية)
                'warehouse_id' => 1, // مستودع رقم 1
                'stored_quantity' => 500.00,
                'storage_start' => Carbon::now()->subDays(10),
                'storage_end' => Carbon::now()->subDays(2),
                'storage_days' => 8,
                'daily_rate' => 50.00,
                'total_storage_cost' => 400.00,
                'status' => 'completed',
                'notes' => 'تم استلام البضاعة'
            ]
        ];

        foreach ($cargoStorages as $storageData) {
            CargoStorage::create($storageData);
        }
    }
}
