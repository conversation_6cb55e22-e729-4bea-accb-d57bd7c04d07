<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $permission = null): Response
    {
        if ($permission && auth()->check()) {
            if (!auth()->user()->can($permission)) {
                abort(403, 'لا تملك صلاحية للوصول إلى هذه الصفحة');
            }
        }

        return $next($request);
    }
}
