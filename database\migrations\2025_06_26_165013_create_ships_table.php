<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ships', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم السفينة
            $table->string('imo_number')->nullable(); // رقم IMO
            $table->string('flag'); // جنسية السفينة
            $table->string('ship_type'); // نوع السفيعة
            $table->decimal('length', 8, 2)->nullable(); // طول السفينة
            $table->decimal('width', 8, 2)->nullable(); // عرض السفينة
            $table->decimal('draft', 8, 2)->nullable(); // عمق السفينة
            $table->integer('gross_tonnage')->nullable(); // الحمولة الإجمالية
            $table->foreignId('agent_id')->constrained('agents'); // الوكيل الملاحي
            $table->string('owner_company')->nullable(); // الشركة المالكة
            $table->datetime('arrival_notice_date')->nullable(); // تاريخ إشعار الوصول
            $table->datetime('expected_arrival_date')->nullable(); // تاريخ الوصول المتوقع
            $table->datetime('actual_arrival_date')->nullable(); // تاريخ الوصول الفعلي
            $table->datetime('departure_date')->nullable(); // تاريخ المغادرة
            $table->enum('status', ['announced', 'outside_port', 'berthed', 'departed'])->default('announced'); // حالة السفينة
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ships');
    }
};
