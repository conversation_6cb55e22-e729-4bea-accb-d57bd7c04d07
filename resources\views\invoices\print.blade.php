<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ $invoice->invoice_number }}</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body, * {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        body {
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
            font-weight: 400;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1, .header h2 {
            font-family: 'Noto Sans Arabic', '<PERSON><PERSON><PERSON><PERSON>', <PERSON><PERSON>, Tahoma, sans-serif !important;
            font-weight: 600;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        
        .header h2 {
            color: #34495e;
            margin: 5px 0;
            font-size: 18px;
        }
        
        .invoice-info {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .invoice-info .left,
        .invoice-info .right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        
        .invoice-info .right {
            text-align: left;
        }
        
        .info-box {
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .info-box h3, .info-box p {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
            font-weight: 600;
        }
        
        .info-box p {
            margin: 5px 0;
            color: #6c757d;
        }
        
        .invoice-details {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .invoice-details th,
        .invoice-details td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .invoice-details th {
            background-color: #343a40;
            color: white;
            font-weight: 600;
        }
        
        .invoice-details .amount {
            text-align: left;
            font-weight: 600;
        }
        
        .total-section {
            width: 50%;
            margin-left: auto;
            margin-bottom: 30px;
        }
        
        .total-row {
            display: table;
            width: 100%;
            margin-bottom: 5px;
        }
        
        .total-label,
        .total-value {
            display: table-cell;
            padding: 8px;
            border: 1px solid #dee2e6;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .total-label {
            background-color: #f8f9fa;
            font-weight: 600;
            width: 60%;
        }
        
        .total-value {
            text-align: left;
            font-weight: 600;
            width: 40%;
        }
        
        .grand-total {
            background-color: #28a745 !important;
            color: white !important;
        }
        
        .payment-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .payment-info h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
            font-weight: 600;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: 600;
            font-size: 11px;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .status-draft { background-color: #6c757d; }
        .status-sent { background-color: #ffc107; color: #212529; }
        .status-paid { background-color: #28a745; }
        .status-overdue { background-color: #dc3545; }
        .status-cancelled { background-color: #343a40; }
        
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
        
        .footer p {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        .notes {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .notes h3 {
            margin: 0 0 10px 0;
            color: #856404;
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
            font-weight: 600;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        table, th, td {
            font-family: 'Noto Sans Arabic', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
    </style>
</head>
<body>
    <!-- رأس الفاتورة -->
    <div class="header">
        <h1>{{ config('app.name') }}</h1>
        <h2>فاتورة رسوم مرفئية</h2>
        <p>رقم الفاتورة: <strong>{{ $invoice->invoice_number }}</strong></p>
        <p>تاريخ الإصدار: {{ $invoice->invoice_date->format('Y/m/d') }}</p>
        <p>تاريخ الاستحقاق: {{ $invoice->due_date ? $invoice->due_date->format('Y/m/d') : '-' }}</p>
        <span class="status-badge status-{{ $invoice->status }}">{{ $invoice->status_label }}</span>
    </div>

    <!-- معلومات الفاتورة -->
    <div class="invoice-info">
        <div class="right">
            <div class="info-box">
                <h3>معلومات السفينة</h3>
                <p><strong>اسم السفينة:</strong> {{ $invoice->ship->name }}</p>
                <p><strong>نوع السفينة:</strong> {{ $invoice->ship->ship_type }}</p>
                <p><strong>العلم:</strong> {{ $invoice->ship->flag }}</p>
                <p><strong>الطول:</strong> {{ $invoice->ship->length }} متر</p>
                <p><strong>الحمولة:</strong> {{ $invoice->ship->gross_tonnage }} طن</p>
                @if($invoice->ship->arrival_date)
                <p><strong>تاريخ الوصول:</strong> {{ $invoice->ship->arrival_date->format('Y/m/d') }}</p>
                @endif
            </div>
        </div>
        
        <div class="left">
            <div class="info-box">
                <h3>معلومات الوكيل الملاحي</h3>
                <p><strong>اسم الوكيل:</strong> {{ $invoice->agent->name }}</p>
                <p><strong>اسم الشركة:</strong> {{ $invoice->agent->company_name }}</p>
                @if($invoice->agent->phone)
                <p><strong>الهاتف:</strong> {{ $invoice->agent->phone }}</p>
                @endif
                @if($invoice->agent->email)
                <p><strong>البريد الإلكتروني:</strong> {{ $invoice->agent->email }}</p>
                @endif
                @if($invoice->agent->address)
                <p><strong>العنوان:</strong> {{ $invoice->agent->address }}</p>
                @endif
            </div>
        </div>
    </div>

    <!-- تفاصيل الرسوم -->
    <table class="invoice-details">
        <thead>
            <tr>
                <th>نوع الرسوم</th>
                <th>الوصف</th>
                <th>المبلغ (ل.س)</th>
            </tr>
        </thead>
        <tbody>
            @if($invoice->berthing_fees > 0)
            <tr>
                <td>رسوم الرسو</td>
                <td>رسوم رسو السفينة في المرفأ</td>
                <td class="amount">{{ number_format($invoice->berthing_fees, 2) }}</td>
            </tr>
            @endif
            
            @if($invoice->cargo_handling_fees > 0)
            <tr>
                <td>رسوم مناولة البضائع</td>
                <td>رسوم تحميل وتفريغ البضائع</td>
                <td class="amount">{{ number_format($invoice->cargo_handling_fees, 2) }}</td>
            </tr>
            @endif
            
            @if($invoice->storage_fees > 0)
            <tr>
                <td>رسوم التخزين</td>
                <td>رسوم تخزين البضائع في المستودعات</td>
                <td class="amount">{{ number_format($invoice->storage_fees, 2) }}</td>
            </tr>
            @endif
            
            @if($invoice->pilotage_fees > 0)
            <tr>
                <td>رسوم الإرشاد</td>
                <td>رسوم إرشاد السفينة داخل المرفأ</td>
                <td class="amount">{{ number_format($invoice->pilotage_fees, 2) }}</td>
            </tr>
            @endif
            
            @if($invoice->other_fees > 0)
            <tr>
                <td>رسوم أخرى</td>
                <td>رسوم إضافية متنوعة</td>
                <td class="amount">{{ number_format($invoice->other_fees, 2) }}</td>
            </tr>
            @endif
        </tbody>
    </table>

    <!-- إجمالي المبالغ -->
    <div class="total-section">
        <div class="total-row">
            <div class="total-label">المبلغ الإجمالي:</div>
            <div class="total-value">{{ number_format($invoice->total_amount, 2) }} ل.س</div>
        </div>
        <div class="total-row">
            <div class="total-label">المبلغ المدفوع:</div>
            <div class="total-value">{{ number_format($invoice->paid_amount, 2) }} ل.س</div>
        </div>
        <div class="total-row">
            <div class="total-label grand-total">المبلغ المتبقي:</div>
            <div class="total-value grand-total">{{ number_format($invoice->remaining_amount, 2) }} ل.س</div>
        </div>
    </div>

    <!-- معلومات الدفع -->
    @if($invoice->payments->count() > 0)
    <div class="payment-info">
        <h3>سجل المدفوعات</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #dee2e6; padding: 8px;">التاريخ</th>
                    <th style="border: 1px solid #dee2e6; padding: 8px;">المبلغ</th>
                    <th style="border: 1px solid #dee2e6; padding: 8px;">طريقة الدفع</th>
                    <th style="border: 1px solid #dee2e6; padding: 8px;">رقم المرجع</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoice->payments as $payment)
                <tr>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">{{ $payment->payment_date->format('Y/m/d') }}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">{{ number_format($payment->amount, 2) }} ل.س</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">
                        @switch($payment->payment_method)
                            @case('cash') نقداً @break
                            @case('bank_transfer') تحويل بنكي @break
                            @case('check') شيك @break
                            @case('credit_card') بطاقة ائتمان @break
                            @default {{ $payment->payment_method }}
                        @endswitch
                    </td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">{{ $payment->reference_number ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- الملاحظات -->
    @if($invoice->notes)
    <div class="notes">
        <h3>ملاحظات</h3>
        <p>{{ $invoice->notes }}</p>
    </div>
    @endif

    <!-- تذييل الفاتورة -->
    <div class="footer">
        <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المرفأ البحري</p>
        <p>تاريخ الطباعة: {{ now()->format('Y/m/d H:i') }}</p>
        <p>في حال وجود أي استفسار، يرجى التواصل مع إدارة المرفأ</p>
    </div>
</body>
</html>