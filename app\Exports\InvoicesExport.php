<?php

namespace App\Exports;

use Illuminate\Contracts\Support\Responsable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class InvoicesExport implements FromCollection, WithHeadings, WithMapping, WithStyles, Responsable
{
    private $invoices;
    private $summary;
    private $dateFrom;
    private $dateTo;
    public $fileName = 'تقرير-الفواتير.xlsx';

    public function __construct($invoices, $summary, $dateFrom, $dateTo)
    {
        $this->invoices = $invoices;
        $this->summary = $summary;
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
    }

    public function collection()
    {
        return $this->invoices;
    }

    public function headings(): array
    {
        return [
            'رقم الفاتورة',
            'اسم السفينة',
            'اسم الوكيل',
            'تاريخ الفاتورة',
            'تاريخ الاستحقاق',
            'الحالة',
            'المبلغ الإجمالي',
            'المبلغ المدفوع',
            'المبلغ المتبقي',
        ];
    }

    public function map($invoice): array
    {
        return [
            $invoice->invoice_number,
            $invoice->ship->name ?? '',
            $invoice->agent->name ?? '',
            optional($invoice->invoice_date)->format('Y/m/d'),
            optional($invoice->due_date)->format('Y/m/d'),
            $invoice->status_label ?? $invoice->status,
            $invoice->total_amount,
            $invoice->paid_amount,
            $invoice->remaining_amount,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:I1')->getFont()->setBold(true);
        $sheet->getStyle('A1:I1')->getFont()->setName('Arial');
        $sheet->getDefaultRowDimension()->setRowHeight(20);
        $sheet->getDefaultColumnDimension()->setWidth(20);
        return [];
    }

    public function toResponse($request)
    {
        return \Maatwebsite\Excel\Facades\Excel::download($this, $this->fileName);
    }
} 