@extends('layouts.port-app')

@section('page-title', 'إضافة حمولة جديدة')

@section('page-actions')
<div class="btn-group">
    <a href="{{ route('cargos.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى القائمة
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة حمولة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('cargos.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ship_id" class="form-label">السفينة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('ship_id') is-invalid @enderror" 
                                                id="ship_id" name="ship_id" required>
                                            <option value="">اختر السفينة</option>
                                            @foreach($ships as $ship)
                                                <option value="{{ $ship->id }}" {{ old('ship_id') == $ship->id ? 'selected' : '' }}>
                                                    {{ $ship->name }} - {{ $ship->agent->name ?? 'بدون وكيل' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('ship_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cargo_type" class="form-label">نوع الحمولة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('cargo_type') is-invalid @enderror" 
                                                id="cargo_type" name="cargo_type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="container" {{ old('cargo_type') == 'container' ? 'selected' : '' }}>حاويات</option>
                                            <option value="bulk" {{ old('cargo_type') == 'bulk' ? 'selected' : '' }}>بضائع سائبة</option>
                                            <option value="liquid" {{ old('cargo_type') == 'liquid' ? 'selected' : '' }}>سوائل</option>
                                            <option value="gas" {{ old('cargo_type') == 'gas' ? 'selected' : '' }}>غازات</option>
                                            <option value="vehicles" {{ old('cargo_type') == 'vehicles' ? 'selected' : '' }}>مركبات</option>
                                            <option value="general" {{ old('cargo_type') == 'general' ? 'selected' : '' }}>بضائع عامة</option>
                                            <option value="dangerous" {{ old('cargo_type') == 'dangerous' ? 'selected' : '' }}>مواد خطرة</option>
                                            <option value="refrigerated" {{ old('cargo_type') == 'refrigerated' ? 'selected' : '' }}>مبردة</option>
                                        </select>
                                        @error('cargo_type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف الحمولة <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                                <div class="form-text">وصف تفصيلي للحمولة ومحتوياتها</div>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الكمية والوزن -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-weight me-2"></i>
                                الكمية والوزن
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quantity" class="form-label">الكمية <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control @error('quantity') is-invalid @enderror" 
                                               id="quantity" name="quantity" value="{{ old('quantity') }}" required>
                                        @error('quantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="unit" class="form-label">الوحدة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('unit') is-invalid @enderror" 
                                                id="unit" name="unit" required>
                                            <option value="">اختر الوحدة</option>
                                            <option value="tons" {{ old('unit') == 'tons' ? 'selected' : '' }}>طن</option>
                                            <option value="kg" {{ old('unit') == 'kg' ? 'selected' : '' }}>كيلوغرام</option>
                                            <option value="pieces" {{ old('unit') == 'pieces' ? 'selected' : '' }}>قطعة</option>
                                            <option value="containers" {{ old('unit') == 'containers' ? 'selected' : '' }}>حاوية</option>
                                            <option value="pallets" {{ old('unit') == 'pallets' ? 'selected' : '' }}>منصة</option>
                                            <option value="cubic_meters" {{ old('unit') == 'cubic_meters' ? 'selected' : '' }}>متر مكعب</option>
                                            <option value="liters" {{ old('unit') == 'liters' ? 'selected' : '' }}>لتر</option>
                                        </select>
                                        @error('unit')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="weight" class="form-label">الوزن (طن)</label>
                                        <input type="number" step="0.01" class="form-control @error('weight') is-invalid @enderror" 
                                               id="weight" name="weight" value="{{ old('weight') }}">
                                        @error('weight')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="volume" class="form-label">الحجم (متر مكعب)</label>
                                        <input type="number" step="0.01" class="form-control @error('volume') is-invalid @enderror" 
                                               id="volume" name="volume" value="{{ old('volume') }}">
                                        @error('volume')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="value" class="form-label">القيمة</label>
                                        <input type="number" step="0.01" class="form-control @error('value') is-invalid @enderror" 
                                               id="value" name="value" value="{{ old('value') }}">
                                        @error('value')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="currency" class="form-label">العملة</label>
                                        <select class="form-select @error('currency') is-invalid @enderror" 
                                                id="currency" name="currency">
                                            <option value="">اختر العملة</option>
                                            <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                                            <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                                            <option value="SYP" {{ old('currency') == 'SYP' ? 'selected' : '' }}>ليرة سورية (SYP)</option>
                                            <option value="AED" {{ old('currency') == 'AED' ? 'selected' : '' }}>درهم إماراتي (AED)</option>
                                        </select>
                                        @error('currency')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المنشأ والوجهة -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-globe me-2"></i>
                                المنشأ والوجهة
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="origin_country" class="form-label">بلد المنشأ <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('origin_country') is-invalid @enderror" 
                                               id="origin_country" name="origin_country" value="{{ old('origin_country') }}" required>
                                        @error('origin_country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="destination_country" class="form-label">بلد الوجهة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('destination_country') is-invalid @enderror" 
                                               id="destination_country" name="destination_country" value="{{ old('destination_country') }}" required>
                                        @error('destination_country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الحالة والتواريخ -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-calendar me-2"></i>
                                الحالة والتواريخ
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                        <select class="form-select @error('status') is-invalid @enderror" 
                                                id="status" name="status" required>
                                            <option value="">اختر الحالة</option>
                                            <option value="loading" {{ old('status') == 'loading' ? 'selected' : '' }}>قيد التحميل</option>
                                            <option value="loaded" {{ old('status') == 'loaded' ? 'selected' : '' }}>محمل</option>
                                            <option value="in_transit" {{ old('status') == 'in_transit' ? 'selected' : '' }}>في الطريق</option>
                                            <option value="unloading" {{ old('status') == 'unloading' ? 'selected' : '' }}>قيد التفريغ</option>
                                            <option value="unloaded" {{ old('status') == 'unloaded' ? 'selected' : '' }}>مفرغ</option>
                                            <option value="stored" {{ old('status') == 'stored' ? 'selected' : '' }}>مخزن</option>
                                            <option value="delivered" {{ old('status') == 'delivered' ? 'selected' : '' }}>مسلم</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="loading_date" class="form-label">تاريخ التحميل</label>
                                        <input type="datetime-local" class="form-control @error('loading_date') is-invalid @enderror" 
                                               id="loading_date" name="loading_date" value="{{ old('loading_date') }}">
                                        @error('loading_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="unloading_date" class="form-label">تاريخ التفريغ</label>
                                        <input type="datetime-local" class="form-control @error('unloading_date') is-invalid @enderror" 
                                               id="unloading_date" name="unloading_date" value="{{ old('unloading_date') }}">
                                        @error('unloading_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- ملاحظات -->
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-sticky-note me-2"></i>
                                ملاحظات إضافية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                <div class="form-text">أي ملاحظات إضافية حول الحمولة</div>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('cargos.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الحمولة
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// تحديث تاريخ التفريغ تلقائياً عند تغيير تاريخ التحميل
document.getElementById('loading_date').addEventListener('change', function() {
    const loadingDate = new Date(this.value);
    const unloadingDateField = document.getElementById('unloading_date');
    
    if (loadingDate && !unloadingDateField.value) {
        // إضافة يوم واحد كتاريخ افتراضي للتفريغ
        loadingDate.setDate(loadingDate.getDate() + 1);
        unloadingDateField.value = loadingDate.toISOString().slice(0, 16);
    }
});

// تحديث الحالة تلقائياً بناءً على التواريخ
document.getElementById('loading_date').addEventListener('change', updateStatusBasedOnDates);
document.getElementById('unloading_date').addEventListener('change', updateStatusBasedOnDates);

function updateStatusBasedOnDates() {
    const loadingDate = document.getElementById('loading_date').value;
    const unloadingDate = document.getElementById('unloading_date').value;
    const statusField = document.getElementById('status');
    
    if (loadingDate && !unloadingDate && statusField.value === '') {
        statusField.value = 'loading';
    } else if (loadingDate && unloadingDate && statusField.value === '') {
        statusField.value = 'loaded';
    }
}
</script>
@endpush
