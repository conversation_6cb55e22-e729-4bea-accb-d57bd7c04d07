<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'نظام إدارة المرفأ البحري') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Google Fonts - Noto Sans Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            font-weight: 400;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.5rem;
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }

        .main-content {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .stats-card .stats-number {
            font-size: 2rem;
            font-weight: bold;
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 600;
        }

        .table td {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .user-dropdown {
            background: rgba(255,255,255,0.1);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .user-dropdown:hover {
            background: rgba(255,255,255,0.2);
        }

        /* تطبيق الخط على جميع العناصر */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 600;
        }

        .card-title {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 600;
        }

        .btn {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .form-control, .form-select {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .alert {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .modal-title {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 600;
        }

        .dropdown-menu {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .badge {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .nav-tabs .nav-link {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .breadcrumb {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .pagination {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .form-label {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-weight: 500;
        }

        .text-muted {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .small {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        .lead {
            font-family: 'Noto Sans Arabic', sans-serif;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="navbar-brand">
                            <i class="fas fa-anchor"></i>
                            نظام مرفأ اللاذقية
                        </h4>
                    </div>

                    <!-- معلومات المستخدم -->
                    <div class="user-dropdown mb-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-user-circle fa-2x"></i>
                        </div>
                        <div class="small">
                            <strong>{{ Auth::user()->name }}</strong>
                            <br>
                            <span class="text-light">
                                @if(Auth::user()->roles->first())
                                    {{ Auth::user()->roles->first()->name }}
                                @else
                                    مستخدم
                                @endif
                            </span>
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('dashboard*') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>

                        @can('ships.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('ships*') ? 'active' : '' }}" href="{{ route('ships.index') }}">
                                <i class="fas fa-ship me-2"></i>
                                إدارة السفن
                            </a>
                        </li>
                        @endcan

                        @can('agents.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('agents*') ? 'active' : '' }}" href="{{ route('agents.index') }}">
                                <i class="fas fa-users me-2"></i>
                                الوكلاء الملاحيون
                            </a>
                        </li>
                        @endcan

                        @can('berths.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('berths*') ? 'active' : '' }}" href="{{ route('berths.index') }}">
                                <i class="fas fa-anchor me-2"></i>
                                إدارة الأرصفة
                            </a>
                        </li>
                        @endcan

                        @can('cargos.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('cargos*') ? 'active' : '' }}" href="{{ route('cargos.index') }}">
                                <i class="fas fa-boxes me-2"></i>
                                إدارة الحمولات
                            </a>
                        </li>
                        @endcan

                        @can('warehouses.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('warehouses*') ? 'active' : '' }}" href="{{ route('warehouses.index') }}">
                                <i class="fas fa-warehouse me-2"></i>
                                المستودعات
                            </a>
                        </li>
                        @endcan

                        @can('inspections.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('inspections*') ? 'active' : '' }}" href="{{ route('inspections.index') }}">
                                <i class="fas fa-search me-2"></i>
                                التفتيش
                            </a>
                        </li>
                        @endcan

                        @can('invoices.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('invoices*') ? 'active' : '' }}" href="{{ route('invoices.index') }}">
                                <i class="fas fa-file-invoice me-2"></i>
                                الفواتير
                            </a>
                        </li>
                        @endcan

                        @can('reports.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('reports*') ? 'active' : '' }}" href="{{ route('reports.index') }}">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        @endcan

                        <hr class="my-3">

                        @can('users.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('users*') ? 'active' : '' }}" href="{{ route('users.index') }}">
                                <i class="fas fa-users-cog me-2"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        @endcan

                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('profile.edit') }}">
                                <i class="fas fa-user-edit me-2"></i>
                                الملف الشخصي
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('test-permissions') }}">
                                <i class="fas fa-shield-alt me-2"></i>
                                اختبار الصلاحيات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">@yield('page-title', 'لوحة التحكم')</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        @yield('page-actions')
                    </div>
                </div>

                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <div class="main-content p-4">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Logout Form -->
    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
        @csrf
    </form>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    @stack('scripts')
</body>
</html>
