<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inspections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained('ships')->onDelete('cascade'); // السفينة
            $table->enum('inspection_type', ['customs', 'health', 'security', 'technical']); // نوع التفتيش
            $table->string('inspector_name'); // اسم المفتش
            $table->string('inspector_authority'); // الجهة المفتشة
            $table->datetime('inspection_date'); // تاريخ التفتيش
            $table->enum('result', ['approved', 'rejected', 'conditional']); // نتيجة التفتيش
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('violations')->nullable(); // المخالفات
            $table->text('conditions')->nullable(); // الشروط (في حالة الموافقة المشروطة)
            $table->datetime('valid_until')->nullable(); // صالح حتى
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inspections');
    }
};
