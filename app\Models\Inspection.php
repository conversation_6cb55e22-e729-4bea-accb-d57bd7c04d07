<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Inspection extends Model
{
    protected $fillable = [
        'ship_id',
        'inspection_type',
        'inspector_name',
        'inspector_authority',
        'inspection_date',
        'result',
        'notes',
        'violations',
        'conditions',
        'valid_until'
    ];

    protected $casts = [
        'inspection_date' => 'datetime',
        'valid_until' => 'datetime'
    ];

    /**
     * العلاقة مع السفينة
     */
    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    /**
     * الحصول على نوع التفتيش بالعربية
     */
    public function getInspectionTypeLabelAttribute(): string
    {
        return match($this->inspection_type) {
            'customs' => 'جمركي',
            'health' => 'صحي',
            'security' => 'أمني',
            'technical' => 'فني',
            default => $this->inspection_type
        };
    }

    /**
     * الحصول على نتيجة التفتيش بالعربية
     */
    public function getResultLabelAttribute(): string
    {
        return match($this->result) {
            'approved' => 'موافق',
            'rejected' => 'مرفوض',
            'conditional' => 'موافق مشروط',
            default => $this->result
        };
    }

    /**
     * الحصول على لون النتيجة
     */
    public function getResultColorAttribute(): string
    {
        return match($this->result) {
            'approved' => 'success',
            'rejected' => 'danger',
            'conditional' => 'warning',
            default => 'secondary'
        };
    }

    /**
     * التحقق من صلاحية التفتيش
     */
    public function getIsValidAttribute(): bool
    {
        if (!$this->valid_until) {
            return true; // إذا لم يكن هناك تاريخ انتهاء
        }

        return $this->valid_until->isFuture();
    }

    /**
     * الحصول على أيقونة نوع التفتيش
     */
    public function getInspectionIconAttribute(): string
    {
        return match($this->inspection_type) {
            'customs' => 'fas fa-file-invoice',
            'health' => 'fas fa-heartbeat',
            'security' => 'fas fa-shield-alt',
            'technical' => 'fas fa-tools',
            default => 'fas fa-search'
        };
    }

    /**
     * فلترة التفتيشات المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('result', 'approved');
    }

    /**
     * فلترة التفتيشات المرفوضة
     */
    public function scopeRejected($query)
    {
        return $query->where('result', 'rejected');
    }

    /**
     * فلترة التفتيشات المشروطة
     */
    public function scopeConditional($query)
    {
        return $query->where('result', 'conditional');
    }

    /**
     * فلترة التفتيشات الصالحة
     */
    public function scopeValid($query)
    {
        return $query->where(function($q) {
            $q->whereNull('valid_until')
              ->orWhere('valid_until', '>', Carbon::now());
        });
    }

    /**
     * فلترة حسب نوع التفتيش
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('inspection_type', $type);
    }
}
