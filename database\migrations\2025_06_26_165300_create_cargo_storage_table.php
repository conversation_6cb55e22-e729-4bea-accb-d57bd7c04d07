<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cargo_storage', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cargo_id')->constrained('cargos')->onDelete('cascade'); // البضاعة
            $table->foreignId('warehouse_id')->constrained('warehouses'); // المستودع
            $table->decimal('stored_quantity', 12, 2); // الكمية المخزنة
            $table->datetime('storage_start'); // بداية التخزين
            $table->datetime('storage_end')->nullable(); // نهاية التخزين
            $table->integer('storage_days')->default(0); // عدد أيام التخزين
            $table->decimal('daily_rate', 8, 2); // التعريفة اليومية
            $table->decimal('total_storage_cost', 10, 2)->default(0); // إجمالي تكلفة التخزين
            $table->enum('status', ['active', 'completed'])->default('active'); // حالة التخزين
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cargo_storage');
    }
};
