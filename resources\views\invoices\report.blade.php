@extends('layouts.port-app')

@section('title', 'تقرير الفواتير المتقدم')

@section('content')
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="text-primary">
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    تقرير الفواتير المتقدم
                </h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-2"></i>
                        تصدير Excel
                    </button>
                    <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf me-2"></i>
                        تصدير PDF
                    </button>
                    <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للفواتير
                    </a>
                </div>
            </div>
            <p class="text-muted">
                الفترة: {{ $dateFrom->format('Y/m/d') }} - {{ $dateTo->format('Y/m/d') }}
                ({{ $dateFrom->diffInDays($dateTo) + 1 }} يوم)
            </p>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر التقرير
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('invoices.report') }}">
                <div class="row">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" 
                               value="{{ request('date_from', $dateFrom->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" 
                               value="{{ request('date_to', $dateTo->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>مسودة</option>
                            <option value="sent" {{ request('status') == 'sent' ? 'selected' : '' }}>مرسلة</option>
                            <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>مدفوعة</option>
                            <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>متأخرة</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="agent_id" class="form-label">الوكيل الملاحي</label>
                        <select name="agent_id" id="agent_id" class="form-select">
                            <option value="">جميع الوكلاء</option>
                            @foreach($agents as $agent)
                            <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                {{ $agent->name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            تطبيق الفلاتر
                        </button>
                        <a href="{{ route('invoices.report') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['total_invoices'] }}</h3>
                    <p class="mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['total_amount'], 0) }}</h3>
                    <p class="mb-0">إجمالي المبلغ (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['paid_amount'], 0) }}</h3>
                    <p class="mb-0">المبلغ المحصل (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($summary['remaining_amount'], 0) }}</h3>
                    <p class="mb-0">المبلغ المتبقي (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ $summary['by_status']['overdue'] }}</h3>
                    <p class="mb-0">فواتير متأخرة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format(($summary['paid_amount'] / max($summary['total_amount'], 1)) * 100, 1) }}%</h3>
                    <p class="mb-0">معدل التحصيل</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل الفواتير حسب الحالة -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع الفواتير حسب الحالة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الحالة</th>
                                    <th>العدد</th>
                                    <th>النسبة</th>
                                    <th>المبلغ (ل.س)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-secondary">مسودة</span></td>
                                    <td>{{ $summary['by_status']['draft'] }}</td>
                                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['draft'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                                    <td>{{ number_format($invoices->where('status', 'draft')->sum('total_amount'), 0) }}</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">مرسلة</span></td>
                                    <td>{{ $summary['by_status']['sent'] }}</td>
                                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['sent'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                                    <td>{{ number_format($invoices->where('status', 'sent')->sum('total_amount'), 0) }}</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">مدفوعة</span></td>
                                    <td>{{ $summary['by_status']['paid'] }}</td>
                                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['paid'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                                    <td>{{ number_format($invoices->where('status', 'paid')->sum('total_amount'), 0) }}</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger">متأخرة</span></td>
                                    <td>{{ $summary['by_status']['overdue'] }}</td>
                                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['overdue'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                                    <td>{{ number_format($invoices->where('status', 'overdue')->sum('total_amount'), 0) }}</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-dark">ملغية</span></td>
                                    <td>{{ $summary['by_status']['cancelled'] }}</td>
                                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['cancelled'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                                    <td>{{ number_format($invoices->where('status', 'cancelled')->sum('total_amount'), 0) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        أفضل 5 وكلاء من حيث الإيرادات
                    </h5>
                </div>
                <div class="card-body">
                    @php
                        $agentRevenues = $invoices->groupBy('agent.name')->map(function($group) {
                            return [
                                'name' => $group->first()->agent->name,
                                'company' => $group->first()->agent->company_name,
                                'total' => $group->sum('total_amount'),
                                'count' => $group->count()
                            ];
                        })->sortByDesc('total')->take(5);
                    @endphp
                    
                    @foreach($agentRevenues as $agent)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong>{{ $agent['name'] }}</strong>
                            <br>
                            <small class="text-muted">{{ $agent['company'] }}</small>
                            <br>
                            <small class="text-info">{{ $agent['count'] }} فاتورة</small>
                        </div>
                        <div class="text-end">
                            <strong class="text-success">{{ number_format($agent['total'], 0) }} ل.س</strong>
                        </div>
                    </div>
                    @if(!$loop->last)
                        <hr>
                    @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الفواتير التفصيلية -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الفواتير التفصيلية ({{ $invoices->count() }})
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="invoicesTable">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>السفينة</th>
                            <th>الوكيل الملاحي</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المبلغ المتبقي</th>
                            <th>الحالة</th>
                            <th>أيام التأخير</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($invoices as $invoice)
                        <tr>
                            <td>
                                <a href="{{ route('invoices.show', $invoice) }}" class="text-decoration-none">
                                    <strong>{{ $invoice->invoice_number }}</strong>
                                </a>
                            </td>
                            <td>{{ $invoice->invoice_date->format('Y/m/d') }}</td>
                            <td>
                                <a href="{{ route('ships.show', $invoice->ship) }}" class="text-decoration-none">
                                    {{ $invoice->ship->name }}
                                </a>
                                <br>
                                <small class="text-muted">{{ $invoice->ship->ship_type }}</small>
                            </td>
                            <td>
                                <a href="{{ route('agents.show', $invoice->agent) }}" class="text-decoration-none">
                                    {{ $invoice->agent->name }}
                                </a>
                                <br>
                                <small class="text-muted">{{ $invoice->agent->company_name }}</small>
                            </td>
                            <td>
                                <strong>{{ number_format($invoice->total_amount, 2) }} ل.س</strong>
                            </td>
                            <td>
                                {{ number_format($invoice->paid_amount, 2) }} ل.س
                            </td>
                            <td>
                                @if($invoice->remaining_amount > 0)
                                    <span class="text-danger">{{ number_format($invoice->remaining_amount, 2) }} ل.س</span>
                                @else
                                    <span class="text-success">0.00 ل.س</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $invoice->status_color }}">
                                    {{ $invoice->status_label }}
                                </span>
                            </td>
                            <td>
                                @if($invoice->due_date && $invoice->due_date->isPast() && !$invoice->is_fully_paid)
                                    <span class="badge bg-danger">
                                        {{ $invoice->due_date->diffInDays(now()) }} يوم
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- تحليل الفواتير المتأخرة -->
    @if($summary['by_status']['overdue'] > 0)
    <div class="card mt-4">
        <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تحليل الفواتير المتأخرة
            </h5>
        </div>
        <div class="card-body">
            @php
                $overdueInvoices = $invoices->filter(function($invoice) {
                    return $invoice->due_date && $invoice->due_date->isPast() && !$invoice->is_fully_paid;
                });
                
                $overdueByPeriod = [
                    '1-30' => $overdueInvoices->filter(function($invoice) {
                        return $invoice->due_date->diffInDays(now()) <= 30;
                    })->count(),
                    '31-60' => $overdueInvoices->filter(function($invoice) {
                        $days = $invoice->due_date->diffInDays(now());
                        return $days > 30 && $days <= 60;
                    })->count(),
                    '61-90' => $overdueInvoices->filter(function($invoice) {
                        $days = $invoice->due_date->diffInDays(now());
                        return $days > 60 && $days <= 90;
                    })->count(),
                    '90+' => $overdueInvoices->filter(function($invoice) {
                        return $invoice->due_date->diffInDays(now()) > 90;
                    })->count(),
                ];
            @endphp
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">{{ $overdueByPeriod['1-30'] }}</h4>
                        <p class="mb-0">1-30 يوم</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">{{ $overdueByPeriod['31-60'] }}</h4>
                        <p class="mb-0">31-60 يوم</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-danger">{{ $overdueByPeriod['61-90'] }}</h4>
                        <p class="mb-0">61-90 يوم</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-danger">{{ $overdueByPeriod['90+'] }}</h4>
                        <p class="mb-0">أكثر من 90 يوم</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
function exportToExcel() {
    // تصدير إلى Excel
    window.location.href = '{{ route("invoices.report") }}?' + new URLSearchParams(window.location.search) + '&format=excel';
}

function exportToPDF() {
    // تصدير إلى PDF
    window.location.href = '{{ route("invoices.report") }}?' + new URLSearchParams(window.location.search) + '&format=pdf';
}

// تحسين الجدول
$(document).ready(function() {
    $('#invoicesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "pageLength": 25,
        "order": [[ 1, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": [8] }
        ]
    });
});
</script>
@endpush
@endsection