@extends('layouts.port-app')

@section('page-title', 'سجل تسجيل الدخول - ' . $user->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        سجل تسجيل الدخول للمستخدم: {{ $user->name }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        ميزة سجل تسجيل الدخول قيد التطوير. سيتم إضافة هذه الميزة قريباً.
                    </div>
                    
                    <div class="text-center py-4">
                        <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد سجلات تسجيل دخول حالياً</h5>
                        <p class="text-muted">سيتم عرض سجل تسجيل الدخول هنا عند تفعيل الميزة</p>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('users.show', $user) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            رجوع لملف المستخدم
                        </a>
                        <a href="{{ route('users.index') }}" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            قائمة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 