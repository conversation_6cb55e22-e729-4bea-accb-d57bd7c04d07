@extends('layouts.port-app')

@section('page-title', 'إدارة التفتيش')

@section('page-actions')
<div class="btn-group">
    @can('inspections.create')
    <a href="{{ route('inspections.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة تفتيش جديد
    </a>
    @endcan
    <a href="{{ route('inspections.export.excel') }}" class="btn btn-outline-success">
        <i class="fas fa-file-excel me-2"></i>
        تصدير Excel
    </a>
    <a href="{{ route('inspections.report') }}" class="btn btn-outline-info">
        <i class="fas fa-chart-bar me-2"></i>
        تقرير التفتيشات
    </a>
    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filterModal">
        <i class="fas fa-filter me-2"></i>
        تصفية متقدمة
    </button>
</div>
@endsection

@section('content')
<!-- إحصائيات سريعة محسنة -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">إجمالي التفتيشات</h6>
                        <h3 class="mb-0">{{ $stats['total'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-search fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">معتمد</h6>
                        <h3 class="mb-0">{{ $stats['approved'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مرفوض</h6>
                        <h3 class="mb-0">{{ $stats['rejected'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">مشروط</h6>
                        <h3 class="mb-0">{{ $stats['conditional'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">قيد المراجعة</h6>
                        <h3 class="mb-0">{{ $stats['pending'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card bg-dark text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">هذا الشهر</h6>
                        <h3 class="mb-0">{{ $stats['this_month'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات حسب النوع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">تفتيش السلامة</h6>
                        <h3 class="mb-0">{{ $stats['safety_inspections'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shield-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">تفتيش الأمن</h6>
                        <h3 class="mb-0">{{ $stats['security_inspections'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-lock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">تفتيش جمركي</h6>
                        <h3 class="mb-0">{{ $stats['customs_inspections'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clipboard-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">تفتيش صحي</h6>
                        <h3 class="mb-0">{{ $stats['health_inspections'] }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-heartbeat fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- شريط البحث والفلترة -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('inspections.index') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في اسم السفينة أو المفتش..." 
                               value="{{ request('search') }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="inspection_type" onchange="this.form.submit()">
                        <option value="">جميع الأنواع</option>
                        <option value="safety" {{ request('inspection_type') == 'safety' ? 'selected' : '' }}>السلامة</option>
                        <option value="security" {{ request('inspection_type') == 'security' ? 'selected' : '' }}>الأمن</option>
                        <option value="customs" {{ request('inspection_type') == 'customs' ? 'selected' : '' }}>جمركي</option>
                        <option value="health" {{ request('inspection_type') == 'health' ? 'selected' : '' }}>صحي</option>
                        <option value="environmental" {{ request('inspection_type') == 'environmental' ? 'selected' : '' }}>بيئي</option>
                        <option value="technical" {{ request('inspection_type') == 'technical' ? 'selected' : '' }}>فني</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="result" onchange="this.form.submit()">
                        <option value="">جميع النتائج</option>
                        <option value="pending" {{ request('result') == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                        <option value="approved" {{ request('result') == 'approved' ? 'selected' : '' }}>معتمد</option>
                        <option value="rejected" {{ request('result') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                        <option value="conditional" {{ request('result') == 'conditional' ? 'selected' : '' }}>مشروط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_from" 
                           value="{{ request('date_from') }}" 
                           onchange="this.form.submit()" 
                           placeholder="من تاريخ">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_to" 
                           value="{{ request('date_to') }}" 
                           onchange="this.form.submit()" 
                           placeholder="إلى تاريخ">
                </div>
                <div class="col-md-1">
                    <a href="{{ route('inspections.index') }}" class="btn btn-outline-secondary w-100" title="إعادة تعيين">
                        <i class="fas fa-redo"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول التفتيشات -->
<div class="card">
    @if($inspections->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>السفينة</th>
                        <th>نوع التفتيش</th>
                        <th>تاريخ التفتيش</th>
                        <th>المفتش</th>
                        <th>الجهة المفتشة</th>
                        <th>النتيجة</th>
                        <th>الملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($inspections as $inspection)
                    <tr>
                        <td>
                            <a href="{{ route('ships.show', $inspection->ship) }}" class="text-decoration-none">
                                {{ $inspection->ship->name }}
                            </a>
                            <br>
                            <small class="text-muted">{{ $inspection->ship->agent->name ?? 'بدون وكيل' }}</small>
                        </td>
                        <td>
                            @switch($inspection->inspection_type)
                                @case('safety')
                                    <span class="badge bg-primary">السلامة</span>
                                    @break
                                @case('security')
                                    <span class="badge bg-success">الأمن</span>
                                    @break
                                @case('customs')
                                    <span class="badge bg-warning">جمركي</span>
                                    @break
                                @case('health')
                                    <span class="badge bg-info">صحي</span>
                                    @break
                                @case('environmental')
                                    <span class="badge bg-secondary">بيئي</span>
                                    @break
                                @case('technical')
                                    <span class="badge bg-dark">فني</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $inspection->inspection_type }}</span>
                            @endswitch
                        </td>
                        <td>
                            {{ $inspection->inspection_date->format('Y/m/d') }}
                            <br>
                            <small class="text-muted">{{ $inspection->inspection_date->format('H:i') }}</small>
                        </td>
                        <td>
                            <strong>{{ $inspection->inspector_name }}</strong>
                            @if($inspection->inspector_license)
                                <br><small class="text-muted">رخصة: {{ $inspection->inspector_license }}</small>
                            @endif
                        </td>
                        <td>{{ $inspection->inspector_authority }}</td>
                        <td>
                            @switch($inspection->result)
                                @case('pending')
                                    <span class="badge bg-warning">قيد المراجعة</span>
                                    @break
                                @case('approved')
                                    <span class="badge bg-success">معتمد</span>
                                    @break
                                @case('rejected')
                                    <span class="badge bg-danger">مرفوض</span>
                                    @break
                                @case('conditional')
                                    <span class="badge bg-info">مشروط</span>
                                    @break
                                @default
                                    <span class="badge bg-light text-dark">{{ $inspection->result }}</span>
                            @endswitch
                        </td>
                        <td>
                            @if($inspection->notes)
                                {{ Str::limit($inspection->notes, 50) }}
                            @else
                                <span class="text-muted">لا توجد ملاحظات</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                @can('inspections.view')
                                <a href="{{ route('inspections.show', $inspection) }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @endcan
                                @can('inspections.edit')
                                <a href="{{ route('inspections.edit', $inspection) }}" class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @endcan
                                @if($inspection->result === 'pending')
                                    @can('inspections.edit')
                                    <button type="button" class="btn btn-outline-success" title="تحديث النتيجة" onclick="updateResult({{ $inspection->id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    @endcan
                                @endif
                                @can('inspections.delete')
                                <button type="button" class="btn btn-outline-danger" title="حذف" onclick="confirmDelete({{ $inspection->id }}, '{{ $inspection->ship->name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer">
            {{ $inspections->appends(request()->query())->links() }}
        </div>
    @else
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تفتيشات</h5>
            <p class="text-muted">لم يتم العثور على أي تفتيشات مطابقة للبحث</p>
            @can('inspections.create')
            <a href="{{ route('inspections.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة تفتيش جديد
            </a>
            @endcan
        </div>
    @endif
</div>

<!-- مودال تحديث النتيجة -->
<div class="modal fade" id="updateResultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث نتيجة التفتيش</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="updateResultForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="result" class="form-label">النتيجة</label>
                        <select class="form-select" id="result" name="result" required>
                            <option value="approved">معتمد</option>
                            <option value="rejected">مرفوض</option>
                            <option value="conditional">مشروط</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="result_notes" class="form-label">ملاحظات النتيجة</label>
                        <textarea class="form-control" id="result_notes" name="result_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث النتيجة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف تفتيش السفينة: <strong id="shipName"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateResult(inspectionId) {
    document.getElementById('updateResultForm').action = `/inspections/${inspectionId}/update-result`;
    new bootstrap.Modal(document.getElementById('updateResultModal')).show();
}

function confirmDelete(inspectionId, shipName) {
    document.getElementById('shipName').textContent = shipName;
    document.getElementById('deleteForm').action = `/inspections/${inspectionId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
