<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Warehouse;
use App\Models\CargoStorage;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;

class WarehouseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Gate::authorize('warehouses.view');

        $query = Warehouse::with(['cargoStorages.cargo.ship']);

        // فلترة حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب التغطية
        if ($request->filled('is_covered')) {
            $query->where('is_covered', $request->is_covered === '1');
        }

        // البحث في الاسم أو الرمز
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('code', 'like', '%' . $request->search . '%')
                  ->orWhere('location', 'like', '%' . $request->search . '%');
            });
        }

        $warehouses = $query->orderBy('name')->paginate(12);

        // حساب الإحصائيات الشاملة المحسنة
        $stats = [
            'total_warehouses' => Warehouse::count(),
            'total_capacity' => Warehouse::sum('max_capacity'),
            'total_used_space' => CargoStorage::sum('stored_quantity'),
            'total_available_space' => Warehouse::sum('max_capacity') - CargoStorage::sum('stored_quantity'),
            
            // حسب الحالة
            'active' => Warehouse::where('status', 'active')->count(),
            'maintenance' => Warehouse::where('status', 'maintenance')->count(),
            'inactive' => Warehouse::where('status', 'inactive')->count(),
            
            // حسب النوع
            'general_warehouses' => Warehouse::where('type', 'general')->count(),
            'cold_storage' => Warehouse::where('type', 'cold_storage')->count(),
            'dangerous_goods' => Warehouse::where('type', 'dangerous_goods')->count(),
            'container_yard' => Warehouse::where('type', 'container_yard')->count(),
            'open_storage' => Warehouse::where('type', 'open_storage')->count(),
            
            // حسب التغطية
            'covered' => Warehouse::where('is_covered', true)->count(),
            'uncovered' => Warehouse::where('is_covered', false)->count(),
            
            // معدلات الإشغال
            'avg_occupancy_rate' => $this->calculateAverageOccupancyRate(),
            'high_occupancy' => $this->getHighOccupancyWarehouses()->count(),
            'low_occupancy' => $this->getLowOccupancyWarehouses()->count(),
            
            // إحصائيات مالية
            'total_storage_value' => CargoStorage::with('cargo')->get()->sum(function($storage) {
                return $storage->cargo->value ?? 0;
            }),
            'monthly_revenue' => $this->calculateMonthlyRevenue(),
            'total_area' => Warehouse::sum('total_area'),
            'available_area' => Warehouse::sum('available_area'),
            'occupancy_rate' => Warehouse::sum('total_area') > 0 ? ( (Warehouse::sum('total_area') - Warehouse::sum('available_area')) / Warehouse::sum('total_area') ) * 100 : 0,
        ];

        return view('warehouses.index', compact('warehouses', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        Gate::authorize('warehouses.create');

        return view('warehouses.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Gate::authorize('warehouses.create');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:warehouses,code',
            'type' => 'required|in:warehouse,yard,cold_storage,tank,silo',
            'total_area' => 'required|numeric|min:0',
            'available_area' => 'required|numeric|min:0|lte:total_area',
            'max_capacity' => 'nullable|numeric|min:0',
            'is_covered' => 'boolean',
            'daily_rate' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,maintenance,full',
            'description' => 'nullable|string|max:1000',
            'location' => 'nullable|string|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20'
        ]);

        $validated['is_covered'] = $request->has('is_covered');
        $validated['current_capacity'] = 0;

        Warehouse::create($validated);

        return redirect()->route('warehouses.index')
            ->with('success', 'تم إضافة المستودع بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Warehouse $warehouse)
    {
        Gate::authorize('warehouses.view');

        $warehouse->load(['cargoStorages.cargo.ship.agent']);

        // الحمولات المخزنة حالياً
        $currentCargos = $warehouse->currentCargos()->with(['cargo.ship.agent'])->get();

        // سجل الحمولات (آخر 30 يوم)
        $recentActivity = $warehouse->cargoStorages()
            ->with(['cargo.ship.agent'])
            ->where('created_at', '>=', now()->subDays(30))
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('warehouses.show', compact('warehouse', 'currentCargos', 'recentActivity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Warehouse $warehouse)
    {
        Gate::authorize('warehouses.edit');

        return view('warehouses.edit', compact('warehouse'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Warehouse $warehouse)
    {
        Gate::authorize('warehouses.edit');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:warehouses,code,' . $warehouse->id,
            'type' => 'required|in:warehouse,yard,cold_storage,tank,silo',
            'total_area' => 'required|numeric|min:0',
            'available_area' => 'required|numeric|min:0|lte:total_area',
            'max_capacity' => 'nullable|numeric|min:0',
            'is_covered' => 'nullable|boolean',
            'daily_rate' => 'nullable|numeric|min:0',
            'status' => 'required|in:active,inactive,maintenance,full',
            'description' => 'nullable|string|max:1000',
            'location' => 'nullable|string|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20'
        ]);

        $validated['is_covered'] = $request->has('is_covered');

        $warehouse->update($validated);

        return redirect()->route('warehouses.show', $warehouse)
            ->with('success', 'تم تحديث المستودع بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Warehouse $warehouse)
    {
        Gate::authorize('warehouses.delete');

        // التحقق من وجود أي سجلات تخزين مرتبطة
        if ($warehouse->cargoStorages()->count() > 0) {
            return redirect()->route('warehouses.index')
                ->with('error', 'لا يمكن حذف المستودع لوجود سجلات تخزين مرتبطة به (حمولات مخزنة حالياً أو سابقاً).');
        }

        $warehouse->delete();

        return redirect()->route('warehouses.index')
            ->with('success', 'تم حذف المستودع بنجاح');
    }

    /**
     * تحديث حالة المستودع
     */
    public function updateStatus(Request $request, Warehouse $warehouse)
    {
        Gate::authorize('warehouses.edit');

        $validated = $request->validate([
            'status' => 'required|in:active,inactive,maintenance,full',
            'notes' => 'nullable|string|max:500'
        ]);

        $warehouse->update([
            'status' => $validated['status']
        ]);

        return redirect()->back()
            ->with('success', 'تم تحديث حالة المستودع بنجاح');
    }

    /**
     * عرض تقرير استغلال المستودعات
     */
    public function occupancyReport()
    {
        Gate::authorize('warehouses.view');

        $warehouses = Warehouse::with(['cargoStorages' => function($query) {
            $query->whereNull('storage_end');
        }])->get();

        return view('warehouses.occupancy-report', compact('warehouses'));
    }

    /**
     * Export warehouses to Excel
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('warehouses.view');

        $query = Warehouse::with(['cargoStorages.cargo.ship']);

        // تطبيق نفس الفلاتر المستخدمة في الفهرس
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('is_covered')) {
            $query->where('is_covered', $request->is_covered === '1');
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('code', 'like', '%' . $request->search . '%')
                  ->orWhere('location', 'like', '%' . $request->search . '%');
            });
        }

        $warehouses = $query->orderBy('name')->get();

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\WarehousesExport($warehouses), 
            'warehouses-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Get warehouse capacity details
     */
    public function getCapacityDetails(Warehouse $warehouse)
    {
        Gate::authorize('warehouses.view');

        $currentStorages = $warehouse->cargoStorages()
            ->whereNull('storage_end')
            ->with(['cargo.ship'])
            ->get();

        $capacityData = [
            'total_area' => $warehouse->total_area,
            'available_area' => $warehouse->available_area,
            'used_area' => $warehouse->total_area - $warehouse->available_area,
            'occupancy_rate' => $warehouse->total_area > 0 ? 
                (($warehouse->total_area - $warehouse->available_area) / $warehouse->total_area) * 100 : 0,
            'current_storages' => $currentStorages,
            'storage_count' => $currentStorages->count(),
            'total_weight' => $currentStorages->sum('cargo.weight') ?? 0
        ];

        return response()->json($capacityData);
    }

    /**
     * Update warehouse capacity
     */
    public function updateCapacity(Request $request, Warehouse $warehouse)
    {
        Gate::authorize('warehouses.edit');

        $validated = $request->validate([
            'available_area' => 'required|numeric|min:0|lte:' . $warehouse->total_area,
            'current_capacity' => 'nullable|numeric|min:0'
        ]);

        $warehouse->update($validated);

        return redirect()->back()
            ->with('success', 'تم تحديث سعة المستودع بنجاح');
    }

    /**
     * Bulk actions for warehouses
     */
    public function bulkActions(Request $request)
    {
        Gate::authorize('warehouses.edit');

        $validated = $request->validate([
            'action' => 'required|in:update_status,delete',
            'warehouse_ids' => 'required|array',
            'warehouse_ids.*' => 'exists:warehouses,id',
            'status' => 'required_if:action,update_status|in:active,inactive,maintenance,full'
        ]);

        $warehouses = Warehouse::whereIn('id', $validated['warehouse_ids']);

        switch ($validated['action']) {
            case 'update_status':
                $warehouses->update(['status' => $validated['status']]);
                return redirect()->back()
                    ->with('success', 'تم تحديث حالة المستودعات المحددة بنجاح');
                break;

            case 'delete':
                if (!Gate::allows('warehouses.delete')) {
                    abort(403);
                }
                
                // التحقق من وجود سجلات تخزين مرتبطة
                $warehousesWithStorages = $warehouses->whereHas('cargoStorages')->count();
                if ($warehousesWithStorages > 0) {
                    return redirect()->back()
                        ->with('error', 'لا يمكن حذف بعض المستودعات لوجود سجلات تخزين مرتبطة بها');
                }

                $warehouses->delete();
                return redirect()->back()
                    ->with('success', 'تم حذف المستودعات المحددة بنجاح');
                break;
        }

        return redirect()->back();
    }

    /**
     * Calculate average occupancy rate
     */
    private function calculateAverageOccupancyRate()
    {
        $warehouses = Warehouse::where('total_area', '>', 0)->get();
        
        if ($warehouses->isEmpty()) {
            return 0;
        }

        $totalOccupancyRate = $warehouses->sum(function ($warehouse) {
            $usedArea = $warehouse->total_area - $warehouse->available_area;
            return ($usedArea / $warehouse->total_area) * 100;
        });

        return round($totalOccupancyRate / $warehouses->count(), 2);
    }

    /**
     * Get warehouses with high occupancy (>80%)
     */
    private function getHighOccupancyWarehouses()
    {
        return Warehouse::where('total_area', '>', 0)
            ->get()
            ->filter(function ($warehouse) {
                $usedArea = $warehouse->total_area - $warehouse->available_area;
                $occupancyRate = ($usedArea / $warehouse->total_area) * 100;
                return $occupancyRate > 80;
            });
    }

    /**
     * Get warehouses with low occupancy (<30%)
     */
    private function getLowOccupancyWarehouses()
    {
        return Warehouse::where('total_area', '>', 0)
            ->get()
            ->filter(function ($warehouse) {
                $usedArea = $warehouse->total_area - $warehouse->available_area;
                $occupancyRate = ($usedArea / $warehouse->total_area) * 100;
                return $occupancyRate < 30;
            });
    }

    /**
     * Calculate monthly revenue from storage fees
     */
    private function calculateMonthlyRevenue()
    {
        // هذا مثال بسيط - يمكن تطويره حسب نظام التسعير
        $currentMonth = now()->startOfMonth();
        $nextMonth = now()->addMonth()->startOfMonth();

        return CargoStorage::whereBetween('storage_start', [$currentMonth, $nextMonth])
            ->with('cargo')
            ->get()
            ->sum(function ($storage) {
                // حساب الرسوم بناءً على المدة والوزن (مثال)
                $days = $storage->storage_start->diffInDays($storage->storage_end ?? now());
                $weight = $storage->cargo->weight ?? 0;
                $dailyRate = 100; // ل.س لكل طن يومياً
                
                return $days * $weight * $dailyRate;
            });
    }

    /**
     * Advanced warehouse report
     */
    public function advancedReport(Request $request)
    {
        Gate::authorize('warehouses.view');

        $dateFrom = $request->input('date_from', now()->startOfMonth());
        $dateTo = $request->input('date_to', now()->endOfMonth());

        // إحصائيات شاملة
        $stats = [
            'total_warehouses' => Warehouse::count(),
            'total_capacity' => Warehouse::sum('max_capacity'),
            'total_used_space' => CargoStorage::sum('stored_quantity'),
            'avg_occupancy_rate' => $this->calculateAverageOccupancyRate(),
            'total_revenue' => $this->calculateMonthlyRevenue(),
        ];

        // إحصائيات حسب النوع
        $typeStats = Warehouse::selectRaw('type, COUNT(*) as count, SUM(max_capacity) as total_capacity')
            ->groupBy('type')
            ->orderByDesc('count')
            ->get();

        // إحصائيات حسب الحالة
        $statusStats = Warehouse::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->orderByDesc('count')
            ->get();

        // أفضل المستودعات من حيث الاستخدام
        $topWarehouses = Warehouse::with(['cargoStorages' => function($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('storage_start', [$dateFrom, $dateTo]);
            }])
            ->get()
            ->map(function ($warehouse) {
                $warehouse->usage_count = $warehouse->cargoStorages->count();
                $warehouse->total_weight = $warehouse->cargoStorages->sum('cargo.weight') ?? 0;
                return $warehouse;
            })
            ->sortByDesc('usage_count')
            ->take(10);

        return view('warehouses.advanced-report', compact(
            'stats', 'typeStats', 'statusStats', 'topWarehouses', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * Quick stats for dashboard
     */
    public function quickStats()
    {
        Gate::authorize('warehouses.view');

        $stats = [
            'total' => Warehouse::count(),
            'active' => Warehouse::where('status', 'active')->count(),
            'maintenance' => Warehouse::where('status', 'maintenance')->count(),
            'occupancy_rate' => $this->calculateAverageOccupancyRate(),
            'high_occupancy' => $this->getHighOccupancyWarehouses()->count(),
            'total_capacity' => Warehouse::sum('max_capacity'),
            'used_capacity' => CargoStorage::sum('stored_quantity'),
        ];

        return response()->json($stats);
    }
}
