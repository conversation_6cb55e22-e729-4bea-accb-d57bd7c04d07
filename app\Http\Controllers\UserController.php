<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:users.view')->only(['index', 'show', 'activityLog', 'loginHistory']);
        $this->middleware('permission:users.create')->only(['create', 'store']);
        $this->middleware('permission:users.edit')->only(['edit', 'update']);
        $this->middleware('permission:users.delete')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::with(['roles']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        // فلتر الدور
        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // فلتر الحالة
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'suspended':
                    $query->where('is_suspended', true);
                    break;
            }
        }

        // فلتر تاريخ الإنشاء
        if ($request->filled('created_from')) {
            $query->whereDate('created_at', '>=', $request->created_from);
        }

        if ($request->filled('created_to')) {
            $query->whereDate('created_at', '<=', $request->created_to);
        }

        $users = $query->latest()->paginate(25);

        // الإحصائيات
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'inactive_users' => User::where('is_active', false)->count(),
            'online_users' => User::where('last_login_at', '>=', now()->subMinutes(15))->count(),
        ];

        // الأدوار للفلتر
        $roles = Role::all();

        return view('users.index', compact('users', 'stats', 'roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();
        return view('users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'username' => ['nullable', 'string', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'phone' => ['nullable', 'string', 'max:20'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'department' => ['nullable', 'string', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
            'hire_date' => ['nullable', 'date'],
            'avatar' => ['nullable', 'image', 'max:2048'],
            'roles' => ['required', 'array', 'min:1'],
            'roles.*' => ['exists:roles,id'],
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username ?: $this->generateUsername($request->name),
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'bio' => $request->bio,
            'department' => $request->department,
            'position' => $request->position,
            'hire_date' => $request->hire_date,
            'is_active' => $request->boolean('is_active', true),
            'email_verified_at' => $request->boolean('email_verified') ? now() : null,
        ];

        // رفع الصورة الشخصية
        if ($request->hasFile('avatar')) {
            $userData['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        $user = User::create($userData);

        // تعيين الأدوار
        $roleNames = Role::whereIn('id', $request->roles)->pluck('name')->toArray();
        $user->syncRoles($roleNames);

        // إرسال بريد ترحيبي إذا كان مطلوباً
        if ($request->boolean('send_welcome_email')) {
            // يمكن إضافة إرسال البريد هنا
        }

        $message = 'تم إنشاء المستخدم بنجاح';
        
        if ($request->boolean('save_and_add_another')) {
            return redirect()->route('users.create')->with('success', $message . '. يمكنك إضافة مستخدم آخر.');
        }

        return redirect()->route('users.index')->with('success', $message);
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load(['roles.permissions']);

        // إحصائيات المستخدم
        $userStats = [
            'total_logins' => 0, // يمكن إضافة جدول لتتبع تسجيل الدخول
            'invoices_created' => 0, // إذا كان هناك علاقة مع الفواتير
            'ships_managed' => 0, // إذا كان هناك علاقة مع السفن
            'days_since_join' => $user->created_at->diffInDays(now()),
        ];

        // آخر النشاطات (يمكن إضافة نظام activity log)
        $recentActivities = collect(); // مؤقتاً فارغ

        return view('users.show', compact('user', 'userStats', 'recentActivities'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $user->load('roles');
        return view('users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'username' => ['nullable', 'string', 'max:255', 'unique:users,username,' . $user->id],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'phone' => ['nullable', 'string', 'max:20'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'department' => ['nullable', 'string', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
            'hire_date' => ['nullable', 'date'],
            'avatar' => ['nullable', 'image', 'max:2048'],
            'roles' => ['required', 'array', 'min:1'],
            'roles.*' => ['exists:roles,id'],
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username ?: $user->username,
            'phone' => $request->phone,
            'bio' => $request->bio,
            'department' => $request->department,
            'position' => $request->position,
            'hire_date' => $request->hire_date,
            'is_active' => $request->boolean('is_active', true),
        ];

        // تحديث كلمة المرور إذا تم إدخالها
        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
        }

        // رفع الصورة الشخصية الجديدة
        if ($request->hasFile('avatar')) {
            // حذف الصورة القديمة
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            $userData['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        $user->update($userData);

        // تحديث الأدوار
        $roleNames = Role::whereIn('id', $request->roles)->pluck('name')->toArray();
        $user->syncRoles($roleNames);

        return redirect()->route('users.index')->with('success', 'تم تحديث المستخدم بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // منع حذف المستخدم الحالي
        if ($user->id === Auth::id()) {
            return response()->json(['success' => false, 'message' => 'لا يمكن حذف حسابك الشخصي']);
        }

        // حذف الصورة الشخصية
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        $user->delete();

        return response()->json(['success' => true, 'message' => 'تم حذف المستخدم بنجاح']);
    }

    /**
     * تعليق المستخدم
     */
    public function suspend(User $user)
    {
        $user->update(['is_active' => false, 'is_suspended' => true]);
        return response()->json(['success' => true, 'message' => 'تم تعليق المستخدم بنجاح']);
    }

    /**
     * تفعيل المستخدم
     */
    public function activate(User $user)
    {
        $user->update(['is_active' => true, 'is_suspended' => false]);
        return response()->json(['success' => true, 'message' => 'تم تفعيل المستخدم بنجاح']);
    }

    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword(User $user)
    {
        // يمكن إضافة إرسال رابط إعادة تعيين كلمة المرور هنا
        return response()->json(['success' => true, 'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور']);
    }

    /**
     * إرسال بريد ترحيبي
     */
    public function sendWelcome(User $user)
    {
        // يمكن إضافة إرسال البريد الترحيبي هنا
        return response()->json(['success' => true, 'message' => 'تم إرسال البريد الترحيبي بنجاح']);
    }

    /**
     * تسجيل الدخول كمستخدم آخر
     */
    public function loginAs(User $user)
    {
        // يمكن إضافة منطق تسجيل الدخول كمستخدم آخر هنا
        return response()->json(['success' => true, 'redirect_url' => route('dashboard')]);
    }

    /**
     * سجل النشاطات
     */
    public function activityLog(User $user)
    {
        // يمكن إضافة سجل النشاطات هنا
        return view('users.activity-log', compact('user'));
    }

    /**
     * سجل تسجيل الدخول
     */
    public function loginHistory(User $user)
    {
        // يمكن إضافة سجل تسجيل الدخول هنا
        return view('users.login-history', compact('user'));
    }

    /**
     * الإجراءات المجمعة
     */
    public function bulkActions(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,suspend,send_welcome,reset_password',
            'user_ids' => 'required|json',
        ]);

        $userIds = json_decode($request->user_ids);
        $users = User::whereIn('id', $userIds)->get();

        $count = 0;
        foreach ($users as $user) {
            switch ($request->action) {
                case 'activate':
                    $user->update(['is_active' => true, 'is_suspended' => false]);
                    $count++;
                    break;
                case 'suspend':
                    $user->update(['is_active' => false, 'is_suspended' => true]);
                    $count++;
                    break;
                case 'send_welcome':
                    // إرسال بريد ترحيبي
                    $count++;
                    break;
                case 'reset_password':
                    // إعادة تعيين كلمة المرور
                    $count++;
                    break;
            }
        }

        return response()->json(['success' => true, 'message' => "تم تطبيق الإجراء على {$count} مستخدم"]);
    }

    /**
     * تصدير قائمة المستخدمين
     */
    public function export()
    {
        // يمكن إضافة تصدير Excel/CSV هنا
        return redirect()->back()->with('info', 'ميزة التصدير قيد التطوير');
    }

    /**
     * إنشاء اسم مستخدم تلقائياً
     */
    private function generateUsername($name)
    {
        $username = strtolower(str_replace(' ', '_', $name));
        $username = preg_replace('/[^a-z0-9_]/', '', $username);
        
        $originalUsername = $username;
        $counter = 1;
        
        while (User::where('username', $username)->exists()) {
            $username = $originalUsername . '_' . $counter;
            $counter++;
        }
        
        return $username;
    }
}