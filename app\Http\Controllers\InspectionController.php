<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Inspection;
use App\Models\Ship;
use Illuminate\Support\Facades\Gate;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\InspectionsExport;

class InspectionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        Gate::authorize('inspections.view');

        $query = Inspection::with(['ship.agent']);

        // فلترة حسب نوع التفتيش
        if ($request->filled('inspection_type')) {
            $query->where('inspection_type', $request->inspection_type);
        }

        // فلترة حسب النتيجة
        if ($request->filled('result')) {
            $query->where('result', $request->result);
        }

        // فلترة حسب الجهة المفتشة
        if ($request->filled('inspector_authority')) {
            $query->where('inspector_authority', 'like', '%' . $request->inspector_authority . '%');
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('inspection_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('inspection_date', '<=', $request->date_to);
        }

        // البحث في اسم السفينة أو المفتش
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('inspector_name', 'like', '%' . $request->search . '%')
                  ->orWhereHas('ship', function($shipQuery) use ($request) {
                      $shipQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $inspections = $query->orderBy('inspection_date', 'desc')->paginate(15);

        // حساب الإحصائيات المحسنة
        $stats = [
            'total' => Inspection::count(),
            'approved' => Inspection::where('result', 'approved')->count(),
            'rejected' => Inspection::where('result', 'rejected')->count(),
            'conditional' => Inspection::where('result', 'conditional')->count(),
            'pending' => Inspection::where('result', 'pending')->count(),
            'today' => Inspection::whereDate('inspection_date', Carbon::today())->count(),
            'this_week' => Inspection::whereBetween('inspection_date', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->count(),
            'this_month' => Inspection::whereMonth('inspection_date', Carbon::now()->month)
                ->whereYear('inspection_date', Carbon::now()->year)
                ->count(),
            'safety_inspections' => Inspection::where('inspection_type', 'safety')->count(),
            'security_inspections' => Inspection::where('inspection_type', 'security')->count(),
            'customs_inspections' => Inspection::where('inspection_type', 'customs')->count(),
            'health_inspections' => Inspection::where('inspection_type', 'health')->count(),
        ];

        return view('inspections.index', compact('inspections', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        Gate::authorize('inspections.create');

        // السفن المتاحة للتفتيش (في الميناء)
        $ships = Ship::whereIn('status', ['outside_port', 'berthed'])
                    ->with('agent')
                    ->orderBy('name')
                    ->get();

        return view('inspections.create', compact('ships'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Gate::authorize('inspections.create');

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id',
            'inspection_type' => 'required|in:customs,health,security,technical',
            'inspector_name' => 'required|string|max:255',
            'inspector_authority' => 'required|string|max:255',
            'inspection_date' => 'required|date',
            'result' => 'required|in:approved,rejected,conditional',
            'notes' => 'nullable|string',
            'violations' => 'nullable|string',
            'conditions' => 'nullable|string',
            'valid_until' => 'nullable|date|after:inspection_date'
        ]);

        Inspection::create($validated);

        return redirect()->route('inspections.index')
            ->with('success', 'تم إضافة التفتيش بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Inspection $inspection)
    {
        Gate::authorize('inspections.view');

        $inspection->load(['ship.agent', 'ship.cargos']);

        return view('inspections.show', compact('inspection'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Inspection $inspection)
    {
        Gate::authorize('inspections.edit');

        $ships = Ship::whereIn('status', ['outside_port', 'berthed'])
                    ->with('agent')
                    ->orderBy('name')
                    ->get();

        return view('inspections.edit', compact('inspection', 'ships'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Inspection $inspection)
    {
        Gate::authorize('inspections.edit');

        $validated = $request->validate([
            'ship_id' => 'required|exists:ships,id',
            'inspection_type' => 'required|in:customs,health,security,technical',
            'inspector_name' => 'required|string|max:255',
            'inspector_authority' => 'required|string|max:255',
            'inspection_date' => 'required|date',
            'result' => 'required|in:approved,rejected,conditional',
            'notes' => 'nullable|string',
            'violations' => 'nullable|string',
            'conditions' => 'nullable|string',
            'valid_until' => 'nullable|date|after:inspection_date'
        ]);

        $inspection->update($validated);

        return redirect()->route('inspections.show', $inspection)
            ->with('success', 'تم تحديث التفتيش بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Inspection $inspection)
    {
        Gate::authorize('inspections.delete');

        $inspection->delete();

        return redirect()->route('inspections.index')
            ->with('success', 'تم حذف التفتيش بنجاح');
    }

    /**
     * عرض تقرير التفتيشات
     */
    public function report(Request $request)
    {
        Gate::authorize('inspections.view');

        // تحديد الفترة الزمنية (افتراضياً آخر 30 يوم)
        $dateFrom = $request->date_from ? Carbon::parse($request->date_from) : Carbon::now()->subDays(30);
        $dateTo = $request->date_to ? Carbon::parse($request->date_to)->endOfDay() : Carbon::now()->endOfDay();

        // بناء استعلام التفتيشات
        $query = Inspection::with(['ship', 'ship.agent'])
            ->whereBetween('inspection_date', [$dateFrom, $dateTo]);

        // تطبيق الفلاتر
        if ($request->inspection_type) {
            $query->where('inspection_type', $request->inspection_type);
        }

        if ($request->result) {
            $query->where('result', $request->result);
        }

        $inspections = $query->orderBy('inspection_date', 'desc')->get();

        // حساب الإحصائيات
        $reportStats = [
            'total_inspections' => $inspections->count(),
            'approved' => $inspections->where('result', 'approved')->count(),
            'rejected' => $inspections->where('result', 'rejected')->count(),
            'conditional' => $inspections->where('result', 'conditional')->count(),
            'by_type' => $inspections->groupBy('inspection_type')->map->count(),
            'by_authority' => $inspections->groupBy('inspector_authority')->map->count(),
        ];

        // حساب معدل الموافقة
        $approvedAndConditional = $reportStats['approved'] + $reportStats['conditional'];
        $reportStats['approval_rate'] = $reportStats['total_inspections'] > 0
            ? ($approvedAndConditional / $reportStats['total_inspections']) * 100
            : 0;

        return view('inspections.report', compact(
            'inspections',
            'reportStats',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * تحديث نتيجة التفتيش
     */
    public function updateResult(Request $request, Inspection $inspection)
    {
        Gate::authorize('inspections.edit');

        $validated = $request->validate([
            'result' => 'required|in:approved,rejected,conditional',
            'notes' => 'nullable|string',
            'violations' => 'nullable|string',
            'conditions' => 'nullable|string',
            'valid_until' => 'nullable|date|after:today'
        ]);

        $inspection->update($validated);

        return redirect()->back()
            ->with('success', 'تم تحديث نتيجة التفتيش بنجاح');
    }

    /**
     * تصدير تقرير التفتيشات إلى Excel
     */
    public function exportExcel(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        return Excel::download(new InspectionsExport($dateFrom, $dateTo), 'تقرير-التفتيشات.xlsx');
    }
}
