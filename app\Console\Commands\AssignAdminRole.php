<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class AssignAdminRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:assign-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign admin role to the first user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = \App\Models\User::first();
        if ($user) {
            $adminRole = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
            if ($adminRole) {
                $user->assignRole($adminRole);
                $this->info("Admin role assigned to user: {$user->name}");
            } else {
                $this->error("Admin role not found");
            }
        } else {
            $this->error("No users found");
        }
    }
}
