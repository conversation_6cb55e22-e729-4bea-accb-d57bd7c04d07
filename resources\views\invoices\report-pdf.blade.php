<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الفواتير - {{ $dateFrom->format('Y/m/d') }} إلى {{ $dateTo->format('Y/m/d') }}</title>
    <link rel="stylesheet" href="{{ public_path('pdf-styles.css') }}">
    <style>
        body, * {
            font-family: 'Cairo', 'DejaVu Sans', Arial, Tahoma, sans-serif !important;
        }
        
        body {
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 20px;
        }
        
        .header h2 {
            color: #34495e;
            margin: 5px 0;
            font-size: 16px;
        }
        
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .stats-row {
            display: table-row;
        }
        
        .stat-box {
            display: table-cell;
            width: 16.66%;
            padding: 15px;
            margin: 5px;
            border: 1px solid #dee2e6;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .stat-box h3 {
            margin: 0;
            font-size: 16px;
            color: #495057;
        }
        
        .stat-box p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 9px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h3 {
            background-color: #343a40;
            color: white;
            padding: 10px;
            margin: 0 0 15px 0;
            font-size: 14px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table th,
        table td {
            border: 1px solid #dee2e6;
            padding: 6px;
            text-align: right;
        }
        
        table th {
            background-color: #f8f9fa;
            font-weight: bold;
            font-size: 9px;
        }
        
        table td {
            font-size: 9px;
        }
        
        .amount {
            text-align: left;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            color: white;
            font-size: 8px;
            font-weight: bold;
        }
        
        .status-draft { background-color: #6c757d; }
        .status-sent { background-color: #ffc107; color: #212529; }
        .status-paid { background-color: #28a745; }
        .status-overdue { background-color: #dc3545; }
        .status-cancelled { background-color: #343a40; }
        
        .footer {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
            font-size: 9px;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <h1>{{ config('app.name') }}</h1>
        <h2>تقرير الفواتير المتقدم</h2>
        <p>الفترة: {{ $dateFrom->format('Y/m/d') }} - {{ $dateTo->format('Y/m/d') }}</p>
        <p>تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="section">
        <h3>الإحصائيات العامة</h3>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stat-box">
                    <h3>{{ $summary['total_invoices'] }}</h3>
                    <p>إجمالي الفواتير</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format($summary['total_amount'], 0) }}</h3>
                    <p>إجمالي المبلغ (ل.س)</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format($summary['paid_amount'], 0) }}</h3>
                    <p>المبلغ المحصل (ل.س)</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format($summary['remaining_amount'], 0) }}</h3>
                    <p>المبلغ المتبقي (ل.س)</p>
                </div>
                <div class="stat-box">
                    <h3>{{ $summary['by_status']['overdue'] }}</h3>
                    <p>فواتير متأخرة</p>
                </div>
                <div class="stat-box">
                    <h3>{{ number_format(($summary['paid_amount'] / max($summary['total_amount'], 1)) * 100, 1) }}%</h3>
                    <p>معدل التحصيل</p>
                </div>
            </div>
        </div>
    </div>

    <!-- توزيع الفواتير حسب الحالة -->
    <div class="section">
        <h3>توزيع الفواتير حسب الحالة</h3>
        <table>
            <thead>
                <tr>
                    <th>الحالة</th>
                    <th>العدد</th>
                    <th>النسبة</th>
                    <th>المبلغ (ل.س)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>مسودة</td>
                    <td>{{ $summary['by_status']['draft'] }}</td>
                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['draft'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                    <td class="amount">{{ number_format($invoices->where('status', 'draft')->sum('total_amount'), 0) }}</td>
                </tr>
                <tr>
                    <td>مرسلة</td>
                    <td>{{ $summary['by_status']['sent'] }}</td>
                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['sent'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                    <td class="amount">{{ number_format($invoices->where('status', 'sent')->sum('total_amount'), 0) }}</td>
                </tr>
                <tr>
                    <td>مدفوعة</td>
                    <td>{{ $summary['by_status']['paid'] }}</td>
                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['paid'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                    <td class="amount">{{ number_format($invoices->where('status', 'paid')->sum('total_amount'), 0) }}</td>
                </tr>
                <tr>
                    <td>متأخرة</td>
                    <td>{{ $summary['by_status']['overdue'] }}</td>
                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['overdue'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                    <td class="amount">{{ number_format($invoices->where('status', 'overdue')->sum('total_amount'), 0) }}</td>
                </tr>
                <tr>
                    <td>ملغية</td>
                    <td>{{ $summary['by_status']['cancelled'] }}</td>
                    <td>{{ $summary['total_invoices'] > 0 ? number_format(($summary['by_status']['cancelled'] / $summary['total_invoices']) * 100, 1) : 0 }}%</td>
                    <td class="amount">{{ number_format($invoices->where('status', 'cancelled')->sum('total_amount'), 0) }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- أفضل الوكلاء -->
    <div class="section">
        <h3>أفضل 10 وكلاء من حيث الإيرادات</h3>
        <table>
            <thead>
                <tr>
                    <th>الترتيب</th>
                    <th>اسم الوكيل</th>
                    <th>اسم الشركة</th>
                    <th>عدد الفواتير</th>
                    <th>إجمالي المبلغ (ل.س)</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $agentRevenues = $invoices->groupBy('agent.name')->map(function($group) {
                        return [
                            'name' => $group->first()->agent->name,
                            'company' => $group->first()->agent->company_name,
                            'total' => $group->sum('total_amount'),
                            'count' => $group->count()
                        ];
                    })->sortByDesc('total')->take(10);
                @endphp
                
                @php $i = 1; @endphp
                @foreach($agentRevenues as $agent)
                <tr>
                    <td>{{ $i++ }}</td>
                    <td>{{ $agent['name'] }}</td>
                    <td>{{ $agent['company'] }}</td>
                    <td>{{ $agent['count'] }}</td>
                    <td class="amount">{{ number_format($agent['total'], 0) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- قائمة الفواتير التفصيلية -->
    <div class="section page-break">
        <h3>قائمة الفواتير التفصيلية ({{ $invoices->count() }})</h3>
        <table>
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>السفينة</th>
                    <th>الوكيل الملاحي</th>
                    <th>المبلغ الإجمالي</th>
                    <th>المبلغ المدفوع</th>
                    <th>المبلغ المتبقي</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoices as $invoice)
                <tr>
                    <td>{{ $invoice->invoice_number }}</td>
                    <td>{{ $invoice->invoice_date->format('Y/m/d') }}</td>
                    <td>{{ $invoice->ship->name }}</td>
                    <td>{{ $invoice->agent->name }}</td>
                    <td class="amount">{{ number_format($invoice->total_amount, 0) }}</td>
                    <td class="amount">{{ number_format($invoice->paid_amount, 0) }}</td>
                    <td class="amount">{{ number_format($invoice->remaining_amount, 0) }}</td>
                    <td>
                        <span class="status-badge status-{{ $invoice->status }}">
                            {{ $invoice->status_label }}
                        </span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- تحليل الفواتير المتأخرة -->
    @if($summary['by_status']['overdue'] > 0)
    <div class="section">
        <h3>تحليل الفواتير المتأخرة</h3>
        @php
            $overdueInvoices = $invoices->filter(function($invoice) {
                return $invoice->due_date && $invoice->due_date->isPast() && !$invoice->is_fully_paid;
            });
            
            $overdueByPeriod = [
                '1-30' => $overdueInvoices->filter(function($invoice) {
                    return $invoice->due_date->diffInDays(now()) <= 30;
                })->count(),
                '31-60' => $overdueInvoices->filter(function($invoice) {
                    $days = $invoice->due_date->diffInDays(now());
                    return $days > 30 && $days <= 60;
                })->count(),
                '61-90' => $overdueInvoices->filter(function($invoice) {
                    $days = $invoice->due_date->diffInDays(now());
                    return $days > 60 && $days <= 90;
                })->count(),
                '90+' => $overdueInvoices->filter(function($invoice) {
                    return $invoice->due_date->diffInDays(now()) > 90;
                })->count(),
            ];
        @endphp
        
        <table>
            <thead>
                <tr>
                    <th>فترة التأخير</th>
                    <th>عدد الفواتير</th>
                    <th>النسبة من المتأخرة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1-30 يوم</td>
                    <td>{{ $overdueByPeriod['1-30'] }}</td>
                    <td>{{ $overdueInvoices->count() > 0 ? number_format(($overdueByPeriod['1-30'] / $overdueInvoices->count()) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>31-60 يوم</td>
                    <td>{{ $overdueByPeriod['31-60'] }}</td>
                    <td>{{ $overdueInvoices->count() > 0 ? number_format(($overdueByPeriod['31-60'] / $overdueInvoices->count()) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>61-90 يوم</td>
                    <td>{{ $overdueByPeriod['61-90'] }}</td>
                    <td>{{ $overdueInvoices->count() > 0 ? number_format(($overdueByPeriod['61-90'] / $overdueInvoices->count()) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>أكثر من 90 يوم</td>
                    <td>{{ $overdueByPeriod['90+'] }}</td>
                    <td>{{ $overdueInvoices->count() > 0 ? number_format(($overdueByPeriod['90+'] / $overdueInvoices->count()) * 100, 1) : 0 }}%</td>
                </tr>
            </tbody>
        </table>
    </div>
    @endif

    <!-- ملخص التقرير -->
    <div class="section">
        <h3>ملخص التقرير</h3>
        <table>
            <tbody>
                <tr>
                    <td><strong>إجمالي الفواتير:</strong></td>
                    <td class="amount">{{ $summary['total_invoices'] }} فاتورة</td>
                </tr>
                <tr>
                    <td><strong>إجمالي المبلغ المستحق:</strong></td>
                    <td class="amount">{{ number_format($summary['total_amount'], 2) }} ل.س</td>
                </tr>
                <tr>
                    <td><strong>إجمالي المبلغ المحصل:</strong></td>
                    <td class="amount">{{ number_format($summary['paid_amount'], 2) }} ل.س</td>
                </tr>
                <tr>
                    <td><strong>معدل التحصيل:</strong></td>
                    <td class="amount">{{ number_format(($summary['paid_amount'] / max($summary['total_amount'], 1)) * 100, 1) }}%</td>
                </tr>
                <tr>
                    <td><strong>المبلغ المتبقي:</strong></td>
                    <td class="amount">{{ number_format($summary['remaining_amount'], 2) }} ل.س</td>
                </tr>
                <tr>
                    <td><strong>عدد الوكلاء النشطين:</strong></td>
                    <td class="amount">{{ $invoices->pluck('agent_id')->unique()->count() }} وكيل</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المرفأ البحري</p>
        <p>{{ config('app.name') }} - تاريخ الإنشاء: {{ now()->format('Y/m/d H:i') }}</p>
    </div>
</body>
</html>