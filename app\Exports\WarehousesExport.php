<?php

namespace App\Exports;

use App\Models\Warehouse;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WarehousesExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $warehouses;

    public function __construct($warehouses)
    {
        $this->warehouses = $warehouses;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->warehouses;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'رقم المستودع',
            'اسم المستودع',
            'الرمز',
            'النوع',
            'المساحة الإجمالية (م²)',
            'المساحة المتاحة (م²)',
            'المساحة المستخدمة (م²)',
            'نسبة الاستغلال (%)',
            'السعة القصوى (طن)',
            'السعة الحالية (طن)',
            'مغطى',
            'السعر اليومي',
            'الحالة',
            'الموقع',
            'اسم المدير',
            'هاتف المدير',
            'تاريخ الإنشاء',
            'الوصف'
        ];
    }

    /**
     * @param mixed $warehouse
     * @return array
     */
    public function map($warehouse): array
    {
        $usedArea = $warehouse->total_area - $warehouse->available_area;
        $occupancyRate = $warehouse->total_area > 0 ? ($usedArea / $warehouse->total_area) * 100 : 0;

        return [
            $warehouse->id,
            $warehouse->name,
            $warehouse->code,
            $this->getTypeLabel($warehouse->type),
            $warehouse->total_area,
            $warehouse->available_area,
            $usedArea,
            number_format($occupancyRate, 2),
            $warehouse->max_capacity,
            $warehouse->current_capacity,
            $warehouse->is_covered ? 'نعم' : 'لا',
            $warehouse->daily_rate,
            $this->getStatusLabel($warehouse->status),
            $warehouse->location,
            $warehouse->manager_name,
            $warehouse->manager_phone,
            $warehouse->created_at->format('Y-m-d H:i'),
            $warehouse->description
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    private function getTypeLabel($type)
    {
        $types = [
            'warehouse' => 'مستودع',
            'yard' => 'ساحة',
            'cold_storage' => 'تبريد',
            'tank' => 'خزان',
            'silo' => 'صومعة'
        ];

        return $types[$type] ?? $type;
    }

    private function getStatusLabel($status)
    {
        $statuses = [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'maintenance' => 'صيانة',
            'full' => 'ممتلئ'
        ];

        return $statuses[$status] ?? $status;
    }
}