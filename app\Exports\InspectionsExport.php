<?php

namespace App\Exports;

use App\Models\Inspection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;

class InspectionsExport implements FromCollection, WithHeadings, WithMapping
{
    protected $dateFrom;
    protected $dateTo;

    public function __construct($dateFrom, $dateTo)
    {
        $this->dateFrom = Carbon::parse($dateFrom)->startOfDay();
        $this->dateTo = Carbon::parse($dateTo)->endOfDay();
    }

    public function collection()
    {
        return Inspection::with('ship')
            ->whereBetween('inspection_date', [$this->dateFrom, $this->dateTo])
            ->orderBy('inspection_date', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            '#',
            'اسم السفينة',
            'علم السفينة',
            'نوع التفتيش',
            'المفتش',
            'الجهة المفتشة',
            'تاريخ التفتيش',
            'النتيجة',
            'صالح حتى',
        ];
    }

    public function map($inspection): array
    {
        return [
            $inspection->id,
            $inspection->ship ? $inspection->ship->name : '',
            $inspection->ship ? $inspection->ship->flag : '',
            $inspection->inspection_type,
            $inspection->inspector_name,
            $inspection->inspector_authority,
            $inspection->inspection_date ? $inspection->inspection_date->format('Y-m-d H:i') : '',
            $inspection->result,
            $inspection->valid_until ? $inspection->valid_until->format('Y-m-d') : '',
        ];
    }
} 