<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->foreignId('ship_id')->constrained('ships')->onDelete('cascade'); // السفينة
            $table->foreignId('agent_id')->constrained('agents'); // الوكيل الملاحي
            $table->date('invoice_date'); // تاريخ الفاتورة
            $table->date('due_date')->nullable(); // تاريخ الاستحقاق
            $table->decimal('berthing_fees', 10, 2)->default(0); // رسوم الرسو
            $table->decimal('cargo_handling_fees', 10, 2)->default(0); // رسوم مناولة البضائع
            $table->decimal('storage_fees', 10, 2)->default(0); // رسوم التخزين
            $table->decimal('pilotage_fees', 10, 2)->default(0); // رسوم الإرشاد
            $table->decimal('other_fees', 10, 2)->default(0); // رسوم أخرى
            $table->decimal('total_amount', 10, 2); // المبلغ الإجمالي
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->enum('status', ['draft', 'sent', 'paid', 'overdue', 'cancelled'])->default('draft'); // حالة الفاتورة
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
