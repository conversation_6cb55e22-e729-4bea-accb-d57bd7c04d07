@extends('layouts.port-app')

@section('page-title', 'إدارة الأرصفة')

@section('page-actions')
<div class="btn-group">
    @can('berths.create')
    <a href="{{ route('berths.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة رصيف جديد
    </a>
    @endcan
</div>
@endsection

@section('content')
<div class="row mb-4">
    <div class="col-md-12">
        <!-- فلاتر البحث -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر البحث
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('berths.index') }}">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث في اسم الرصيف أو الكود</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="اسم الرصيف أو الكود...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="available" {{ request('status') === 'available' ? 'selected' : '' }}>
                                    متاح
                                </option>
                                <option value="occupied" {{ request('status') === 'occupied' ? 'selected' : '' }}>
                                    مشغول
                                </option>
                                <option value="maintenance" {{ request('status') === 'maintenance' ? 'selected' : '' }}>
                                    صيانة
                                </option>
                                <option value="out_of_service" {{ request('status') === 'out_of_service' ? 'selected' : '' }}>
                                    خارج الخدمة
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="{{ route('berths.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ $berths->where('status', 'available')->count() }}</h4>
                        <p class="mb-0">أرصفة متاحة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ $berths->where('status', 'occupied')->count() }}</h4>
                        <p class="mb-0">أرصفة مشغولة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-ship fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ $berths->where('status', 'maintenance')->count() }}</h4>
                        <p class="mb-0">قيد الصيانة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ $berths->where('status', 'out_of_service')->count() }}</h4>
                        <p class="mb-0">خارج الخدمة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-anchor me-2"></i>
                    قائمة الأرصفة ({{ $berths->total() }} رصيف)
                </h5>
            </div>
            <div class="card-body">
                @if($berths->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم الرصيف</th>
                                    <th>الأبعاد</th>
                                    <th>الحمولة القصوى</th>
                                    <th>الحالة</th>
                                    <th>السفينة الحالية</th>
                                    <th>عدد السفن</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($berths as $berth)
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ $berth->code }}</strong>
                                    </td>
                                    <td>{{ $berth->name }}</td>
                                    <td>
                                        @if($berth->length && $berth->depth)
                                            <small class="text-muted">
                                                الطول: {{ $berth->length }}م<br>
                                                العمق: {{ $berth->depth }}م
                                            </small>
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($berth->max_tonnage)
                                            <span class="badge bg-info">{{ number_format($berth->max_tonnage) }} طن</span>
                                        @else
                                            <span class="text-muted">غير محدد</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($berth->status)
                                            @case('available')
                                                <span class="badge bg-success">متاح</span>
                                                @break
                                            @case('occupied')
                                                <span class="badge bg-warning">مشغول</span>
                                                @break
                                            @case('maintenance')
                                                <span class="badge bg-info">صيانة</span>
                                                @break
                                            @case('out_of_service')
                                                <span class="badge bg-danger">خارج الخدمة</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        @if($berth->currentShip)
                                            <div>
                                                <strong>{{ $berth->currentShip->name }}</strong><br>
                                                <small class="text-muted">
                                                    منذ {{ $berth->occupied_since ? $berth->occupied_since->diffForHumans() : 'غير محدد' }}
                                                </small>
                                            </div>
                                        @else
                                            <span class="text-muted">لا توجد</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $berth->ships_count }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @can('berths.view')
                                            <a href="{{ route('berths.show', $berth) }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('berths.edit')
                                            <a href="{{ route('berths.edit', $berth) }}" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                            
                                            @can('berths.delete')
                                            <button type="button" class="btn btn-outline-danger" title="حذف"
                                                    onclick="confirmDelete('{{ $berth->id }}', '{{ $berth->name }}', {{ $berth->ships_count }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $berths->withQueryString()->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-anchor fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أرصفة</h5>
                        <p class="text-muted">لم يتم العثور على أي أرصفة تطابق معايير البحث</p>
                        @can('berths.create')
                        <a href="{{ route('berths.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة رصيف جديد
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الرصيف <strong id="berthName"></strong>؟</p>
                <div id="warningMessage" class="text-danger" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا يمكن حذف هذا الرصيف لأنه مرتبط بسفن موجودة
                </div>
                <p id="confirmMessage" class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم حذف جميع البيانات المرتبطة بهذا الرصيف نهائياً
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="deleteButton">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(berthId, berthName, shipsCount) {
    document.getElementById('berthName').textContent = berthName;
    document.getElementById('deleteForm').action = '/berths/' + berthId;
    
    const warningMessage = document.getElementById('warningMessage');
    const confirmMessage = document.getElementById('confirmMessage');
    const deleteButton = document.getElementById('deleteButton');
    
    if (shipsCount > 0) {
        warningMessage.style.display = 'block';
        confirmMessage.style.display = 'none';
        deleteButton.style.display = 'none';
    } else {
        warningMessage.style.display = 'none';
        confirmMessage.style.display = 'block';
        deleteButton.style.display = 'inline-block';
    }
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
