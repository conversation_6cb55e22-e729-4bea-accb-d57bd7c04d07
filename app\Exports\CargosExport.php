<?php

namespace App\Exports;

use App\Models\Cargo;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CargosExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $cargos;

    public function __construct($cargos)
    {
        $this->cargos = $cargos;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->cargos;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'رقم الحمولة',
            'الوصف',
            'نوع الحمولة',
            'السفينة',
            'الوكيل الملاحي',
            'الكمية',
            'الوحدة',
            'الوزن (طن)',
            'الحجم (م³)',
            'القيمة',
            'العملة',
            'بلد المنشأ',
            'بلد الوجهة',
            'الحالة',
            'تاريخ التحميل',
            'تاريخ التفريغ',
            'تاريخ الإنشاء',
            'ملاحظات'
        ];
    }

    /**
     * @param mixed $cargo
     * @return array
     */
    public function map($cargo): array
    {
        return [
            $cargo->id,
            $cargo->description,
            $this->getCargoTypeLabel($cargo->cargo_type),
            $cargo->ship->name ?? '',
            $cargo->ship->agent->name ?? '',
            $cargo->quantity,
            $this->getUnitLabel($cargo->unit),
            $cargo->weight,
            $cargo->volume,
            $cargo->value,
            $cargo->currency,
            $cargo->origin_country,
            $cargo->destination_country,
            $this->getStatusLabel($cargo->status),
            $cargo->loading_date ? $cargo->loading_date->format('Y-m-d') : '',
            $cargo->unloading_date ? $cargo->unloading_date->format('Y-m-d') : '',
            $cargo->created_at->format('Y-m-d H:i'),
            $cargo->notes
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    private function getCargoTypeLabel($type)
    {
        $types = [
            'container' => 'حاويات',
            'bulk' => 'بضائع سائبة',
            'liquid' => 'سوائل',
            'gas' => 'غازات',
            'vehicles' => 'مركبات',
            'general' => 'بضائع عامة',
            'dangerous' => 'مواد خطرة',
            'refrigerated' => 'مبردة'
        ];

        return $types[$type] ?? $type;
    }

    private function getUnitLabel($unit)
    {
        $units = [
            'tons' => 'طن',
            'kg' => 'كيلوغرام',
            'pieces' => 'قطعة',
            'containers' => 'حاوية',
            'pallets' => 'منصة',
            'cubic_meters' => 'متر مكعب',
            'liters' => 'لتر'
        ];

        return $units[$unit] ?? $unit;
    }

    private function getStatusLabel($status)
    {
        $statuses = [
            'loading' => 'قيد التحميل',
            'loaded' => 'محمل',
            'in_transit' => 'في الطريق',
            'unloading' => 'قيد التفريغ',
            'unloaded' => 'مفرغ',
            'stored' => 'مخزن',
            'delivered' => 'مسلم'
        ];

        return $statuses[$status] ?? $status;
    }
}