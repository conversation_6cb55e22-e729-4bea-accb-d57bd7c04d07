<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('berths', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الرصيف
            $table->string('code')->unique(); // رمز الرصيف
            $table->decimal('length', 8, 2); // طول الرصيف
            $table->decimal('depth', 8, 2); // عمق الرصيف
            $table->integer('max_tonnage')->nullable(); // أقصى حمولة
            $table->enum('status', ['available', 'occupied', 'maintenance'])->default('available'); // حالة الرصيف
            $table->foreignId('current_ship_id')->nullable()->constrained('ships'); // السفينة الحالية
            $table->datetime('occupied_since')->nullable(); // مشغول منذ
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('berths');
    }
};
