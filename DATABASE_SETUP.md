# إعداد قاعدة البيانات MySQL

## المتطلبات
- MySQL Server 8.0 أو أحدث
- PHP مع امتداد MySQL/PDO

## خطوات الإعداد

### 1. إنشاء قاعدة البيانات
قم بتشغيل الأوامر التالية في MySQL:

```sql
CREATE DATABASE IF NOT EXISTS port_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### 2. إنشاء مستخدم (اختياري)
```sql
CREATE USER IF NOT EXISTS 'port_user'@'localhost' IDENTIFIED BY 'port_password';
GRANT ALL PRIVILEGES ON port_management.* TO 'port_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تحديث ملف .env
تأكد من أن إعدادات قاعدة البيانات في ملف `.env` كالتالي:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=port_management
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
```

### 4. تشغيل الجداول
```bash
php artisan migrate:fresh --seed
```

## ملاحظات
- تأكد من تشغيل MySQL Server قبل تشغيل الأوامر
- قم بتحديث كلمة المرور في ملف .env حسب إعدادات MySQL الخاصة بك
- النظام يدعم اللغة العربية بالكامل مع ترميز UTF-8
