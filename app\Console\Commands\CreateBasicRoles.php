<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CreateBasicRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'roles:create-basic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create basic roles and permissions for the port management system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating basic roles and permissions...');

        // إنشاء الأدوار الأساسية
        $roles = [
            ['name' => 'admin', 'display_name' => 'مدير النظام', 'description' => 'صلاحيات كاملة على النظام'],
            ['name' => 'manager', 'display_name' => 'مدير', 'description' => 'صلاحيات إدارية محدودة'],
            ['name' => 'employee', 'display_name' => 'موظف', 'description' => 'صلاحيات أساسية للموظفين'],
            ['name' => 'accountant', 'display_name' => 'محاسب', 'description' => 'صلاحيات محاسبية'],
            ['name' => 'operator', 'display_name' => 'مشغل', 'description' => 'صلاحيات تشغيلية'],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
            $this->info("Role created: {$role->display_name}");
        }

        // إنشاء الصلاحيات الأساسية
        $permissions = [
            // صلاحيات المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين'],
            ['name' => 'users.create', 'display_name' => 'إنشاء مستخدمين'],
            ['name' => 'users.edit', 'display_name' => 'تعديل المستخدمين'],
            ['name' => 'users.delete', 'display_name' => 'حذف المستخدمين'],
            
            // صلاحيات الفواتير
            ['name' => 'invoices.view', 'display_name' => 'عرض الفواتير'],
            ['name' => 'invoices.create', 'display_name' => 'إنشاء فواتير'],
            ['name' => 'invoices.edit', 'display_name' => 'تعديل الفواتير'],
            ['name' => 'invoices.delete', 'display_name' => 'حذف الفواتير'],
            
            // صلاحيات السفن
            ['name' => 'ships.view', 'display_name' => 'عرض السفن'],
            ['name' => 'ships.create', 'display_name' => 'إنشاء سفن'],
            ['name' => 'ships.edit', 'display_name' => 'تعديل السفن'],
            ['name' => 'ships.delete', 'display_name' => 'حذف السفن'],
            
            // صلاحيات التقارير
            ['name' => 'reports.view', 'display_name' => 'عرض التقارير'],
            ['name' => 'reports.export', 'display_name' => 'تصدير التقارير'],
        ];

        foreach ($permissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
            $this->info("Permission created: {$permission->display_name}");
        }

        // تعيين الصلاحيات للأدوار
        $adminRole = Role::where('name', 'admin')->first();
        $adminRole->givePermissionTo(Permission::all());

        $managerRole = Role::where('name', 'manager')->first();
        $managerRole->givePermissionTo([
            'users.view', 'users.create', 'users.edit',
            'invoices.view', 'invoices.create', 'invoices.edit',
            'ships.view', 'ships.create', 'ships.edit',
            'reports.view', 'reports.export'
        ]);

        $employeeRole = Role::where('name', 'employee')->first();
        $employeeRole->givePermissionTo([
            'invoices.view', 'invoices.create',
            'ships.view',
            'reports.view'
        ]);

        $this->info('Basic roles and permissions created successfully!');
        return 0;
    }
}
