@extends('layouts.port-app')

@section('title', 'التقرير المالي الشهري')

@section('content')
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="text-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    التقرير المالي الشهري - {{ $report_month->format('F Y') }}
                </h2>
                <div class="btn-group">
                    <a href="{{ route('reports.monthly-financial', array_merge(request()->all(), ['format' => 'pdf'])) }}" 
                       class="btn btn-danger">
                        <i class="fas fa-file-pdf me-2"></i>
                        تحميل PDF
                    </a>
                    <a href="{{ route('reports.monthly-financial', array_merge(request()->all(), ['format' => 'excel'])) }}" 
                       class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i>
                        تحميل Excel
                    </a>
                    <a href="{{ route('reports.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- فلتر الشهر -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar me-2"></i>
                اختيار الشهر
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('reports.monthly-financial') }}">
                <div class="row">
                    <div class="col-md-4">
                        <label for="month" class="form-label">الشهر والسنة</label>
                        <input type="month" name="month" id="month" class="form-control" 
                               value="{{ request('month', $report_month->format('Y-m')) }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search me-2"></i>
                            عرض التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $invoice_stats['total_invoices'] }}</h3>
                    <p class="mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($invoice_stats['total_amount'], 0) }}</h3>
                    <p class="mb-0">إجمالي المبلغ (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($invoice_stats['paid_amount'], 0) }}</h3>
                    <p class="mb-0">المبلغ المحصل (ل.س)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ number_format($invoice_stats['pending_amount'], 0) }}</h3>
                    <p class="mb-0">المبلغ المعلق (ل.س)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تفصيل الإيرادات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        تفصيل الإيرادات حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>نوع الرسوم</th>
                                    <th>المبلغ (ل.س)</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $total_revenue = array_sum($revenue_breakdown);
                                @endphp
                                <tr>
                                    <td><i class="fas fa-anchor me-2 text-primary"></i>رسوم الرسو</td>
                                    <td>{{ number_format($revenue_breakdown['berthing_fees'], 2) }}</td>
                                    <td>
                                        @if($total_revenue > 0)
                                            {{ number_format(($revenue_breakdown['berthing_fees'] / $total_revenue) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-boxes me-2 text-success"></i>رسوم مناولة البضائع</td>
                                    <td>{{ number_format($revenue_breakdown['cargo_handling_fees'], 2) }}</td>
                                    <td>
                                        @if($total_revenue > 0)
                                            {{ number_format(($revenue_breakdown['cargo_handling_fees'] / $total_revenue) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-warehouse me-2 text-warning"></i>رسوم التخزين</td>
                                    <td>{{ number_format($revenue_breakdown['storage_fees'], 2) }}</td>
                                    <td>
                                        @if($total_revenue > 0)
                                            {{ number_format(($revenue_breakdown['storage_fees'] / $total_revenue) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-compass me-2 text-info"></i>رسوم الإرشاد</td>
                                    <td>{{ number_format($revenue_breakdown['pilotage_fees'], 2) }}</td>
                                    <td>
                                        @if($total_revenue > 0)
                                            {{ number_format(($revenue_breakdown['pilotage_fees'] / $total_revenue) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-plus me-2 text-secondary"></i>رسوم أخرى</td>
                                    <td>{{ number_format($revenue_breakdown['other_fees'], 2) }}</td>
                                    <td>
                                        @if($total_revenue > 0)
                                            {{ number_format(($revenue_breakdown['other_fees'] / $total_revenue) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <th>الإجمالي</th>
                                    <th>{{ number_format($total_revenue, 2) }}</th>
                                    <th>100%</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الوكلاء -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        أفضل 5 وكلاء ملاحيين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الوكيل</th>
                                    <th>عدد الفواتير</th>
                                    <th>إجمالي المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($agent_stats->take(5) as $agent)
                                <tr>
                                    <td>
                                        <strong>{{ $agent['name'] }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $agent['company_name'] }}</small>
                                    </td>
                                    <td>{{ $agent['invoices_count'] }}</td>
                                    <td>{{ number_format($agent['total_amount'], 0) }} ل.س</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات اليومية -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-day me-2"></i>
                الإحصائيات اليومية للشهر
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>اليوم</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبلغ (ل.س)</th>
                            <th>المبلغ المحصل (ل.س)</th>
                            <th>معدل التحصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($daily_stats as $day)
                        <tr>
                            <td>{{ \Carbon\Carbon::parse($day['date'])->format('Y/m/d') }}</td>
                            <td>{{ \Carbon\Carbon::parse($day['date'])->locale('ar')->dayName }}</td>
                            <td>
                                @if($day['invoices_count'] > 0)
                                    <span class="badge bg-primary">{{ $day['invoices_count'] }}</span>
                                @else
                                    <span class="text-muted">0</span>
                                @endif
                            </td>
                            <td>{{ number_format($day['total_amount'], 0) }}</td>
                            <td>{{ number_format($day['paid_amount'], 0) }}</td>
                            <td>
                                @if($day['total_amount'] > 0)
                                    @php
                                        $collection_rate = ($day['paid_amount'] / $day['total_amount']) * 100;
                                    @endphp
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar 
                                            @if($collection_rate >= 80) bg-success
                                            @elseif($collection_rate >= 50) bg-warning
                                            @else bg-danger
                                            @endif" 
                                            role="progressbar" 
                                            style="width: {{ $collection_rate }}%">
                                            {{ number_format($collection_rate, 1) }}%
                                        </div>
                                    </div>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// يمكن إضافة مخططات بيانية هنا باستخدام Chart.js
</script>
@endpush
@endsection