@extends('layouts.port-app')

@section('page-title', 'لوحة التحكم')

@section('content')
<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $stats['total_ships'] ?? 0 }}</h3>
                        <p class="mb-0">إجمالي السفن</p>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-ship"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $stats['ships_in_port'] ?? 0 }}</h3>
                        <p class="mb-0">السفن في الميناء</p>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-anchor"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $berthStats['available_berths'] ?? 0 }}</h3>
                        <p class="mb-0">الأرصفة المتاحة</p>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-warehouse"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $stats['total_agents'] ?? 0 }}</h3>
                        <p class="mb-0">الوكلاء النشطون</p>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الفواتير -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $invoiceStats['today_invoices'] ?? 0 }}</h3>
                        <p class="mb-0">فواتير اليوم</p>
                        <small>{{ number_format($invoiceStats['today_amount'] ?? 0, 0) }} ل.س</small>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-dark text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $invoiceStats['month_invoices'] ?? 0 }}</h3>
                        <p class="mb-0">فواتير الشهر</p>
                        <small>{{ number_format($invoiceStats['month_amount'] ?? 0, 0) }} ل.س</small>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $invoiceStats['overdue_count'] ?? 0 }}</h3>
                        <p class="mb-0">فواتير متأخرة</p>
                        <small>{{ number_format($invoiceStats['overdue_amount'] ?? 0, 0) }} ل.س</small>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ number_format($invoiceStats['month_paid'] ?? 0, 0) }}</h3>
                        <p class="mb-0">المحصل هذا الشهر</p>
                        <small>
                            @php
                                $collection_rate = ($invoiceStats['month_amount'] ?? 0) > 0 
                                    ? (($invoiceStats['month_paid'] ?? 0) / ($invoiceStats['month_amount'] ?? 1)) * 100 
                                    : 0;
                            @endphp
                            معدل التحصيل: {{ number_format($collection_rate, 1) }}%
                        </small>
                    </div>
                    <div class="fs-1">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مقارنة مع الشهر الماضي -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    مقارنة الأداء مع الشهر الماضي
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ $invoiceStats['month_invoices'] ?? 0 }}</h4>
                            <p class="mb-1">فواتير هذا الشهر</p>
                            <small class="text-muted">
                                الشهر الماضي: {{ $lastMonthStats['invoices'] ?? 0 }}
                                @php
                                    $invoiceDiff = ($invoiceStats['month_invoices'] ?? 0) - ($lastMonthStats['invoices'] ?? 0);
                                @endphp
                                @if($invoiceDiff > 0)
                                    <span class="text-success">(+{{ $invoiceDiff }})</span>
                                @elseif($invoiceDiff < 0)
                                    <span class="text-danger">({{ $invoiceDiff }})</span>
                                @else
                                    <span class="text-muted">(0)</span>
                                @endif
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h4 class="text-success">{{ number_format($invoiceStats['month_amount'] ?? 0, 0) }}</h4>
                            <p class="mb-1">إيرادات هذا الشهر (ل.س)</p>
                            <small class="text-muted">
                                الشهر الماضي: {{ number_format($lastMonthStats['amount'] ?? 0, 0) }}
                                @php
                                    $amountDiff = ($invoiceStats['month_amount'] ?? 0) - ($lastMonthStats['amount'] ?? 0);
                                @endphp
                                @if($amountDiff > 0)
                                    <span class="text-success">(+{{ number_format($amountDiff, 0) }})</span>
                                @elseif($amountDiff < 0)
                                    <span class="text-danger">({{ number_format($amountDiff, 0) }})</span>
                                @else
                                    <span class="text-muted">(0)</span>
                                @endif
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h4 class="text-info">{{ $stats['total_ships'] ?? 0 }}</h4>
                            <p class="mb-1">سفن جديدة هذا الشهر</p>
                            <small class="text-muted">
                                الشهر الماضي: {{ $lastMonthStats['ships'] ?? 0 }}
                                @php
                                    $shipsDiff = ($stats['total_ships'] ?? 0) - ($lastMonthStats['ships'] ?? 0);
                                @endphp
                                @if($shipsDiff > 0)
                                    <span class="text-success">(+{{ $shipsDiff }})</span>
                                @elseif($shipsDiff < 0)
                                    <span class="text-danger">({{ $shipsDiff }})</span>
                                @else
                                    <span class="text-muted">(0)</span>
                                @endif
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- السفن المتوقع وصولها اليوم -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    السفن المتوقع وصولها اليوم
                </h5>
            </div>
            <div class="card-body">
                @if($expected_arrivals && $expected_arrivals->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم السفينة</th>
                                    <th>الوكيل</th>
                                    <th>الوقت المتوقع</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($expected_arrivals as $ship)
                                <tr>
                                    <td>{{ $ship->name }}</td>
                                    <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                                    <td>{{ $ship->expected_arrival_date ? $ship->expected_arrival_date->format('H:i') : 'غير محدد' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted text-center py-3">لا توجد سفن متوقع وصولها اليوم</p>
                @endif
            </div>
        </div>
    </div>

    <!-- السفن الموجودة في الميناء -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ship me-2"></i>
                    السفن الموجودة في الميناء
                </h5>
            </div>
            <div class="card-body">
                @if($ships_in_port && $ships_in_port->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم السفينة</th>
                                    <th>الوكيل</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الوصول</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($ships_in_port as $ship)
                                <tr>
                                    <td>{{ $ship->name }}</td>
                                    <td>{{ $ship->agent->name ?? 'غير محدد' }}</td>
                                    <td>
                                        @if($ship->status == 'berthed')
                                            <span class="badge bg-success">مرسية</span>
                                        @elseif($ship->status == 'outside_port')
                                            <span class="badge bg-warning">خارج الحوض</span>
                                        @endif
                                    </td>
                                    <td>{{ $ship->actual_arrival_date ? $ship->actual_arrival_date->format('Y-m-d') : 'غير محدد' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted text-center py-3">لا توجد سفن في الميناء حالياً</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- الفواتير المتأخرة والأنشطة الحديثة -->
<div class="row mb-4">
    <!-- الفواتير المتأخرة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    الفواتير المتأخرة ({{ $overdue_invoices->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($overdue_invoices && $overdue_invoices->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>السفينة</th>
                                    <th>الوكيل</th>
                                    <th>المبلغ</th>
                                    <th>أيام التأخير</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($overdue_invoices as $invoice)
                                <tr>
                                    <td>
                                        <a href="{{ route('invoices.show', $invoice) }}" class="text-decoration-none">
                                            {{ $invoice->invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ $invoice->ship->name }}</td>
                                    <td>{{ $invoice->agent->name }}</td>
                                    <td>{{ number_format($invoice->remaining_amount, 0) }} ل.س</td>
                                    <td>
                                        <span class="badge bg-danger">
                                            {{ $invoice->due_date->diffInDays(now()) }} يوم
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('invoices.index', ['status' => 'overdue']) }}" class="btn btn-sm btn-outline-danger">
                            عرض جميع الفواتير المتأخرة
                        </a>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد فواتير متأخرة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- الأنشطة الحديثة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    الأنشطة الحديثة
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    @if($recent_activities['recent_invoices']->count() > 0)
                        <h6 class="text-muted mb-3">أحدث الفواتير</h6>
                        @foreach($recent_activities['recent_invoices'] as $invoice)
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-file-invoice text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>فاتورة {{ $invoice->invoice_number }}</strong>
                                            <br>
                                            <small class="text-muted">
                                                {{ $invoice->ship->name }} - {{ $invoice->agent->name }}
                                            </small>
                                        </div>
                                        <small class="text-muted">
                                            {{ $invoice->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    @endif

                    @if($recent_activities['recent_ships']->count() > 0)
                        <h6 class="text-muted mb-3 mt-4">أحدث السفن</h6>
                        @foreach($recent_activities['recent_ships']->take(3) as $ship)
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-ship text-success"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>{{ $ship->name }}</strong>
                                            <br>
                                            <small class="text-muted">
                                                {{ $ship->agent->name ?? 'بدون وكيل' }}
                                            </small>
                                        </div>
                                        <small class="text-muted">
                                            {{ $ship->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة وإحصائيات -->
<div class="row mb-4">
    <!-- روابط سريعة -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    @can('ships.create')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('ships.create') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة سفينة جديدة
                        </a>
                    </div>
                    @endcan

                    @can('invoices.create')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('invoices.create') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-file-invoice-dollar me-2"></i>
                            إنشاء فاتورة جديدة
                        </a>
                    </div>
                    @endcan

                    @can('reports.view')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('reports.monthly-financial') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-line me-2"></i>
                            التقرير المالي الشهري
                        </a>
                    </div>
                    @endcan

                    @can('agents.create')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('agents.create') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة وكيل ملاحي
                        </a>
                    </div>
                    @endcan

                    @can('invoices.view')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('invoices.report') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-file-alt me-2"></i>
                            تقرير الفواتير المتقدم
                        </a>
                    </div>
                    @endcan

                    @can('reports.view')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('reports.berths-utilization') }}" class="btn btn-outline-dark w-100">
                            <i class="fas fa-warehouse me-2"></i>
                            تقرير استخدام الأرصفة
                        </a>
                    </div>
                    @endcan

                    @can('invoices.edit')
                    <div class="col-md-3 mb-3">
                        <form action="{{ route('invoices.send-overdue-reminders') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger w-100" 
                                    onclick="return confirm('هل تريد إرسال تذكيرات للفواتير المتأخرة؟')">
                                <i class="fas fa-bell me-2"></i>
                                إرسال تذكيرات متأخرة
                            </button>
                        </form>
                    </div>
                    @endcan

                    @can('reports.view')
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('reports.agents-performance') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users me-2"></i>
                            تقرير أداء الوكلاء
                        </a>
                    </div>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأرصفة -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    حالة الأرصفة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2 bg-success text-white">
                            <h4>{{ $berthStats['available_berths'] ?? 0 }}</h4>
                            <small>متاحة</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2 bg-danger text-white">
                            <h4>{{ $berthStats['occupied_berths'] ?? 0 }}</h4>
                            <small>مشغولة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2 bg-warning text-dark">
                            <h4>{{ $berthStats['maintenance_berths'] ?? 0 }}</h4>
                            <small>صيانة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2 bg-info text-white">
                            <h4>{{ $berthStats['total_berths'] ?? 0 }}</h4>
                            <small>الإجمالي</small>
                        </div>
                    </div>
                </div>
                
                @php
                    $occupancy_rate = ($berthStats['total_berths'] ?? 0) > 0 
                        ? (($berthStats['occupied_berths'] ?? 0) / ($berthStats['total_berths'] ?? 1)) * 100 
                        : 0;
                @endphp
                
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <small>معدل الإشغال</small>
                        <small>{{ number_format($occupancy_rate, 1) }}%</small>
                    </div>
                    <div class="progress">
                        <div class="progress-bar 
                            @if($occupancy_rate >= 80) bg-danger
                            @elseif($occupancy_rate >= 60) bg-warning
                            @else bg-success
                            @endif" 
                            role="progressbar" 
                            style="width: {{ $occupancy_rate }}%">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if($invoiceStats['overdue_count'] > 0)
<!-- تنبيه الفواتير المتأخرة -->
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>تنبيه:</strong> يوجد {{ $invoiceStats['overdue_count'] }} فاتورة متأخرة بقيمة إجمالية {{ number_format($invoiceStats['overdue_amount'], 0) }} ل.س.
    <a href="{{ route('invoices.index', ['status' => 'overdue']) }}" class="alert-link">عرض الفواتير المتأخرة</a>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif
@endsection
