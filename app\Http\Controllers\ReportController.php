<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ship;
use App\Models\Agent;
use App\Models\Berth;
use App\Models\Cargo;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class ReportController extends Controller
{
    /**
     * عرض صفحة التقارير
     */
    public function index()
    {
        return view('reports.index');
    }

    /**
     * تقرير حركة السفن اليومي (مشابه لتقرير مرفأ اللاذقية)
     */
    public function dailyShipMovement(Request $request)
    {
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));
        $reportDate = Carbon::parse($date);

        // إعلانات الوصول
        $arrival_announcements = Ship::where('status', 'announced')
            ->whereDate('expected_arrival_date', $reportDate)
            ->with(['agent', 'cargos'])
            ->get();

        // السفن الموجودة خارج الحوض
        $ships_outside_port = Ship::where('status', 'outside_port')
            ->with(['agent', 'cargos'])
            ->get();

        // السفن الموجودة على الأرصفة
        $ships_on_berths = Ship::where('status', 'berthed')
            ->with(['agent', 'cargos', 'currentBerth.berth'])
            ->get();

        // السفن التي غادرت خلال 24 ساعة
        $departed_ships = Ship::where('status', 'departed')
            ->whereDate('departure_date', $reportDate)
            ->with(['agent', 'cargos'])
            ->get();

        $data = [
            'report_date' => $reportDate,
            'arrival_announcements' => $arrival_announcements,
            'ships_outside_port' => $ships_outside_port,
            'ships_on_berths' => $ships_on_berths,
            'departed_ships' => $departed_ships
        ];

        if ($request->get('format') === 'pdf') {
            $pdf = Pdf::loadView('reports.daily-ship-movement-pdf', $data);
            return $pdf->download('تقرير-حركة-السفن-' . $reportDate->format('Y-m-d') . '.pdf');
        }

        return view('reports.daily-ship-movement', $data);
    }

    /**
     * تقرير مالي شهري
     */
    public function monthlyFinancial(Request $request)
    {
        $month = $request->get('month', Carbon::now()->format('Y-m'));
        $reportMonth = Carbon::parse($month . '-01');
        $startDate = $reportMonth->copy()->startOfMonth();
        $endDate = $reportMonth->copy()->endOfMonth();

        // إحصائيات الفواتير
        $invoiceStats = [
            'total_invoices' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->count(),
            'total_amount' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('total_amount'),
            'paid_amount' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('paid_amount'),
            'pending_amount' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('total_amount') - 
                               \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('paid_amount'),
        ];

        // تفصيل الإيرادات حسب النوع
        $revenueBreakdown = [
            'berthing_fees' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('berthing_fees'),
            'cargo_handling_fees' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('cargo_handling_fees'),
            'storage_fees' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('storage_fees'),
            'pilotage_fees' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('pilotage_fees'),
            'other_fees' => \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])->sum('other_fees'),
        ];

        // إحصائيات الوكلاء
        $agentStats = \App\Models\Agent::withCount(['invoices' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('invoice_date', [$startDate, $endDate]);
        }])
        ->with(['invoices' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('invoice_date', [$startDate, $endDate]);
        }])
        ->get()
        ->map(function($agent) {
            return [
                'name' => $agent->name,
                'company_name' => $agent->company_name,
                'invoices_count' => $agent->invoices_count,
                'total_amount' => $agent->invoices->sum('total_amount'),
                'paid_amount' => $agent->invoices->sum('paid_amount'),
            ];
        })
        ->sortByDesc('total_amount');

        // إحصائيات يومية للشهر
        $dailyStats = [];
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dayInvoices = \App\Models\Invoice::whereDate('invoice_date', $currentDate)->get();
            $dailyStats[] = [
                'date' => $currentDate->format('Y-m-d'),
                'day_name' => $currentDate->format('l'),
                'invoices_count' => $dayInvoices->count(),
                'total_amount' => $dayInvoices->sum('total_amount'),
                'paid_amount' => $dayInvoices->sum('paid_amount'),
            ];
            $currentDate->addDay();
        }

        $data = [
            'report_month' => $reportMonth,
            'invoice_stats' => $invoiceStats,
            'revenue_breakdown' => $revenueBreakdown,
            'agent_stats' => $agentStats,
            'daily_stats' => $dailyStats,
        ];

        if ($request->get('format') === 'pdf') {
            $pdf = Pdf::loadView('reports.monthly-financial-pdf', $data);
            return $pdf->download('التقرير-المالي-' . $reportMonth->format('Y-m') . '.pdf');
        }

        if ($request->get('format') === 'excel') {
            return $this->exportMonthlyFinancialExcel($data);
        }

        return view('reports.monthly-financial', $data);
    }

    /**
     * تقرير استخدام الأرصفة
     */
    public function berthsUtilization(Request $request)
    {
        $dateFrom = $request->get('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', Carbon::now()->format('Y-m-d'));
        
        $startDate = Carbon::parse($dateFrom)->startOfDay();
        $endDate = Carbon::parse($dateTo)->endOfDay();

        // إحصائيات الأرصفة
        $berths = Berth::with(['shipBerthHistory' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('berthing_start', [$startDate, $endDate])
                  ->orWhereBetween('berthing_end', [$startDate, $endDate])
                  ->orWhere(function($q) use ($startDate, $endDate) {
                      $q->where('berthing_start', '<=', $startDate)
                        ->where(function($subQ) use ($endDate) {
                            $subQ->where('berthing_end', '>=', $endDate)
                                 ->orWhereNull('berthing_end');
                        });
                  });
        }])->get();

        $berthStats = $berths->map(function($berth) use ($startDate, $endDate) {
            $totalDays = $startDate->diffInDays($endDate) + 1;
            $occupiedDays = 0;
            $totalRevenue = 0;
            $shipsCount = $berth->shipBerthHistory->count();

            foreach ($berth->shipBerthHistory as $history) {
                $berthStart = Carbon::parse($history->berthing_start);
                $berthEnd = $history->berthing_end ? Carbon::parse($history->berthing_end) : Carbon::now();
                
                // حساب الأيام المشغولة في الفترة المحددة
                $periodStart = $berthStart->max($startDate);
                $periodEnd = $berthEnd->min($endDate);
                
                if ($periodStart <= $periodEnd) {
                    $occupiedDays += $periodStart->diffInDays($periodEnd) + 1;
                    $totalRevenue += $history->total_berthing_cost ?? 0;
                }
            }

            $utilizationRate = $totalDays > 0 ? ($occupiedDays / $totalDays) * 100 : 0;

            return [
                'berth' => $berth,
                'total_days' => $totalDays,
                'occupied_days' => $occupiedDays,
                'utilization_rate' => round($utilizationRate, 2),
                'ships_count' => $shipsCount,
                'total_revenue' => $totalRevenue,
                'average_revenue_per_day' => $occupiedDays > 0 ? $totalRevenue / $occupiedDays : 0,
            ];
        });

        $data = [
            'date_from' => $startDate,
            'date_to' => $endDate,
            'berth_stats' => $berthStats,
            'summary' => [
                'total_berths' => $berths->count(),
                'average_utilization' => $berthStats->avg('utilization_rate'),
                'total_revenue' => $berthStats->sum('total_revenue'),
                'total_ships' => $berthStats->sum('ships_count'),
            ]
        ];

        if ($request->get('format') === 'pdf') {
            $pdf = Pdf::loadView('reports.berths-utilization-pdf', $data);
            return $pdf->download('تقرير-استخدام-الأرصفة-' . $startDate->format('Y-m-d') . '-إلى-' . $endDate->format('Y-m-d') . '.pdf');
        }

        return view('reports.berths-utilization', $data);
    }

    /**
     * تقرير المستودعات والتخزين
     */
    public function warehousesStorage(Request $request)
    {
        $dateFrom = $request->get('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', Carbon::now()->format('Y-m-d'));
        
        $startDate = Carbon::parse($dateFrom)->startOfDay();
        $endDate = Carbon::parse($dateTo)->endOfDay();

        // إحصائيات المستودعات
        $warehouses = \App\Models\Warehouse::with(['cargoStorages' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('storage_start', [$startDate, $endDate])
                  ->orWhereBetween('storage_end', [$startDate, $endDate])
                  ->orWhere(function($q) use ($startDate, $endDate) {
                      $q->where('storage_start', '<=', $startDate)
                        ->where(function($subQ) use ($endDate) {
                            $subQ->where('storage_end', '>=', $endDate)
                                 ->orWhereNull('storage_end');
                        });
                  });
        }])->get();

        $warehouseStats = $warehouses->map(function($warehouse) use ($startDate, $endDate) {
            $totalCapacity = $warehouse->max_capacity;
            $currentOccupancy = 0;
            $totalRevenue = 0;
            $storageCount = $warehouse->cargoStorages->count();

            foreach ($warehouse->cargoStorages as $storage) {
                $storageStart = Carbon::parse($storage->storage_start);
                $storageEnd = $storage->storage_end ? Carbon::parse($storage->storage_end) : Carbon::now();
                
                // حساب الإيرادات في الفترة المحددة
                $periodStart = $storageStart->max($startDate);
                $periodEnd = $storageEnd->min($endDate);
                
                if ($periodStart <= $periodEnd) {
                    $days = $periodStart->diffInDays($periodEnd) + 1;
                    $totalRevenue += $days * $storage->daily_rate;
                }

                // حساب الإشغال الحالي
                if (!$storage->storage_end || Carbon::parse($storage->storage_end)->isFuture()) {
                    $currentOccupancy += $storage->cargo->quantity ?? 0;
                }
            }

            $occupancyRate = $totalCapacity > 0 ? ($currentOccupancy / $totalCapacity) * 100 : 0;

            return [
                'warehouse' => $warehouse,
                'total_capacity' => $totalCapacity,
                'current_occupancy' => $currentOccupancy,
                'occupancy_rate' => round($occupancyRate, 2),
                'storage_count' => $storageCount,
                'total_revenue' => $totalRevenue,
                'available_space' => $totalCapacity - $currentOccupancy,
            ];
        });

        $data = [
            'date_from' => $startDate,
            'date_to' => $endDate,
            'warehouse_stats' => $warehouseStats,
            'summary' => [
                'total_warehouses' => $warehouses->count(),
                'total_capacity' => $warehouseStats->sum('total_capacity'),
                'total_occupancy' => $warehouseStats->sum('current_occupancy'),
                'average_occupancy_rate' => $warehouseStats->avg('occupancy_rate'),
                'total_revenue' => $warehouseStats->sum('total_revenue'),
                'total_available_space' => $warehouseStats->sum('available_space'),
            ]
        ];

        if ($request->get('format') === 'pdf') {
            $pdf = Pdf::loadView('reports.warehouses-storage-pdf', $data);
            return $pdf->download('تقرير-المستودعات-' . $startDate->format('Y-m-d') . '-إلى-' . $endDate->format('Y-m-d') . '.pdf');
        }

        return view('reports.warehouses-storage', $data);
    }

    /**
     * تقرير الوكلاء الملاحيين
     */
    public function agentsPerformance(Request $request)
    {
        $dateFrom = $request->get('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', Carbon::now()->format('Y-m-d'));
        
        $startDate = Carbon::parse($dateFrom)->startOfDay();
        $endDate = Carbon::parse($dateTo)->endOfDay();

        $agents = \App\Models\Agent::with([
            'ships' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('actual_arrival_date', [$startDate, $endDate]);
            },
            'invoices' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('invoice_date', [$startDate, $endDate]);
            }
        ])->get();

        $agentStats = $agents->map(function($agent) {
            return [
                'agent' => $agent,
                'ships_count' => $agent->ships->count(),
                'invoices_count' => $agent->invoices->count(),
                'total_amount' => $agent->invoices->sum('total_amount'),
                'paid_amount' => $agent->invoices->sum('paid_amount'),
                'pending_amount' => $agent->invoices->sum('total_amount') - $agent->invoices->sum('paid_amount'),
                'payment_rate' => $agent->invoices->sum('total_amount') > 0 ? 
                    ($agent->invoices->sum('paid_amount') / $agent->invoices->sum('total_amount')) * 100 : 0,
                'average_invoice_amount' => $agent->invoices->count() > 0 ? 
                    $agent->invoices->sum('total_amount') / $agent->invoices->count() : 0,
            ];
        })->sortByDesc('total_amount');

        $data = [
            'date_from' => $startDate,
            'date_to' => $endDate,
            'agent_stats' => $agentStats,
            'summary' => [
                'total_agents' => $agents->count(),
                'active_agents' => $agentStats->where('ships_count', '>', 0)->count(),
                'total_ships' => $agentStats->sum('ships_count'),
                'total_invoices' => $agentStats->sum('invoices_count'),
                'total_amount' => $agentStats->sum('total_amount'),
                'total_paid' => $agentStats->sum('paid_amount'),
                'total_pending' => $agentStats->sum('pending_amount'),
            ]
        ];

        if ($request->get('format') === 'pdf') {
            $pdf = Pdf::loadView('reports.agents-performance-pdf', $data);
            return $pdf->download('تقرير-أداء-الوكلاء-' . $startDate->format('Y-m-d') . '-إلى-' . $endDate->format('Y-m-d') . '.pdf');
        }

        return view('reports.agents-performance', $data);
    }

    /**
     * تصدير التقرير المالي إلى Excel
     */
    private function exportMonthlyFinancialExcel($data)
    {
        // سيتم تنفيذ هذه الوظيفة لاحقاً باستخدام Laravel Excel
        return response()->json(['message' => 'تصدير Excel سيتم تنفيذه قريباً'], 501);
    }
}
